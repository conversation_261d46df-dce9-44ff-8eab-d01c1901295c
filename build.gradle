buildscript {
    repositories {
        maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
    }
    dependencies {
        classpath('se.transmode.gradle:gradle-docker:1.2')
    }
}

plugins {
    id 'org.springframework.boot' version '2.3.3.RELEASE'
    id 'io.spring.dependency-management' version '1.0.10.RELEASE'
    id 'java'
    id 'groovy'
}

apply plugin: 'io.spring.dependency-management'
apply plugin: 'docker'

group = 'cn.abcyun.cis'
version = '0.0.1-SNAPSHOT'
sourceCompatibility = '1.8'

ext {
    set('springCloudVersion', "Hoxton.SR7")
    set('dockerApplication', 'abc-cis-crm-service')
    set('dockerRegistry', 'registry.cn-shanghai.aliyuncs.com')
    set('dockerGroup', 'byteflow')
    set('dockerVersion', 'latest')
    set('nexusSnapShotUrl', "https://packages.aliyun.com/maven/repository/105566-snapshot-k87VEs/")
    set('nexusReleaseUrl', 'https://packages.aliyun.com/maven/repository/105566-release-Sy2Ug0/')
    set('nexusUsername', 'ZLmZuu')
    set('nexusPassword', 'Nl4rmLzuy7')
}

repositories {
    mavenLocal()
    maven { url 'https://maven.aliyun.com/repository/public' }
    maven { url 'https://maven.aliyun.com/repository/gradle-plugin' }
    mavenCentral()
    jcenter()
    maven {
        credentials {
            username "${nexusUsername}"
            password "${nexusPassword}"
        }
        url "${nexusSnapShotUrl}"
    }

    maven {
        credentials {
            username "${nexusUsername}"
            password "${nexusPassword}"
        }
        url "${nexusReleaseUrl}"
    }
}

configurations.all {
    resolutionStrategy.cacheChangingModulesFor 0, 'seconds'
}

dependencies {
    implementation 'cn.abcyun.cis:abc-cis-id-generator:0.0.8'
    implementation 'cn.abcyun.cis:abc-cis-commons:2.1.5.21'
    implementation 'cn.abcyun.cis:abc-cis-core:0.2.08'
    implementation 'cn.abcyun.common:abc-common-log:0.0.7'
    implementation 'org.apache.logging.log4j:log4j-api:2.15.0'
    implementation 'org.apache.logging.log4j:log4j-to-slf4j:2.15.0'
    implementation 'cn.abcyun.bis:abc-bis-rpc-sdk:2.90.57'
    implementation 'cn.abcyun.common:abc-common-model:1.0.6'

    implementation 'org.springframework.cloud:spring-cloud-starter-config'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.cloud:spring-cloud-starter-netflix-hystrix'
    implementation 'org.springframework.cloud:spring-cloud-starter-sleuth'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-amqp'
    implementation('org.apache.rocketmq:rocketmq-spring-boot-starter:2.2.3') {
        exclude group: 'org.apache.tomcat', module: 'annotations-api'
    }
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-cache'

    implementation 'org.mybatis.spring.boot:mybatis-spring-boot-starter:2.0.0'
    implementation 'org.apache.commons:commons-lang3:3.4'
    implementation 'commons-lang:commons-lang:2.6'
    implementation 'commons-collections:commons-collections:3.2.2'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-json-org:2.9.0'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.12.0'

    annotationProcessor "org.springframework.boot:spring-boot-configuration-processor"

    implementation 'io.springfox:springfox-boot-starter:3.0.0'

    compile 'com.github.promeg:tinypinyin:2.0.3' // TinyPinyin核心包，约80KB
    compile 'com.github.promeg:tinypinyin-lexicons-java-cncity:2.0.3' // 可选，适用于Java的中国地区词典
    runtimeOnly 'mysql:mysql-connector-java'

    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'

    compile 'net.javacrumbs.shedlock:shedlock-spring:4.16.0'
    compile 'net.javacrumbs.shedlock:shedlock-provider-redis-spring:4.16.0'
    compile 'com.vladmihalcea:hibernate-types-5:2.4.2'
    implementation 'org.redisson:redisson:3.17.7'

    // excel 解析
    implementation 'com.alibaba:easyexcel:2.2.3'

    testImplementation 'org.springframework.boot:spring-boot-starter-test'

    compile 'com.google.protobuf:protobuf-java:2.5.0'
    compile 'com.aliyun.openservices:aliyun-log-logback-appender:0.1.19'
//    compile 'cn.hutool:hutool-all:5.7.7'

    implementation 'cn.hutool:hutool-all:5.8.20'

    // mapstruct
    implementation "org.mapstruct:mapstruct:1.5.2.Final"
    annotationProcessor 'org.mapstruct:mapstruct-processor:1.5.2.Final'
    // 处理 mapstruct 和 lombok 冲突问题
    annotationProcessor 'org.projectlombok:lombok-mapstruct-binding:0.2.0'
    implementation 'org.apache.commons:commons-pool2:2.8.0'

    implementation('org.apache.rocketmq:rocketmq-spring-boot-starter:2.2.3') {
        exclude group: 'org.apache.tomcat', module: 'annotations-api'
    }

    compile 'org.springframework.integration:spring-integration-redis:5.3.1.RELEASE'
    implementation 'cn.abcyun.scrm:abc-scrm-rpc-sdk:0.0.11'

    implementation 'com.github.ben-manes.caffeine:caffeine:2.8.0'

    implementation 'com.googlecode.libphonenumber:libphonenumber:8.13.27'
    implementation 'io.micrometer:micrometer-registry-prometheus'

    // 阿里云OSS
    implementation 'com.aliyun.oss:aliyun-sdk-oss:3.10.2'

    // 地址标准化
    implementation fileTree(dir: 'libs/geocoding', include: ['*.jar'])
    implementation 'org.jetbrains.kotlin:kotlin-stdlib:1.6.10'

//    implementation 'org.bitlap:geocoding:1.3.1'

    // mandatory dependencies for using Spock
    implementation 'org.codehaus.groovy:groovy:3.0.10'
    testImplementation platform("org.spockframework:spock-bom:2.1-groovy-3.0")
    testImplementation "org.spockframework:spock-core"

    testImplementation 'org.powermock:powermock-module-junit4:2.0.9'
    testImplementation 'org.powermock:powermock-api-mockito2:2.0.9'

    implementation 'com.belerweb:pinyin4j:2.5.1'
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

task buildUnpackJar(type: Copy) {
    dependsOn clean
    dependsOn bootJar
    tasks.findByName('bootJar').mustRunAfter('clean')

    from(zipTree(tasks.bootJar.outputs.files.singleFile))
    into("build/dependency")
}

task buildDocker(type: Docker) {
    dependsOn buildUnpackJar
    tag = "${dockerRegistry}/${dockerGroup}/${dockerApplication}"
    tagVersion = "${dockerVersion}"
    dockerfile = file('Dockerfile')

    doFirst {
        copy {
            from "build/dependency"
            into "${stageDir}/build/dependency"
        }
    }
}

task deployDocker(type: Exec) {
    dependsOn buildDocker
    commandLine "docker", "push", "${dockerRegistry}/${dockerGroup}/${dockerApplication}:${dockerVersion}"
}