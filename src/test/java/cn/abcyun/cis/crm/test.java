package cn.abcyun.cis.crm;

import cn.abcyun.cis.commons.model.AddressNode;
import cn.abcyun.cis.commons.model.CisPatientAge;
import cn.abcyun.cis.commons.util.AddressUtils;
import cn.abcyun.cis.commons.util.DateUtils;
import cn.abcyun.cis.commons.util.PinyinUtils;
import cn.abcyun.cis.commons.util.TextUtils;
import cn.abcyun.cis.crm.common.CommonUtil;
import cn.abcyun.cis.crm.common.JsonUtils;
import cn.abcyun.cis.crm.entity.QueryParam;
import cn.abcyun.cis.crm.patient.merge.PatientMergeTask;
import cn.abcyun.cis.crm.rpc.entity.PatientImportReq;
import cn.abcyun.cis.crm.service.PatientDeliveryService;
import cn.hutool.core.util.IdcardUtil;
import cn.hutool.extra.pinyin.PinyinUtil;
import com.github.promeg.pinyinhelper.Pinyin;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.FastDateFormat;
import org.junit.Test;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.zip.GZIPOutputStream;


public class test {

    @Test
    public void test1() {
        PatientMergeTask task = new PatientMergeTask();
        String str = JsonUtils.dump(task);
        System.out.println(str);
    }

    @Test
    public void test2() {

        //树结构
        String content = "{\"100000\":{\"110000\":\"北京\",\"120000\":\"天津\",\"130000\":\"河北\",\"140000\":\"山西\",\"150000\":\"内蒙古\",\"210000\":\"辽宁\",\"220000\":\"吉林\",\"230000\":\"黑龙江\",\"310000\":\"上海\",\"320000\":\"江苏\",\"330000\":\"浙江\",\"340000\":\"安徽\",\"350000\":\"福建\",\"360000\":\"江西\",\"370000\":\"山东\",\"410000\":\"河南\",\"420000\":\"湖北\",\"430000\":\"湖南\",\"440000\":\"广东\",\"450000\":\"广西\",\"460000\":\"海南\",\"500000\":\"重庆\",\"510000\":\"四川\",\"520000\":\"贵州\",\"530000\":\"云南\",\"540000\":\"西藏\",\"610000\":\"陕西\",\"620000\":\"甘肃\",\"630000\":\"青海\",\"640000\":\"宁夏\",\"650000\":\"新疆\",\"710000\":\"台湾\",\"810000\":\"香港\",\"820000\":\"澳门\"},\"110000\":{\"110100\":\"北京\"},\"110100\":{\"110101\":\"东城\",\"110102\":\"西城\",\"110105\":\"朝阳\",\"110106\":\"丰台\",\"110107\":\"石景山\",\"110108\":\"海淀\",\"110109\":\"门头沟\",\"110111\":\"房山\",\"110112\":\"通州\",\"110113\":\"顺义\",\"110114\":\"昌平\",\"110115\":\"大兴\",\"110116\":\"怀柔\",\"110117\":\"平谷\",\"110118\":\"密云\",\"110119\":\"延庆\"},\"120000\":{\"120100\":\"天津\"},\"120100\":{\"120101\":\"和平\",\"120102\":\"河东\",\"120103\":\"河西\",\"120104\":\"南开\",\"120105\":\"河北\",\"120106\":\"红桥\",\"120110\":\"东丽\",\"120111\":\"西青\",\"120112\":\"津南\",\"120113\":\"北辰\",\"120114\":\"武清\",\"120115\":\"宝坻\",\"120116\":\"滨海新\",\"120117\":\"宁河\",\"120118\":\"静海\",\"120119\":\"蓟州\"},\"130000\":{\"130100\":\"石家庄\",\"130200\":\"唐山\",\"130300\":\"秦皇岛\",\"130400\":\"邯郸\",\"130500\":\"邢台\",\"130600\":\"保定\",\"130700\":\"张家口\",\"130800\":\"承德\",\"130900\":\"沧州\",\"131000\":\"廊坊\",\"131100\":\"衡水\"},\"130100\":{\"130102\":\"长安\",\"130104\":\"桥西\",\"130105\":\"新华\",\"130107\":\"井陉矿\",\"130108\":\"裕华\",\"130109\":\"藁城\",\"130110\":\"鹿泉\",\"130111\":\"栾城\",\"130121\":\"井陉\",\"130123\":\"正定\",\"130125\":\"行唐\",\"130126\":\"灵寿\",\"130127\":\"高邑\",\"130128\":\"深泽\",\"130129\":\"赞皇\",\"130130\":\"无极\",\"130131\":\"平山\",\"130132\":\"元氏\",\"130133\":\"赵县\",\"130181\":\"辛集\",\"130183\":\"晋州\",\"130184\":\"新乐\"},\"130200\":{\"130202\":\"路南\",\"130203\":\"路北\",\"130204\":\"古冶\",\"130205\":\"开平\",\"130207\":\"丰南\",\"130208\":\"丰润\",\"130209\":\"曹妃甸\",\"130223\":\"滦县\",\"130224\":\"滦南\",\"130225\":\"乐亭\",\"130227\":\"迁西\",\"130229\":\"玉田\",\"130281\":\"遵化\",\"130283\":\"迁安\"},\"130300\":{\"130302\":\"海港\",\"130303\":\"山海关\",\"130304\":\"北戴河\",\"130306\":\"抚宁\",\"130321\":\"青龙\",\"130322\":\"昌黎\",\"130324\":\"卢龙\"},\"130400\":{\"130402\":\"邯山\",\"130403\":\"丛台\",\"130404\":\"复兴\",\"130406\":\"峰峰矿\",\"130423\":\"临漳\",\"130424\":\"成安\",\"130425\":\"大名\",\"130426\":\"涉县\",\"130427\":\"磁县\",\"130428\":\"肥乡\",\"130429\":\"永年\",\"130430\":\"邱县\",\"130431\":\"鸡泽\",\"130432\":\"广平\",\"130433\":\"馆陶\",\"130434\":\"魏县\",\"130435\":\"曲周\",\"130481\":\"武安\"},\"130500\":{\"130502\":\"桥东\",\"130503\":\"桥西\",\"130521\":\"邢台\",\"130522\":\"临城\",\"130523\":\"内丘\",\"130524\":\"柏乡\",\"130525\":\"隆尧\",\"130526\":\"任县\",\"130527\":\"南和\",\"130528\":\"宁晋\",\"130529\":\"巨鹿\",\"130530\":\"新河\",\"130531\":\"广宗\",\"130532\":\"平乡\",\"130533\":\"威县\",\"130534\":\"清河\",\"130535\":\"临西\",\"130581\":\"南宫\",\"130582\":\"沙河\"},\"130600\":{\"130602\":\"竞秀\",\"130606\":\"莲池\",\"130607\":\"满城\",\"130608\":\"清苑\",\"130609\":\"徐水\",\"130623\":\"涞水\",\"130624\":\"阜平\",\"130626\":\"定兴\",\"130627\":\"唐县\",\"130628\":\"高阳\",\"130629\":\"容城\",\"130630\":\"涞源\",\"130631\":\"望都\",\"130632\":\"安新\",\"130633\":\"易县\",\"130634\":\"曲阳\",\"130635\":\"蠡县\",\"130636\":\"顺平\",\"130637\":\"博野\",\"130638\":\"雄县\",\"130681\":\"涿州\",\"130682\":\"定州\",\"130683\":\"安国\",\"130684\":\"高碑店\"},\"130700\":{\"130702\":\"桥东\",\"130703\":\"桥西\",\"130705\":\"宣化\",\"130706\":\"下花园\",\"130708\":\"万全\",\"130709\":\"崇礼\",\"130722\":\"张北\",\"130723\":\"康保\",\"130724\":\"沽源\",\"130725\":\"尚义\",\"130726\":\"蔚县\",\"130727\":\"阳原\",\"130728\":\"怀安\",\"130730\":\"怀来\",\"130731\":\"涿鹿\",\"130732\":\"赤城\"},\"130800\":{\"130802\":\"双桥\",\"130803\":\"双滦\",\"130804\":\"鹰手营子矿\",\"130821\":\"承德\",\"130822\":\"兴隆\",\"130823\":\"平泉\",\"130824\":\"滦平\",\"130825\":\"隆化\",\"130826\":\"丰宁\",\"130827\":\"宽城\",\"130828\":\"围场\"},\"130900\":{\"130902\":\"新华\",\"130903\":\"运河\",\"130921\":\"沧县\",\"130922\":\"青县\",\"130923\":\"东光\",\"130924\":\"海兴\",\"130925\":\"盐山\",\"130926\":\"肃宁\",\"130927\":\"南皮\",\"130928\":\"吴桥\",\"130929\":\"献县\",\"130930\":\"孟村\",\"130981\":\"泊头\",\"130982\":\"任丘\",\"130983\":\"黄骅\",\"130984\":\"河间\"},\"131000\":{\"131002\":\"安次\",\"131003\":\"广阳\",\"131022\":\"固安\",\"131023\":\"永清\",\"131024\":\"香河\",\"131025\":\"大城\",\"131026\":\"文安\",\"131028\":\"大厂\",\"131081\":\"霸州\",\"131082\":\"三河\"},\"131100\":{\"131102\":\"桃城\",\"131103\":\"冀州\",\"131121\":\"枣强\",\"131122\":\"武邑\",\"131123\":\"武强\",\"131124\":\"饶阳\",\"131125\":\"安平\",\"131126\":\"故城\",\"131127\":\"景县\",\"131128\":\"阜城\",\"131182\":\"深州\"},\"140000\":{\"140100\":\"太原\",\"140200\":\"大同\",\"140300\":\"阳泉\",\"140400\":\"长治\",\"140500\":\"晋城\",\"140600\":\"朔州\",\"140700\":\"晋中\",\"140800\":\"运城\",\"140900\":\"忻州\",\"141000\":\"临汾\",\"141100\":\"吕梁\"},\"140100\":{\"140105\":\"小店\",\"140106\":\"迎泽\",\"140107\":\"杏花岭\",\"140108\":\"尖草坪\",\"140109\":\"万柏林\",\"140110\":\"晋源\",\"140121\":\"清徐\",\"140122\":\"阳曲\",\"140123\":\"娄烦\",\"140181\":\"古交\"},\"140200\":{\"140202\":\"城区\",\"140203\":\"矿区\",\"140211\":\"南郊\",\"140212\":\"新荣\",\"140221\":\"阳高\",\"140222\":\"天镇\",\"140223\":\"广灵\",\"140224\":\"灵丘\",\"140225\":\"浑源\",\"140226\":\"左云\",\"140227\":\"大同\"},\"140300\":{\"140302\":\"城区\",\"140303\":\"矿区\",\"140311\":\"郊区\",\"140321\":\"平定\",\"140322\":\"盂县\"},\"140400\":{\"140402\":\"城区\",\"140411\":\"郊区\",\"140421\":\"长治\",\"140423\":\"襄垣\",\"140424\":\"屯留\",\"140425\":\"平顺\",\"140426\":\"黎城\",\"140427\":\"壶关\",\"140428\":\"长子\",\"140429\":\"武乡\",\"140430\":\"沁县\",\"140431\":\"沁源\",\"140481\":\"潞城\"},\"140500\":{\"140502\":\"城区\",\"140521\":\"沁水\",\"140522\":\"阳城\",\"140524\":\"陵川\",\"140525\":\"泽州\",\"140581\":\"高平\"},\"140600\":{\"140602\":\"朔城\",\"140603\":\"平鲁\",\"140621\":\"山阴\",\"140622\":\"应县\",\"140623\":\"右玉\",\"140624\":\"怀仁\"},\"140700\":{\"140702\":\"榆次\",\"140721\":\"榆社\",\"140722\":\"左权\",\"140723\":\"和顺\",\"140724\":\"昔阳\",\"140725\":\"寿阳\",\"140726\":\"太谷\",\"140727\":\"祁县\",\"140728\":\"平遥\",\"140729\":\"灵石\",\"140781\":\"介休\"},\"140800\":{\"140802\":\"盐湖\",\"140821\":\"临猗\",\"140822\":\"万荣\",\"140823\":\"闻喜\",\"140824\":\"稷山\",\"140825\":\"新绛\",\"140826\":\"绛县\",\"140827\":\"垣曲\",\"140828\":\"夏县\",\"140829\":\"平陆\",\"140830\":\"芮城\",\"140881\":\"永济\",\"140882\":\"河津\"},\"140900\":{\"140902\":\"忻府\",\"140921\":\"定襄\",\"140922\":\"五台\",\"140923\":\"代县\",\"140924\":\"繁峙\",\"140925\":\"宁武\",\"140926\":\"静乐\",\"140927\":\"神池\",\"140928\":\"五寨\",\"140929\":\"岢岚\",\"140930\":\"河曲\",\"140931\":\"保德\",\"140932\":\"偏关\",\"140981\":\"原平\"},\"141000\":{\"141002\":\"尧都\",\"141021\":\"曲沃\",\"141022\":\"翼城\",\"141023\":\"襄汾\",\"141024\":\"洪洞\",\"141025\":\"古县\",\"141026\":\"安泽\",\"141027\":\"浮山\",\"141028\":\"吉县\",\"141029\":\"乡宁\",\"141030\":\"大宁\",\"141031\":\"隰县\",\"141032\":\"永和\",\"141033\":\"蒲县\",\"141034\":\"汾西\",\"141081\":\"侯马\",\"141082\":\"霍州\"},\"141100\":{\"141102\":\"离石\",\"141121\":\"文水\",\"141122\":\"交城\",\"141123\":\"兴县\",\"141124\":\"临县\",\"141125\":\"柳林\",\"141126\":\"石楼\",\"141127\":\"岚县\",\"141128\":\"方山\",\"141129\":\"中阳\",\"141130\":\"交口\",\"141181\":\"孝义\",\"141182\":\"汾阳\"},\"150000\":{\"150100\":\"呼和浩特\",\"150200\":\"包头\",\"150300\":\"乌海\",\"150400\":\"赤峰\",\"150500\":\"通辽\",\"150600\":\"鄂尔多斯\",\"150700\":\"呼伦贝尔\",\"150800\":\"巴彦淖尔\",\"150900\":\"乌兰察布\",\"152200\":\"兴安\",\"152500\":\"锡林郭勒\",\"152900\":\"阿拉善\"},\"150100\":{\"150102\":\"新城\",\"150103\":\"回民\",\"150104\":\"玉泉\",\"150105\":\"赛罕\",\"150121\":\"土默特左\",\"150122\":\"托克托\",\"150123\":\"和林格尔\",\"150124\":\"清水河\",\"150125\":\"武川\"},\"150200\":{\"150202\":\"东河\",\"150203\":\"昆都仑\",\"150204\":\"青山\",\"150205\":\"石拐\",\"150206\":\"白云鄂博矿\",\"150207\":\"九原\",\"150221\":\"土默特右\",\"150222\":\"固阳\",\"150223\":\"达尔罕茂明安联合\"},\"150300\":{\"150302\":\"海勃湾\",\"150303\":\"海南\",\"150304\":\"乌达\"},\"150400\":{\"150402\":\"红山\",\"150403\":\"元宝山\",\"150404\":\"松山\",\"150421\":\"阿鲁科尔沁\",\"150422\":\"巴林左\",\"150423\":\"巴林右\",\"150424\":\"林西\",\"150425\":\"克什克腾\",\"150426\":\"翁牛特\",\"150428\":\"喀喇沁\",\"150429\":\"宁城\",\"150430\":\"敖汉\"},\"150500\":{\"150502\":\"科尔沁\",\"150521\":\"科尔沁左翼中\",\"150522\":\"科尔沁左翼后\",\"150523\":\"开鲁\",\"150524\":\"库伦\",\"150525\":\"奈曼\",\"150526\":\"扎鲁特\",\"150581\":\"霍林郭勒\"},\"150600\":{\"150602\":\"东胜\",\"150603\":\"康巴什\",\"150621\":\"达拉特\",\"150622\":\"准格尔\",\"150623\":\"鄂托克前\",\"150624\":\"鄂托克\",\"150625\":\"杭锦\",\"150626\":\"乌审\",\"150627\":\"伊金霍洛\"},\"150700\":{\"150702\":\"海拉尔\",\"150703\":\"扎赉诺尔\",\"150721\":\"阿荣\",\"150722\":\"莫力达瓦达斡尔族\",\"150723\":\"鄂伦春\",\"150724\":\"鄂温克族\",\"150725\":\"陈巴尔虎\",\"150726\":\"新巴尔虎左\",\"150727\":\"新巴尔虎右\",\"150781\":\"满洲里\",\"150782\":\"牙克石\",\"150783\":\"扎兰屯\",\"150784\":\"额尔古纳\",\"150785\":\"根河\"},\"150800\":{\"150802\":\"临河\",\"150821\":\"五原\",\"150822\":\"磴口\",\"150823\":\"乌拉特前\",\"150824\":\"乌拉特中\",\"150825\":\"乌拉特后\",\"150826\":\"杭锦后\"},\"150900\":{\"150902\":\"集宁\",\"150921\":\"卓资\",\"150922\":\"化德\",\"150923\":\"商都\",\"150924\":\"兴和\",\"150925\":\"凉城\",\"150926\":\"察哈尔右翼前\",\"150927\":\"察哈尔右翼中\",\"150928\":\"察哈尔右翼后\",\"150929\":\"四子王\",\"150981\":\"丰镇\"},\"152200\":{\"152201\":\"乌兰浩特\",\"152202\":\"阿尔山\",\"152221\":\"科尔沁右翼前\",\"152222\":\"科尔沁右翼中\",\"152223\":\"扎赉特\",\"152224\":\"突泉\"},\"152500\":{\"152501\":\"二连浩特\",\"152502\":\"锡林浩特\",\"152522\":\"阿巴嘎\",\"152523\":\"苏尼特左\",\"152524\":\"苏尼特右\",\"152525\":\"东乌珠穆沁\",\"152526\":\"西乌珠穆沁\",\"152527\":\"太仆寺\",\"152528\":\"镶黄\",\"152529\":\"正镶白\",\"152530\":\"正蓝\",\"152531\":\"多伦\"},\"152900\":{\"152921\":\"阿拉善左\",\"152922\":\"阿拉善右\",\"152923\":\"额济纳\"},\"210000\":{\"210100\":\"沈阳\",\"210200\":\"大连\",\"210300\":\"鞍山\",\"210400\":\"抚顺\",\"210500\":\"本溪\",\"210600\":\"丹东\",\"210700\":\"锦州\",\"210800\":\"营口\",\"210900\":\"阜新\",\"211000\":\"辽阳\",\"211100\":\"盘锦\",\"211200\":\"铁岭\",\"211300\":\"朝阳\",\"211400\":\"葫芦岛\"},\"210100\":{\"210102\":\"和平\",\"210103\":\"沈河\",\"210104\":\"大东\",\"210105\":\"皇姑\",\"210106\":\"铁西\",\"210111\":\"苏家屯\",\"210112\":\"浑南\",\"210113\":\"沈北新\",\"210114\":\"于洪\",\"210115\":\"辽中\",\"210123\":\"康平\",\"210124\":\"法库\",\"210181\":\"新民\"},\"210200\":{\"210202\":\"中山\",\"210203\":\"西岗\",\"210204\":\"沙河口\",\"210211\":\"甘井子\",\"210212\":\"旅顺口\",\"210213\":\"金州\",\"210214\":\"普兰店\",\"210224\":\"长海\",\"210281\":\"瓦房店\",\"210283\":\"庄河\"},\"210300\":{\"210302\":\"铁东\",\"210303\":\"铁西\",\"210304\":\"立山\",\"210311\":\"千山\",\"210321\":\"台安\",\"210323\":\"岫岩\",\"210381\":\"海城\"},\"210400\":{\"210402\":\"新抚\",\"210403\":\"东洲\",\"210404\":\"望花\",\"210411\":\"顺城\",\"210421\":\"抚顺\",\"210422\":\"新宾\",\"210423\":\"清原\"},\"210500\":{\"210502\":\"平山\",\"210503\":\"溪湖\",\"210504\":\"明山\",\"210505\":\"南芬\",\"210521\":\"本溪\",\"210522\":\"桓仁\"},\"210600\":{\"210602\":\"元宝\",\"210603\":\"振兴\",\"210604\":\"振安\",\"210624\":\"宽甸\",\"210681\":\"东港\",\"210682\":\"凤城\"},\"210700\":{\"210702\":\"古塔\",\"210703\":\"凌河\",\"210711\":\"太和\",\"210726\":\"黑山\",\"210727\":\"义县\",\"210781\":\"凌海\",\"210782\":\"北镇\"},\"210800\":{\"210802\":\"站前\",\"210803\":\"西区\",\"210804\":\"鲅鱼圈\",\"210811\":\"老边\",\"210881\":\"盖州\",\"210882\":\"大石桥\"},\"210900\":{\"210902\":\"海州\",\"210903\":\"新邱\",\"210904\":\"太平\",\"210905\":\"清河门\",\"210911\":\"细河\",\"210921\":\"阜新\",\"210922\":\"彰武\"},\"211000\":{\"211002\":\"白塔\",\"211003\":\"文圣\",\"211004\":\"宏伟\",\"211005\":\"弓长岭\",\"211011\":\"太子河\",\"211021\":\"辽阳\",\"211081\":\"灯塔\"},\"211100\":{\"211102\":\"双台子\",\"211103\":\"兴隆台\",\"211104\":\"大洼\",\"211122\":\"盘山\"},\"211200\":{\"211202\":\"银州\",\"211204\":\"清河\",\"211221\":\"铁岭\",\"211223\":\"西丰\",\"211224\":\"昌图\",\"211281\":\"调兵山\",\"211282\":\"开原\"},\"211300\":{\"211302\":\"双塔\",\"211303\":\"龙城\",\"211321\":\"朝阳\",\"211322\":\"建平\",\"211324\":\"喀喇沁左翼\",\"211381\":\"北票\",\"211382\":\"凌源\"},\"211400\":{\"211402\":\"连山\",\"211403\":\"龙港\",\"211404\":\"南票\",\"211421\":\"绥中\",\"211422\":\"建昌\",\"211481\":\"兴城\"},\"220000\":{\"220100\":\"长春\",\"220200\":\"吉林\",\"220300\":\"四平\",\"220400\":\"辽源\",\"220500\":\"通化\",\"220600\":\"白山\",\"220700\":\"松原\",\"220800\":\"白城\",\"222400\":\"延边\"},\"220100\":{\"220102\":\"南关\",\"220103\":\"宽城\",\"220104\":\"朝阳\",\"220105\":\"二道\",\"220106\":\"绿园\",\"220112\":\"双阳\",\"220113\":\"九台\",\"220122\":\"农安\",\"220182\":\"榆树\",\"220183\":\"德惠\"},\"220200\":{\"220202\":\"昌邑\",\"220203\":\"龙潭\",\"220204\":\"船营\",\"220211\":\"丰满\",\"220221\":\"永吉\",\"220281\":\"蛟河\",\"220282\":\"桦甸\",\"220283\":\"舒兰\",\"220284\":\"磐石\"},\"220300\":{\"220302\":\"铁西\",\"220303\":\"铁东\",\"220322\":\"梨树\",\"220323\":\"伊通\",\"220381\":\"公主岭\",\"220382\":\"双辽\"},\"220400\":{\"220402\":\"龙山\",\"220403\":\"西安\",\"220421\":\"东丰\",\"220422\":\"东辽\"},\"220500\":{\"220502\":\"东昌\",\"220503\":\"二道江\",\"220521\":\"通化\",\"220523\":\"辉南\",\"220524\":\"柳河\",\"220581\":\"梅河口\",\"220582\":\"集安\"},\"220600\":{\"220602\":\"浑江\",\"220605\":\"江源\",\"220621\":\"抚松\",\"220622\":\"靖宇\",\"220623\":\"长白\",\"220681\":\"临江\"},\"220700\":{\"220702\":\"宁江\",\"220721\":\"前郭尔罗斯\",\"220722\":\"长岭\",\"220723\":\"乾安\",\"220781\":\"扶余\"},\"220800\":{\"220802\":\"洮北\",\"220821\":\"镇赉\",\"220822\":\"通榆\",\"220881\":\"洮南\",\"220882\":\"大安\"},\"222400\":{\"222401\":\"延吉\",\"222402\":\"图们\",\"222403\":\"敦化\",\"222404\":\"珲春\",\"222405\":\"龙井\",\"222406\":\"和龙\",\"222424\":\"汪清\",\"222426\":\"安图\"},\"230000\":{\"230100\":\"哈尔滨\",\"230200\":\"齐齐哈尔\",\"230300\":\"鸡西\",\"230400\":\"鹤岗\",\"230500\":\"双鸭山\",\"230600\":\"大庆\",\"230700\":\"伊春\",\"230800\":\"佳木斯\",\"230900\":\"七台河\",\"231000\":\"牡丹江\",\"231100\":\"黑河\",\"231200\":\"绥化\",\"232700\":\"大兴安岭\"},\"230100\":{\"230102\":\"道里\",\"230103\":\"南岗\",\"230104\":\"道外\",\"230108\":\"平房\",\"230109\":\"松北\",\"230110\":\"香坊\",\"230111\":\"呼兰\",\"230112\":\"阿城\",\"230113\":\"双城\",\"230123\":\"依兰\",\"230124\":\"方正\",\"230125\":\"宾县\",\"230126\":\"巴彦\",\"230127\":\"木兰\",\"230128\":\"通河\",\"230129\":\"延寿\",\"230183\":\"尚志\",\"230184\":\"五常\"},\"230200\":{\"230202\":\"龙沙\",\"230203\":\"建华\",\"230204\":\"铁锋\",\"230205\":\"昂昂溪\",\"230206\":\"富拉尔基\",\"230207\":\"碾子山\",\"230208\":\"梅里斯达斡尔族\",\"230221\":\"龙江\",\"230223\":\"依安\",\"230224\":\"泰来\",\"230225\":\"甘南\",\"230227\":\"富裕\",\"230229\":\"克山\",\"230230\":\"克东\",\"230231\":\"拜泉\",\"230281\":\"讷河\"},\"230300\":{\"230302\":\"鸡冠\",\"230303\":\"恒山\",\"230304\":\"滴道\",\"230305\":\"梨树\",\"230306\":\"城子河\",\"230307\":\"麻山\",\"230321\":\"鸡东\",\"230381\":\"虎林\",\"230382\":\"密山\"},\"230400\":{\"230402\":\"向阳\",\"230403\":\"工农\",\"230404\":\"南山\",\"230405\":\"兴安\",\"230406\":\"东山\",\"230407\":\"兴山\",\"230421\":\"萝北\",\"230422\":\"绥滨\"},\"230500\":{\"230502\":\"尖山\",\"230503\":\"岭东\",\"230505\":\"四方台\",\"230506\":\"宝山\",\"230521\":\"集贤\",\"230522\":\"友谊\",\"230523\":\"宝清\",\"230524\":\"饶河\"},\"230600\":{\"230602\":\"萨尔图\",\"230603\":\"龙凤\",\"230604\":\"让胡路\",\"230605\":\"红岗\",\"230606\":\"大同\",\"230621\":\"肇州\",\"230622\":\"肇源\",\"230623\":\"林甸\",\"230624\":\"杜尔伯特\"},\"230700\":{\"230702\":\"伊春\",\"230703\":\"南岔\",\"230704\":\"友好\",\"230705\":\"西林\",\"230706\":\"翠峦\",\"230707\":\"新青\",\"230708\":\"美溪\",\"230709\":\"金山屯\",\"230710\":\"五营\",\"230711\":\"乌马河\",\"230712\":\"汤旺河\",\"230713\":\"带岭\",\"230714\":\"乌伊岭\",\"230715\":\"红星\",\"230716\":\"上甘岭\",\"230722\":\"嘉荫\",\"230781\":\"铁力\"},\"230800\":{\"230803\":\"向阳\",\"230804\":\"前进\",\"230805\":\"东风\",\"230811\":\"郊区\",\"230822\":\"桦南\",\"230826\":\"桦川\",\"230828\":\"汤原\",\"230881\":\"同江\",\"230882\":\"富锦\",\"230883\":\"抚远\"},\"230900\":{\"230902\":\"新兴\",\"230903\":\"桃山\",\"230904\":\"茄子河\",\"230921\":\"勃利\"},\"231000\":{\"231002\":\"东安\",\"231003\":\"阳明\",\"231004\":\"爱民\",\"231005\":\"西安\",\"231025\":\"林口\",\"231081\":\"绥芬河\",\"231083\":\"海林\",\"231084\":\"宁安\",\"231085\":\"穆棱\",\"231086\":\"东宁\"},\"231100\":{\"231102\":\"爱辉\",\"231121\":\"嫩江\",\"231123\":\"逊克\",\"231124\":\"孙吴\",\"231181\":\"北安\",\"231182\":\"五大连池\"},\"231200\":{\"231202\":\"北林\",\"231221\":\"望奎\",\"231222\":\"兰西\",\"231223\":\"青冈\",\"231224\":\"庆安\",\"231225\":\"明水\",\"231226\":\"绥棱\",\"231281\":\"安达\",\"231282\":\"肇东\",\"231283\":\"海伦\"},\"232700\":{\"232701\":\"加格达奇\",\"232721\":\"呼玛\",\"232722\":\"塔河\",\"232723\":\"漠河\"},\"310000\":{\"310100\":\"上海\"},\"310100\":{\"310101\":\"黄浦\",\"310104\":\"徐汇\",\"310105\":\"长宁\",\"310106\":\"静安\",\"310107\":\"普陀\",\"310109\":\"虹口\",\"310110\":\"杨浦\",\"310112\":\"闵行\",\"310113\":\"宝山\",\"310114\":\"嘉定\",\"310115\":\"浦东新\",\"310116\":\"金山\",\"310117\":\"松江\",\"310118\":\"青浦\",\"310120\":\"奉贤\",\"310151\":\"崇明\"},\"320000\":{\"320100\":\"南京\",\"320200\":\"无锡\",\"320300\":\"徐州\",\"320400\":\"常州\",\"320500\":\"苏州\",\"320600\":\"南通\",\"320700\":\"连云港\",\"320800\":\"淮安\",\"320900\":\"盐城\",\"321000\":\"扬州\",\"321100\":\"镇江\",\"321200\":\"泰州\",\"321300\":\"宿迁\"},\"320100\":{\"320102\":\"玄武\",\"320104\":\"秦淮\",\"320105\":\"建邺\",\"320106\":\"鼓楼\",\"320111\":\"浦口\",\"320113\":\"栖霞\",\"320114\":\"雨花台\",\"320115\":\"江宁\",\"320116\":\"六合\",\"320117\":\"溧水\",\"320118\":\"高淳\"},\"320200\":{\"320205\":\"锡山\",\"320206\":\"惠山\",\"320211\":\"滨湖\",\"320213\":\"梁溪\",\"320214\":\"新吴\",\"320281\":\"江阴\",\"320282\":\"宜兴\"},\"320300\":{\"320302\":\"鼓楼\",\"320303\":\"云龙\",\"320305\":\"贾汪\",\"320311\":\"泉山\",\"320312\":\"铜山\",\"320321\":\"丰县\",\"320322\":\"沛县\",\"320324\":\"睢宁\",\"320381\":\"新沂\",\"320382\":\"邳州\"},\"320400\":{\"320402\":\"天宁\",\"320404\":\"钟楼\",\"320411\":\"新北\",\"320412\":\"武进\",\"320413\":\"金坛\",\"320481\":\"溧阳\"},\"320500\":{\"320505\":\"虎丘\",\"320506\":\"吴中\",\"320507\":\"相城\",\"320508\":\"姑苏\",\"320509\":\"吴江\",\"320581\":\"常熟\",\"320582\":\"张家港\",\"320583\":\"昆山\",\"320585\":\"太仓\"},\"320600\":{\"320602\":\"崇川\",\"320611\":\"港闸\",\"320612\":\"通州\",\"320621\":\"海安\",\"320623\":\"如东\",\"320681\":\"启东\",\"320682\":\"如皋\",\"320684\":\"海门\"},\"320700\":{\"320703\":\"连云\",\"320706\":\"海州\",\"320707\":\"赣榆\",\"320722\":\"东海\",\"320723\":\"灌云\",\"320724\":\"灌南\"},\"320800\":{\"320802\":\"清江浦\",\"320803\":\"淮安\",\"320804\":\"淮阴\",\"320813\":\"洪泽\",\"320826\":\"涟水\",\"320830\":\"盱眙\",\"320831\":\"金湖\"},\"320900\":{\"320902\":\"亭湖\",\"320903\":\"盐都\",\"320904\":\"大丰\",\"320921\":\"响水\",\"320922\":\"滨海\",\"320923\":\"阜宁\",\"320924\":\"射阳\",\"320925\":\"建湖\",\"320981\":\"东台\"},\"321000\":{\"321002\":\"广陵\",\"321003\":\"邗江\",\"321012\":\"江都\",\"321023\":\"宝应\",\"321081\":\"仪征\",\"321084\":\"高邮\"},\"321100\":{\"321102\":\"京口\",\"321111\":\"润州\",\"321112\":\"丹徒\",\"321181\":\"丹阳\",\"321182\":\"扬中\",\"321183\":\"句容\"},\"321200\":{\"321202\":\"海陵\",\"321203\":\"高港\",\"321204\":\"姜堰\",\"321281\":\"兴化\",\"321282\":\"靖江\",\"321283\":\"泰兴\"},\"321300\":{\"321302\":\"宿城\",\"321311\":\"宿豫\",\"321322\":\"沭阳\",\"321323\":\"泗阳\",\"321324\":\"泗洪\"},\"330000\":{\"330100\":\"杭州\",\"330200\":\"宁波\",\"330300\":\"温州\",\"330400\":\"嘉兴\",\"330500\":\"湖州\",\"330600\":\"绍兴\",\"330700\":\"金华\",\"330800\":\"衢州\",\"330900\":\"舟山\",\"331000\":\"台州\",\"331100\":\"丽水\"},\"330100\":{\"330102\":\"上城\",\"330103\":\"下城\",\"330104\":\"江干\",\"330105\":\"拱墅\",\"330106\":\"西湖\",\"330108\":\"滨江\",\"330109\":\"萧山\",\"330110\":\"余杭\",\"330111\":\"富阳\",\"330122\":\"桐庐\",\"330127\":\"淳安\",\"330182\":\"建德\",\"330185\":\"临安\"},\"330200\":{\"330203\":\"海曙\",\"330205\":\"江北\",\"330206\":\"北仑\",\"330211\":\"镇海\",\"330212\":\"鄞州\",\"330225\":\"象山\",\"330226\":\"宁海\",\"330281\":\"余姚\",\"330282\":\"慈溪\",\"330283\":\"奉化\"},\"330300\":{\"330302\":\"鹿城\",\"330303\":\"龙湾\",\"330304\":\"瓯海\",\"330305\":\"洞头\",\"330324\":\"永嘉\",\"330326\":\"平阳\",\"330327\":\"苍南\",\"330328\":\"文成\",\"330329\":\"泰顺\",\"330381\":\"瑞安\",\"330382\":\"乐清\"},\"330400\":{\"330402\":\"南湖\",\"330411\":\"秀洲\",\"330421\":\"嘉善\",\"330424\":\"海盐\",\"330481\":\"海宁\",\"330482\":\"平湖\",\"330483\":\"桐乡\"},\"330500\":{\"330502\":\"吴兴\",\"330503\":\"南浔\",\"330521\":\"德清\",\"330522\":\"长兴\",\"330523\":\"安吉\"},\"330600\":{\"330602\":\"越城\",\"330603\":\"柯桥\",\"330604\":\"上虞\",\"330624\":\"新昌\",\"330681\":\"诸暨\",\"330683\":\"嵊州\"},\"330700\":{\"330702\":\"婺城\",\"330703\":\"金东\",\"330723\":\"武义\",\"330726\":\"浦江\",\"330727\":\"磐安\",\"330781\":\"兰溪\",\"330782\":\"义乌\",\"330783\":\"东阳\",\"330784\":\"永康\"},\"330800\":{\"330802\":\"柯城\",\"330803\":\"衢江\",\"330822\":\"常山\",\"330824\":\"开化\",\"330825\":\"龙游\",\"330881\":\"江山\"},\"330900\":{\"330902\":\"定海\",\"330903\":\"普陀\",\"330921\":\"岱山\",\"330922\":\"嵊泗\"},\"331000\":{\"331002\":\"椒江\",\"331003\":\"黄岩\",\"331004\":\"路桥\",\"331021\":\"玉环\",\"331022\":\"三门\",\"331023\":\"天台\",\"331024\":\"仙居\",\"331081\":\"温岭\",\"331082\":\"临海\"},\"331100\":{\"331102\":\"莲都\",\"331121\":\"青田\",\"331122\":\"缙云\",\"331123\":\"遂昌\",\"331124\":\"松阳\",\"331125\":\"云和\",\"331126\":\"庆元\",\"331127\":\"景宁\",\"331181\":\"龙泉\"},\"340000\":{\"340100\":\"合肥\",\"340200\":\"芜湖\",\"340300\":\"蚌埠\",\"340400\":\"淮南\",\"340500\":\"马鞍山\",\"340600\":\"淮北\",\"340700\":\"铜陵\",\"340800\":\"安庆\",\"341000\":\"黄山\",\"341100\":\"滁州\",\"341200\":\"阜阳\",\"341300\":\"宿州\",\"341500\":\"六安\",\"341600\":\"亳州\",\"341700\":\"池州\",\"341800\":\"宣城\"},\"340100\":{\"340102\":\"瑶海\",\"340103\":\"庐阳\",\"340104\":\"蜀山\",\"340111\":\"包河\",\"340121\":\"长丰\",\"340122\":\"肥东\",\"340123\":\"肥西\",\"340124\":\"庐江\",\"340181\":\"巢湖\"},\"340200\":{\"340202\":\"镜湖\",\"340203\":\"弋江\",\"340207\":\"鸠江\",\"340208\":\"三山\",\"340221\":\"芜湖\",\"340222\":\"繁昌\",\"340223\":\"南陵\",\"340225\":\"无为\"},\"340300\":{\"340302\":\"龙子湖\",\"340303\":\"蚌山\",\"340304\":\"禹会\",\"340311\":\"淮上\",\"340321\":\"怀远\",\"340322\":\"五河\",\"340323\":\"固镇\"},\"340400\":{\"340402\":\"大通\",\"340403\":\"田家庵\",\"340404\":\"谢家集\",\"340405\":\"八公山\",\"340406\":\"潘集\",\"340421\":\"凤台\",\"340422\":\"寿县\"},\"340500\":{\"340503\":\"花山\",\"340504\":\"雨山\",\"340506\":\"博望\",\"340521\":\"当涂\",\"340522\":\"含山\",\"340523\":\"和县\"},\"340600\":{\"340602\":\"杜集\",\"340603\":\"相山\",\"340604\":\"烈山\",\"340621\":\"濉溪\"},\"340700\":{\"340705\":\"铜官\",\"340706\":\"义安\",\"340711\":\"郊区\",\"340722\":\"枞阳\"},\"340800\":{\"340802\":\"迎江\",\"340803\":\"大观\",\"340811\":\"宜秀\",\"340822\":\"怀宁\",\"340824\":\"潜山\",\"340825\":\"太湖\",\"340826\":\"宿松\",\"340827\":\"望江\",\"340828\":\"岳西\",\"340881\":\"桐城\"},\"341000\":{\"341002\":\"屯溪\",\"341003\":\"黄山\",\"341004\":\"徽州\",\"341021\":\"歙县\",\"341022\":\"休宁\",\"341023\":\"黟县\",\"341024\":\"祁门\"},\"341100\":{\"341102\":\"琅琊\",\"341103\":\"南谯\",\"341122\":\"来安\",\"341124\":\"全椒\",\"341125\":\"定远\",\"341126\":\"凤阳\",\"341181\":\"天长\",\"341182\":\"明光\"},\"341200\":{\"341202\":\"颍州\",\"341203\":\"颍东\",\"341204\":\"颍泉\",\"341221\":\"临泉\",\"341222\":\"太和\",\"341225\":\"阜南\",\"341226\":\"颍上\",\"341282\":\"界首\"},\"341300\":{\"341302\":\"埇桥\",\"341321\":\"砀山\",\"341322\":\"萧县\",\"341323\":\"灵璧\",\"341324\":\"泗县\"},\"341500\":{\"341502\":\"金安\",\"341503\":\"裕安\",\"341504\":\"叶集\",\"341522\":\"霍邱\",\"341523\":\"舒城\",\"341524\":\"金寨\",\"341525\":\"霍山\"},\"341600\":{\"341602\":\"谯城\",\"341621\":\"涡阳\",\"341622\":\"蒙城\",\"341623\":\"利辛\"},\"341700\":{\"341702\":\"贵池\",\"341721\":\"东至\",\"341722\":\"石台\",\"341723\":\"青阳\"},\"341800\":{\"341802\":\"宣州\",\"341821\":\"郎溪\",\"341822\":\"广德\",\"341823\":\"泾县\",\"341824\":\"绩溪\",\"341825\":\"旌德\",\"341881\":\"宁国\"},\"350000\":{\"350100\":\"福州\",\"350200\":\"厦门\",\"350300\":\"莆田\",\"350400\":\"三明\",\"350500\":\"泉州\",\"350600\":\"漳州\",\"350700\":\"南平\",\"350800\":\"龙岩\",\"350900\":\"宁德\"},\"350100\":{\"350102\":\"鼓楼\",\"350103\":\"台江\",\"350104\":\"仓山\",\"350105\":\"马尾\",\"350111\":\"晋安\",\"350121\":\"闽侯\",\"350122\":\"连江\",\"350123\":\"罗源\",\"350124\":\"闽清\",\"350125\":\"永泰\",\"350128\":\"平潭\",\"350181\":\"福清\",\"350182\":\"长乐\"},\"350200\":{\"350203\":\"思明\",\"350205\":\"海沧\",\"350206\":\"湖里\",\"350211\":\"集美\",\"350212\":\"同安\",\"350213\":\"翔安\"},\"350300\":{\"350302\":\"城厢\",\"350303\":\"涵江\",\"350304\":\"荔城\",\"350305\":\"秀屿\",\"350322\":\"仙游\"},\"350400\":{\"350402\":\"梅列\",\"350403\":\"三元\",\"350421\":\"明溪\",\"350423\":\"清流\",\"350424\":\"宁化\",\"350425\":\"大田\",\"350426\":\"尤溪\",\"350427\":\"沙县\",\"350428\":\"将乐\",\"350429\":\"泰宁\",\"350430\":\"建宁\",\"350481\":\"永安\"},\"350500\":{\"350502\":\"鲤城\",\"350503\":\"丰泽\",\"350504\":\"洛江\",\"350505\":\"泉港\",\"350521\":\"惠安\",\"350524\":\"安溪\",\"350525\":\"永春\",\"350526\":\"德化\",\"350527\":\"金门\",\"350581\":\"石狮\",\"350582\":\"晋江\",\"350583\":\"南安\"},\"350600\":{\"350602\":\"芗城\",\"350603\":\"龙文\",\"350622\":\"云霄\",\"350623\":\"漳浦\",\"350624\":\"诏安\",\"350625\":\"长泰\",\"350626\":\"东山\",\"350627\":\"南靖\",\"350628\":\"平和\",\"350629\":\"华安\",\"350681\":\"龙海\"},\"350700\":{\"350702\":\"延平\",\"350703\":\"建阳\",\"350721\":\"顺昌\",\"350722\":\"浦城\",\"350723\":\"光泽\",\"350724\":\"松溪\",\"350725\":\"政和\",\"350781\":\"邵武\",\"350782\":\"武夷山\",\"350783\":\"建瓯\"},\"350800\":{\"350802\":\"新罗\",\"350803\":\"永定\",\"350821\":\"长汀\",\"350823\":\"上杭\",\"350824\":\"武平\",\"350825\":\"连城\",\"350881\":\"漳平\"},\"350900\":{\"350902\":\"蕉城\",\"350921\":\"霞浦\",\"350922\":\"古田\",\"350923\":\"屏南\",\"350924\":\"寿宁\",\"350925\":\"周宁\",\"350926\":\"柘荣\",\"350981\":\"福安\",\"350982\":\"福鼎\"},\"360000\":{\"360100\":\"南昌\",\"360200\":\"景德镇\",\"360300\":\"萍乡\",\"360400\":\"九江\",\"360500\":\"新余\",\"360600\":\"鹰潭\",\"360700\":\"赣州\",\"360800\":\"吉安\",\"360900\":\"宜春\",\"361000\":\"抚州\",\"361100\":\"上饶\"},\"360100\":{\"360102\":\"东湖\",\"360103\":\"西湖\",\"360104\":\"青云谱\",\"360105\":\"湾里\",\"360111\":\"青山湖\",\"360112\":\"新建\",\"360121\":\"南昌\",\"360123\":\"安义\",\"360124\":\"进贤\"},\"360200\":{\"360202\":\"昌江\",\"360203\":\"珠山\",\"360222\":\"浮梁\",\"360281\":\"乐平\"},\"360300\":{\"360302\":\"安源\",\"360313\":\"湘东\",\"360321\":\"莲花\",\"360322\":\"上栗\",\"360323\":\"芦溪\"},\"360400\":{\"360402\":\"濂溪\",\"360403\":\"浔阳\",\"360421\":\"九江\",\"360423\":\"武宁\",\"360424\":\"修水\",\"360425\":\"永修\",\"360426\":\"德安\",\"360427\":\"庐山\",\"360428\":\"都昌\",\"360429\":\"湖口\",\"360430\":\"彭泽\",\"360481\":\"瑞昌\",\"360482\":\"共青城\"},\"360500\":{\"360502\":\"渝水\",\"360521\":\"分宜\"},\"360600\":{\"360602\":\"月湖\",\"360622\":\"余江\",\"360681\":\"贵溪\"},\"360700\":{\"360702\":\"章贡\",\"360703\":\"南康\",\"360721\":\"赣县\",\"360722\":\"信丰\",\"360723\":\"大余\",\"360724\":\"上犹\",\"360725\":\"崇义\",\"360726\":\"安远\",\"360727\":\"龙南\",\"360728\":\"定南\",\"360729\":\"全南\",\"360730\":\"宁都\",\"360731\":\"于都\",\"360732\":\"兴国\",\"360733\":\"会昌\",\"360734\":\"寻乌\",\"360735\":\"石城\",\"360781\":\"瑞金\"},\"360800\":{\"360802\":\"吉州\",\"360803\":\"青原\",\"360821\":\"吉安\",\"360822\":\"吉水\",\"360823\":\"峡江\",\"360824\":\"新干\",\"360825\":\"永丰\",\"360826\":\"泰和\",\"360827\":\"遂川\",\"360828\":\"万安\",\"360829\":\"安福\",\"360830\":\"永新\",\"360881\":\"井冈山\"},\"360900\":{\"360902\":\"袁州\",\"360921\":\"奉新\",\"360922\":\"万载\",\"360923\":\"上高\",\"360924\":\"宜丰\",\"360925\":\"靖安\",\"360926\":\"铜鼓\",\"360981\":\"丰城\",\"360982\":\"樟树\",\"360983\":\"高安\"},\"361000\":{\"361002\":\"临川\",\"361021\":\"南城\",\"361022\":\"黎川\",\"361023\":\"南丰\",\"361024\":\"崇仁\",\"361025\":\"乐安\",\"361026\":\"宜黄\",\"361027\":\"金溪\",\"361028\":\"资溪\",\"361029\":\"东乡\",\"361030\":\"广昌\"},\"361100\":{\"361102\":\"信州\",\"361103\":\"广丰\",\"361121\":\"上饶\",\"361123\":\"玉山\",\"361124\":\"铅山\",\"361125\":\"横峰\",\"361126\":\"弋阳\",\"361127\":\"余干\",\"361128\":\"鄱阳\",\"361129\":\"万年\",\"361130\":\"婺源\",\"361181\":\"德兴\"},\"370000\":{\"370100\":\"济南\",\"370200\":\"青岛\",\"370300\":\"淄博\",\"370400\":\"枣庄\",\"370500\":\"东营\",\"370600\":\"烟台\",\"370700\":\"潍坊\",\"370800\":\"济宁\",\"370900\":\"泰安\",\"371000\":\"威海\",\"371100\":\"日照\",\"371200\":\"莱芜\",\"371300\":\"临沂\",\"371400\":\"德州\",\"371500\":\"聊城\",\"371600\":\"滨州\",\"371700\":\"菏泽\"},\"370100\":{\"370102\":\"历下\",\"370103\":\"中区\",\"370104\":\"槐荫\",\"370105\":\"天桥\",\"370112\":\"历城\",\"370113\":\"长清\",\"370124\":\"平阴\",\"370125\":\"济阳\",\"370126\":\"商河\",\"370181\":\"章丘\"},\"370200\":{\"370202\":\"南区\",\"370203\":\"北区\",\"370211\":\"黄岛\",\"370212\":\"崂山\",\"370213\":\"李沧\",\"370214\":\"城阳\",\"370281\":\"胶州\",\"370282\":\"即墨\",\"370283\":\"平度\",\"370285\":\"莱西\"},\"370300\":{\"370302\":\"淄川\",\"370303\":\"张店\",\"370304\":\"博山\",\"370305\":\"临淄\",\"370306\":\"周村\",\"370321\":\"桓台\",\"370322\":\"高青\",\"370323\":\"沂源\"},\"370400\":{\"370402\":\"中区\",\"370403\":\"薛城\",\"370404\":\"峄城\",\"370405\":\"台儿庄\",\"370406\":\"山亭\",\"370481\":\"滕州\"},\"370500\":{\"370502\":\"东营\",\"370503\":\"河口\",\"370505\":\"垦利\",\"370522\":\"利津\",\"370523\":\"广饶\"},\"370600\":{\"370602\":\"芝罘\",\"370611\":\"福山\",\"370612\":\"牟平\",\"370613\":\"莱山\",\"370634\":\"长岛\",\"370681\":\"龙口\",\"370682\":\"莱阳\",\"370683\":\"莱州\",\"370684\":\"蓬莱\",\"370685\":\"招远\",\"370686\":\"栖霞\",\"370687\":\"海阳\"},\"370700\":{\"370702\":\"潍城\",\"370703\":\"寒亭\",\"370704\":\"坊子\",\"370705\":\"奎文\",\"370724\":\"临朐\",\"370725\":\"昌乐\",\"370781\":\"青州\",\"370782\":\"诸城\",\"370783\":\"寿光\",\"370784\":\"安丘\",\"370785\":\"高密\",\"370786\":\"昌邑\"},\"370800\":{\"370811\":\"任城\",\"370812\":\"兖州\",\"370826\":\"微山\",\"370827\":\"鱼台\",\"370828\":\"金乡\",\"370829\":\"嘉祥\",\"370830\":\"汶上\",\"370831\":\"泗水\",\"370832\":\"梁山\",\"370881\":\"曲阜\",\"370883\":\"邹城\"},\"370900\":{\"370902\":\"泰山\",\"370911\":\"岱岳\",\"370921\":\"宁阳\",\"370923\":\"东平\",\"370982\":\"新泰\",\"370983\":\"肥城\"},\"371000\":{\"371002\":\"环翠\",\"371003\":\"文登\",\"371082\":\"荣成\",\"371083\":\"乳山\"},\"371100\":{\"371102\":\"东港\",\"371103\":\"岚山\",\"371121\":\"五莲\",\"371122\":\"莒县\"},\"371200\":{\"371202\":\"莱城\",\"371203\":\"钢城\"},\"371300\":{\"371302\":\"兰山\",\"371311\":\"罗庄\",\"371312\":\"河东\",\"371321\":\"沂南\",\"371322\":\"郯城\",\"371323\":\"沂水\",\"371324\":\"兰陵\",\"371325\":\"费县\",\"371326\":\"平邑\",\"371327\":\"莒南\",\"371328\":\"蒙阴\",\"371329\":\"临沭\"},\"371400\":{\"371402\":\"德城\",\"371403\":\"陵城\",\"371422\":\"宁津\",\"371423\":\"庆云\",\"371424\":\"临邑\",\"371425\":\"齐河\",\"371426\":\"平原\",\"371427\":\"夏津\",\"371428\":\"武城\",\"371481\":\"乐陵\",\"371482\":\"禹城\"},\"371500\":{\"371502\":\"东昌府\",\"371521\":\"阳谷\",\"371522\":\"莘县\",\"371523\":\"茌平\",\"371524\":\"东阿\",\"371525\":\"冠县\",\"371526\":\"高唐\",\"371581\":\"临清\"},\"371600\":{\"371602\":\"滨城\",\"371603\":\"沾化\",\"371621\":\"惠民\",\"371622\":\"阳信\",\"371623\":\"无棣\",\"371625\":\"博兴\",\"371626\":\"邹平\"},\"371700\":{\"371702\":\"牡丹\",\"371703\":\"定陶\",\"371721\":\"曹县\",\"371722\":\"单县\",\"371723\":\"成武\",\"371724\":\"巨野\",\"371725\":\"郓城\",\"371726\":\"鄄城\",\"371728\":\"东明\"},\"410000\":{\"410100\":\"郑州\",\"410200\":\"开封\",\"410300\":\"洛阳\",\"410400\":\"平顶山\",\"410500\":\"安阳\",\"410600\":\"鹤壁\",\"410700\":\"新乡\",\"410800\":\"焦作\",\"410900\":\"濮阳\",\"411000\":\"许昌\",\"411100\":\"漯河\",\"411200\":\"三门峡\",\"411300\":\"南阳\",\"411400\":\"商丘\",\"411500\":\"信阳\",\"411600\":\"周口\",\"411700\":\"驻马店\",\"419001\":\"济源\"},\"410100\":{\"410102\":\"中原\",\"410103\":\"二七\",\"410104\":\"管城\",\"410105\":\"金水\",\"410106\":\"上街\",\"410108\":\"惠济\",\"410122\":\"中牟\",\"410181\":\"巩义\",\"410182\":\"荥阳\",\"410183\":\"新密\",\"410184\":\"新郑\",\"410185\":\"登封\"},\"410200\":{\"410202\":\"龙亭\",\"410203\":\"顺河\",\"410204\":\"鼓楼\",\"410205\":\"禹王台\",\"410212\":\"祥符\",\"410221\":\"杞县\",\"410222\":\"通许\",\"410223\":\"尉氏\",\"410225\":\"兰考\"},\"410300\":{\"410302\":\"老城\",\"410303\":\"西工\",\"410304\":\"瀍河\",\"410305\":\"涧西\",\"410306\":\"吉利\",\"410311\":\"洛龙\",\"410322\":\"孟津\",\"410323\":\"新安\",\"410324\":\"栾川\",\"410325\":\"嵩县\",\"410326\":\"汝阳\",\"410327\":\"宜阳\",\"410328\":\"洛宁\",\"410329\":\"伊川\",\"410381\":\"偃师\"},\"410400\":{\"410402\":\"新华\",\"410403\":\"卫东\",\"410404\":\"石龙\",\"410411\":\"湛河\",\"410421\":\"宝丰\",\"410422\":\"叶县\",\"410423\":\"鲁山\",\"410425\":\"郏县\",\"410481\":\"舞钢\",\"410482\":\"汝州\"},\"410500\":{\"410502\":\"文峰\",\"410503\":\"北关\",\"410505\":\"殷都\",\"410506\":\"龙安\",\"410522\":\"安阳\",\"410523\":\"汤阴\",\"410526\":\"滑县\",\"410527\":\"内黄\",\"410581\":\"林州\"},\"410600\":{\"410602\":\"鹤山\",\"410603\":\"山城\",\"410611\":\"淇滨\",\"410621\":\"浚县\",\"410622\":\"淇县\"},\"410700\":{\"410702\":\"红旗\",\"410703\":\"卫滨\",\"410704\":\"凤泉\",\"410711\":\"牧野\",\"410721\":\"新乡\",\"410724\":\"获嘉\",\"410725\":\"原阳\",\"410726\":\"延津\",\"410727\":\"封丘\",\"410728\":\"长垣\",\"410781\":\"卫辉\",\"410782\":\"辉县\"},\"410800\":{\"410802\":\"解放\",\"410803\":\"中站\",\"410804\":\"马村\",\"410811\":\"山阳\",\"410821\":\"修武\",\"410822\":\"博爱\",\"410823\":\"武陟\",\"410825\":\"温县\",\"410882\":\"沁阳\",\"410883\":\"孟州\"},\"410900\":{\"410902\":\"华龙\",\"410922\":\"清丰\",\"410923\":\"南乐\",\"410926\":\"范县\",\"410927\":\"台前\",\"410928\":\"濮阳\"},\"411000\":{\"411002\":\"魏都\",\"411023\":\"许昌\",\"411024\":\"鄢陵\",\"411025\":\"襄城\",\"411081\":\"禹州\",\"411082\":\"长葛\"},\"411100\":{\"411102\":\"源汇\",\"411103\":\"郾城\",\"411104\":\"召陵\",\"411121\":\"舞阳\",\"411122\":\"临颍\"},\"411200\":{\"411202\":\"湖滨\",\"411203\":\"陕州\",\"411221\":\"渑池\",\"411224\":\"卢氏\",\"411281\":\"义马\",\"411282\":\"灵宝\"},\"411300\":{\"411302\":\"宛城\",\"411303\":\"卧龙\",\"411321\":\"南召\",\"411322\":\"方城\",\"411323\":\"西峡\",\"411324\":\"镇平\",\"411325\":\"内乡\",\"411326\":\"淅川\",\"411327\":\"社旗\",\"411328\":\"唐河\",\"411329\":\"新野\",\"411330\":\"桐柏\",\"411381\":\"邓州\"},\"411400\":{\"411402\":\"梁园\",\"411403\":\"睢阳\",\"411421\":\"民权\",\"411422\":\"睢县\",\"411423\":\"宁陵\",\"411424\":\"柘城\",\"411425\":\"虞城\",\"411426\":\"夏邑\",\"411481\":\"永城\"},\"411500\":{\"411502\":\"浉河\",\"411503\":\"平桥\",\"411521\":\"罗山\",\"411522\":\"光山\",\"411523\":\"新县\",\"411524\":\"商城\",\"411525\":\"固始\",\"411526\":\"潢川\",\"411527\":\"淮滨\",\"411528\":\"息县\"},\"411600\":{\"411602\":\"川汇\",\"411621\":\"扶沟\",\"411622\":\"西华\",\"411623\":\"商水\",\"411624\":\"沈丘\",\"411625\":\"郸城\",\"411626\":\"淮阳\",\"411627\":\"太康\",\"411628\":\"鹿邑\",\"411681\":\"项城\"},\"411700\":{\"411702\":\"驿城\",\"411721\":\"西平\",\"411722\":\"上蔡\",\"411723\":\"平舆\",\"411724\":\"正阳\",\"411725\":\"确山\",\"411726\":\"泌阳\",\"411727\":\"汝南\",\"411728\":\"遂平\",\"411729\":\"新蔡\"},\"420000\":{\"420100\":\"武汉\",\"420200\":\"黄石\",\"420300\":\"十堰\",\"420500\":\"宜昌\",\"420600\":\"襄阳\",\"420700\":\"鄂州\",\"420800\":\"荆门\",\"420900\":\"孝感\",\"421000\":\"荆州\",\"421100\":\"黄冈\",\"421200\":\"咸宁\",\"421300\":\"随州\",\"422800\":\"恩施\",\"429004\":\"仙桃\",\"429005\":\"潜江\",\"429006\":\"天门\",\"429021\":\"神农架林\"},\"420100\":{\"420102\":\"江岸\",\"420103\":\"江汉\",\"420104\":\"硚口\",\"420105\":\"汉阳\",\"420106\":\"武昌\",\"420107\":\"青山\",\"420111\":\"洪山\",\"420112\":\"东西湖\",\"420113\":\"汉南\",\"420114\":\"蔡甸\",\"420115\":\"江夏\",\"420116\":\"黄陂\",\"420117\":\"新洲\"},\"420200\":{\"420202\":\"黄石港\",\"420203\":\"西塞山\",\"420204\":\"下陆\",\"420205\":\"铁山\",\"420222\":\"阳新\",\"420281\":\"大冶\"},\"420300\":{\"420302\":\"茅箭\",\"420303\":\"张湾\",\"420304\":\"郧阳\",\"420322\":\"郧西\",\"420323\":\"竹山\",\"420324\":\"竹溪\",\"420325\":\"房县\",\"420381\":\"丹江口\"},\"420500\":{\"420502\":\"西陵\",\"420503\":\"伍家岗\",\"420504\":\"点军\",\"420505\":\"猇亭\",\"420506\":\"夷陵\",\"420525\":\"远安\",\"420526\":\"兴山\",\"420527\":\"秭归\",\"420528\":\"长阳\",\"420529\":\"五峰\",\"420581\":\"宜都\",\"420582\":\"当阳\",\"420583\":\"枝江\"},\"420600\":{\"420602\":\"襄城\",\"420606\":\"樊城\",\"420607\":\"襄州\",\"420624\":\"南漳\",\"420625\":\"谷城\",\"420626\":\"保康\",\"420682\":\"老河口\",\"420683\":\"枣阳\",\"420684\":\"宜城\"},\"420700\":{\"420702\":\"梁子湖\",\"420703\":\"华容\",\"420704\":\"鄂城\"},\"420800\":{\"420802\":\"东宝\",\"420804\":\"掇刀\",\"420821\":\"京山\",\"420822\":\"沙洋\",\"420881\":\"钟祥\"},\"420900\":{\"420902\":\"孝南\",\"420921\":\"孝昌\",\"420922\":\"大悟\",\"420923\":\"云梦\",\"420981\":\"应城\",\"420982\":\"安陆\",\"420984\":\"汉川\"},\"421000\":{\"421002\":\"沙区\",\"421003\":\"荆州\",\"421022\":\"公安\",\"421023\":\"监利\",\"421024\":\"江陵\",\"421081\":\"石首\",\"421083\":\"洪湖\",\"421087\":\"松滋\"},\"421100\":{\"421102\":\"黄州\",\"421121\":\"团风\",\"421122\":\"红安\",\"421123\":\"罗田\",\"421124\":\"英山\",\"421125\":\"浠水\",\"421126\":\"蕲春\",\"421127\":\"黄梅\",\"421181\":\"麻城\",\"421182\":\"武穴\"},\"421200\":{\"421202\":\"咸安\",\"421221\":\"嘉鱼\",\"421222\":\"通城\",\"421223\":\"崇阳\",\"421224\":\"通山\",\"421281\":\"赤壁\"},\"421300\":{\"421303\":\"曾都\",\"421321\":\"随县\",\"421381\":\"广水\"},\"422800\":{\"422801\":\"恩施\",\"422802\":\"利川\",\"422822\":\"建始\",\"422823\":\"巴东\",\"422825\":\"宣恩\",\"422826\":\"咸丰\",\"422827\":\"来凤\",\"422828\":\"鹤峰\"},\"430000\":{\"430100\":\"长沙\",\"430200\":\"株洲\",\"430300\":\"湘潭\",\"430400\":\"衡阳\",\"430500\":\"邵阳\",\"430600\":\"岳阳\",\"430700\":\"常德\",\"430800\":\"张家界\",\"430900\":\"益阳\",\"431000\":\"郴州\",\"431100\":\"永州\",\"431200\":\"怀化\",\"431300\":\"娄底\",\"433100\":\"湘西\"},\"430100\":{\"430102\":\"芙蓉\",\"430103\":\"天心\",\"430104\":\"岳麓\",\"430105\":\"开福\",\"430111\":\"雨花\",\"430112\":\"望城\",\"430121\":\"长沙\",\"430124\":\"宁乡\",\"430181\":\"浏阳\"},\"430200\":{\"430202\":\"荷塘\",\"430203\":\"芦淞\",\"430204\":\"石峰\",\"430211\":\"天元\",\"430221\":\"株洲\",\"430223\":\"攸县\",\"430224\":\"茶陵\",\"430225\":\"炎陵\",\"430281\":\"醴陵\"},\"430300\":{\"430302\":\"雨湖\",\"430304\":\"岳塘\",\"430321\":\"湘潭\",\"430381\":\"湘乡\",\"430382\":\"韶山\"},\"430400\":{\"430405\":\"珠晖\",\"430406\":\"雁峰\",\"430407\":\"石鼓\",\"430408\":\"蒸湘\",\"430412\":\"南岳\",\"430421\":\"衡阳\",\"430422\":\"衡南\",\"430423\":\"衡山\",\"430424\":\"衡东\",\"430426\":\"祁东\",\"430481\":\"耒阳\",\"430482\":\"常宁\"},\"430500\":{\"430502\":\"双清\",\"430503\":\"大祥\",\"430511\":\"北塔\",\"430521\":\"邵东\",\"430522\":\"新邵\",\"430523\":\"邵阳\",\"430524\":\"隆回\",\"430525\":\"洞口\",\"430527\":\"绥宁\",\"430528\":\"新宁\",\"430529\":\"城步\",\"430581\":\"武冈\"},\"430600\":{\"430602\":\"岳阳楼\",\"430603\":\"云溪\",\"430611\":\"君山\",\"430621\":\"岳阳\",\"430623\":\"华容\",\"430624\":\"湘阴\",\"430626\":\"平江\",\"430681\":\"汨罗\",\"430682\":\"临湘\"},\"430700\":{\"430702\":\"武陵\",\"430703\":\"鼎城\",\"430721\":\"安乡\",\"430722\":\"汉寿\",\"430723\":\"澧县\",\"430724\":\"临澧\",\"430725\":\"桃源\",\"430726\":\"石门\",\"430781\":\"津\"},\"430800\":{\"430802\":\"永定\",\"430811\":\"武陵源\",\"430821\":\"慈利\",\"430822\":\"桑植\"},\"430900\":{\"430902\":\"资阳\",\"430903\":\"赫山\",\"430921\":\"南县\",\"430922\":\"桃江\",\"430923\":\"安化\",\"430981\":\"沅江\"},\"431000\":{\"431002\":\"北湖\",\"431003\":\"苏仙\",\"431021\":\"桂阳\",\"431022\":\"宜章\",\"431023\":\"永兴\",\"431024\":\"嘉禾\",\"431025\":\"临武\",\"431026\":\"汝城\",\"431027\":\"桂东\",\"431028\":\"安仁\",\"431081\":\"资兴\"},\"431100\":{\"431102\":\"零陵\",\"431103\":\"冷水滩\",\"431121\":\"祁阳\",\"431122\":\"东安\",\"431123\":\"双牌\",\"431124\":\"道县\",\"431125\":\"江永\",\"431126\":\"宁远\",\"431127\":\"蓝山\",\"431128\":\"新田\",\"431129\":\"江华\"},\"431200\":{\"431202\":\"鹤城\",\"431221\":\"中方\",\"431222\":\"沅陵\",\"431223\":\"辰溪\",\"431224\":\"溆浦\",\"431225\":\"会同\",\"431226\":\"麻阳\",\"431227\":\"新晃\",\"431228\":\"芷江\",\"431229\":\"靖州\",\"431230\":\"通道\",\"431281\":\"洪江\"},\"431300\":{\"431302\":\"娄星\",\"431321\":\"双峰\",\"431322\":\"新化\",\"431381\":\"冷水江\",\"431382\":\"涟源\"},\"433100\":{\"433101\":\"吉首\",\"433122\":\"泸溪\",\"433123\":\"凤凰\",\"433124\":\"花垣\",\"433125\":\"保靖\",\"433126\":\"古丈\",\"433127\":\"永顺\",\"433130\":\"龙山\"},\"440000\":{\"440100\":\"广州\",\"440200\":\"韶关\",\"440300\":\"深圳\",\"440400\":\"珠海\",\"440500\":\"汕头\",\"440600\":\"佛山\",\"440700\":\"江门\",\"440800\":\"湛江\",\"440900\":\"茂名\",\"441200\":\"肇庆\",\"441300\":\"惠州\",\"441400\":\"梅州\",\"441500\":\"汕尾\",\"441600\":\"河源\",\"441700\":\"阳江\",\"441800\":\"清远\",\"441900\":\"东莞\",\"442000\":\"中山\",\"445100\":\"潮州\",\"445200\":\"揭阳\",\"445300\":\"云浮\"},\"440100\":{\"440103\":\"荔湾\",\"440104\":\"越秀\",\"440105\":\"海珠\",\"440106\":\"天河\",\"440111\":\"白云\",\"440112\":\"黄埔\",\"440113\":\"番禺\",\"440114\":\"花都\",\"440115\":\"南沙\",\"440117\":\"从化\",\"440118\":\"增城\"},\"440200\":{\"440203\":\"武江\",\"440204\":\"浈江\",\"440205\":\"曲江\",\"440222\":\"始兴\",\"440224\":\"仁化\",\"440229\":\"翁源\",\"440232\":\"乳源\",\"440233\":\"新丰\",\"440281\":\"乐昌\",\"440282\":\"南雄\"},\"440300\":{\"440303\":\"罗湖\",\"440304\":\"福田\",\"440305\":\"南山\",\"440306\":\"宝安\",\"440307\":\"龙岗\",\"440308\":\"盐田\"},\"440400\":{\"440402\":\"香洲\",\"440403\":\"斗门\",\"440404\":\"金湾\"},\"440500\":{\"440507\":\"龙湖\",\"440511\":\"金平\",\"440512\":\"濠江\",\"440513\":\"潮阳\",\"440514\":\"潮南\",\"440515\":\"澄海\",\"440523\":\"南澳\"},\"440600\":{\"440604\":\"禅城\",\"440605\":\"南海\",\"440606\":\"顺德\",\"440607\":\"三水\",\"440608\":\"高明\"},\"440700\":{\"440703\":\"蓬江\",\"440704\":\"江海\",\"440705\":\"新会\",\"440781\":\"台山\",\"440783\":\"开平\",\"440784\":\"鹤山\",\"440785\":\"恩平\"},\"440800\":{\"440802\":\"赤坎\",\"440803\":\"霞山\",\"440804\":\"坡头\",\"440811\":\"麻章\",\"440823\":\"遂溪\",\"440825\":\"徐闻\",\"440881\":\"廉江\",\"440882\":\"雷州\",\"440883\":\"吴川\"},\"440900\":{\"440902\":\"茂南\",\"440904\":\"电白\",\"440981\":\"高州\",\"440982\":\"化州\",\"440983\":\"信宜\"},\"441200\":{\"441202\":\"端州\",\"441203\":\"鼎湖\",\"441204\":\"高要\",\"441223\":\"广宁\",\"441224\":\"怀集\",\"441225\":\"封开\",\"441226\":\"德庆\",\"441284\":\"四会\"},\"441300\":{\"441302\":\"惠城\",\"441303\":\"惠阳\",\"441322\":\"博罗\",\"441323\":\"惠东\",\"441324\":\"龙门\"},\"441400\":{\"441402\":\"梅江\",\"441403\":\"梅县\",\"441422\":\"大埔\",\"441423\":\"丰顺\",\"441424\":\"五华\",\"441426\":\"平远\",\"441427\":\"蕉岭\",\"441481\":\"兴宁\"},\"441500\":{\"441502\":\"城区\",\"441521\":\"海丰\",\"441523\":\"陆河\",\"441581\":\"陆丰\"},\"441600\":{\"441602\":\"源城\",\"441621\":\"紫金\",\"441622\":\"龙川\",\"441623\":\"连平\",\"441624\":\"和平\",\"441625\":\"东源\"},\"441700\":{\"441702\":\"江城\",\"441704\":\"阳东\",\"441721\":\"阳西\",\"441781\":\"阳春\"},\"441800\":{\"441802\":\"清城\",\"441803\":\"清新\",\"441821\":\"佛冈\",\"441823\":\"阳山\",\"441825\":\"连山\",\"441826\":\"连南\",\"441881\":\"英德\",\"441882\":\"连州\"},\"441900\":{\"441900\":\"东莞\"},\"442000\":{\"442000\":\"中山\"},\"442100\":{\"442100\":\"东沙群岛\"},\"445100\":{\"445102\":\"湘桥\",\"445103\":\"潮安\",\"445122\":\"饶平\"},\"445200\":{\"445202\":\"榕城\",\"445203\":\"揭东\",\"445222\":\"揭西\",\"445224\":\"惠来\",\"445281\":\"普宁\"},\"445300\":{\"445302\":\"云城\",\"445303\":\"云安\",\"445321\":\"新兴\",\"445322\":\"郁南\",\"445381\":\"罗定\"},\"450000\":{\"450100\":\"南宁\",\"450200\":\"柳州\",\"450300\":\"桂林\",\"450400\":\"梧州\",\"450500\":\"北海\",\"450600\":\"防城港\",\"450700\":\"钦州\",\"450800\":\"贵港\",\"450900\":\"玉林\",\"451000\":\"百色\",\"451100\":\"贺州\",\"451200\":\"河池\",\"451300\":\"来宾\",\"451400\":\"崇左\"},\"450100\":{\"450102\":\"兴宁\",\"450103\":\"青秀\",\"450105\":\"江南\",\"450107\":\"西乡塘\",\"450108\":\"良庆\",\"450109\":\"邕宁\",\"450110\":\"武鸣\",\"450123\":\"隆安\",\"450124\":\"马山\",\"450125\":\"上林\",\"450126\":\"宾阳\",\"450127\":\"横县\"},\"450200\":{\"450202\":\"城中\",\"450203\":\"鱼峰\",\"450204\":\"柳南\",\"450205\":\"柳北\",\"450221\":\"柳江\",\"450222\":\"柳城\",\"450223\":\"鹿寨\",\"450224\":\"融安\",\"450225\":\"融水\",\"450226\":\"三江\"},\"450300\":{\"450302\":\"秀峰\",\"450303\":\"叠彩\",\"450304\":\"象山\",\"450305\":\"七星\",\"450311\":\"雁山\",\"450312\":\"临桂\",\"450321\":\"阳朔\",\"450323\":\"灵川\",\"450324\":\"全州\",\"450325\":\"兴安\",\"450326\":\"永福\",\"450327\":\"灌阳\",\"450328\":\"龙胜\",\"450329\":\"资源\",\"450330\":\"平乐\",\"450331\":\"荔浦\",\"450332\":\"恭城\"},\"450400\":{\"450403\":\"万秀\",\"450405\":\"长洲\",\"450406\":\"龙圩\",\"450421\":\"苍梧\",\"450422\":\"藤县\",\"450423\":\"蒙山\",\"450481\":\"岑溪\"},\"450500\":{\"450502\":\"海城\",\"450503\":\"银海\",\"450512\":\"铁山港\",\"450521\":\"合浦\"},\"450600\":{\"450602\":\"港口\",\"450603\":\"防城\",\"450621\":\"上思\",\"450681\":\"东兴\"},\"450700\":{\"450702\":\"钦南\",\"450703\":\"钦北\",\"450721\":\"灵山\",\"450722\":\"浦北\"},\"450800\":{\"450802\":\"港北\",\"450803\":\"港南\",\"450804\":\"覃塘\",\"450821\":\"平南\",\"450881\":\"桂平\"},\"450900\":{\"450902\":\"玉州\",\"450903\":\"福绵\",\"450921\":\"容县\",\"450922\":\"陆川\",\"450923\":\"博白\",\"450924\":\"兴业\",\"450981\":\"北流\"},\"451000\":{\"451002\":\"右江\",\"451021\":\"田阳\",\"451022\":\"田东\",\"451023\":\"平果\",\"451024\":\"德保\",\"451026\":\"那坡\",\"451027\":\"凌云\",\"451028\":\"乐业\",\"451029\":\"田林\",\"451030\":\"西林\",\"451031\":\"隆林\",\"451081\":\"靖西\"},\"451100\":{\"451102\":\"八步\",\"451103\":\"平桂\",\"451121\":\"昭平\",\"451122\":\"钟山\",\"451123\":\"富川\"},\"451200\":{\"451202\":\"金城江\",\"451221\":\"南丹\",\"451222\":\"天峨\",\"451223\":\"凤山\",\"451224\":\"东兰\",\"451225\":\"罗城\",\"451226\":\"环江\",\"451227\":\"巴马\",\"451228\":\"都安\",\"451229\":\"大化\",\"451281\":\"宜州\"},\"451300\":{\"451302\":\"兴宾\",\"451321\":\"忻城\",\"451322\":\"象州\",\"451323\":\"武宣\",\"451324\":\"金秀\",\"451381\":\"合山\"},\"451400\":{\"451402\":\"江州\",\"451421\":\"扶绥\",\"451422\":\"宁明\",\"451423\":\"龙州\",\"451424\":\"大新\",\"451425\":\"天等\",\"451481\":\"凭祥\"},\"460000\":{\"460100\":\"海口\",\"460200\":\"三亚\",\"460300\":\"三沙\",\"460400\":\"儋州\",\"469001\":\"五指山\",\"469002\":\"琼海\",\"469005\":\"文昌\",\"469006\":\"万宁\",\"469007\":\"东方\",\"469021\":\"定安\",\"469022\":\"屯昌\",\"469023\":\"澄迈\",\"469024\":\"临高\",\"469025\":\"白沙\",\"469026\":\"昌江\",\"469027\":\"乐东\",\"469028\":\"陵水\",\"469029\":\"保亭\",\"469030\":\"琼中\"},\"460100\":{\"460105\":\"秀英\",\"460106\":\"龙华\",\"460107\":\"琼山\",\"460108\":\"美兰\"},\"460200\":{\"460202\":\"海棠\",\"460203\":\"吉阳\",\"460204\":\"天涯\",\"460205\":\"崖州\"},\"460300\":{\"460321\":\"西沙群岛\",\"460322\":\"南沙群岛\",\"460323\":\"中沙群岛\"},\"500000\":{\"500100\":\"重庆\"},\"500100\":{\"500101\":\"万州\",\"500102\":\"涪陵\",\"500103\":\"渝中\",\"500104\":\"大渡口\",\"500105\":\"江北\",\"500106\":\"沙坪坝\",\"500107\":\"九龙坡\",\"500108\":\"南岸\",\"500109\":\"北碚\",\"500110\":\"綦江\",\"500111\":\"大足\",\"500112\":\"渝北\",\"500113\":\"巴南\",\"500114\":\"黔江\",\"500115\":\"长寿\",\"500116\":\"江津\",\"500117\":\"合川\",\"500118\":\"永川\",\"500119\":\"南川\",\"500120\":\"璧山\",\"500151\":\"铜梁\",\"500152\":\"潼南\",\"500153\":\"荣昌\",\"500154\":\"开州\",\"500228\":\"梁平\",\"500229\":\"城口\",\"500230\":\"丰都\",\"500231\":\"垫江\",\"500232\":\"武隆\",\"500233\":\"忠县\",\"500235\":\"云阳\",\"500236\":\"奉节\",\"500237\":\"巫山\",\"500238\":\"巫溪\",\"500240\":\"石柱\",\"500241\":\"秀山\",\"500242\":\"酉阳\",\"500243\":\"彭水\",\"500244\":\"郊县\"},\"510000\":{\"510100\":\"成都\",\"510300\":\"自贡\",\"510400\":\"攀枝花\",\"510500\":\"泸州\",\"510600\":\"德阳\",\"510700\":\"绵阳\",\"510800\":\"广元\",\"510900\":\"遂宁\",\"511000\":\"内江\",\"511100\":\"乐山\",\"511300\":\"南充\",\"511400\":\"眉山\",\"511500\":\"宜宾\",\"511600\":\"广安\",\"511700\":\"达州\",\"511800\":\"雅安\",\"511900\":\"巴中\",\"512000\":\"资阳\",\"513200\":\"阿坝\",\"513300\":\"甘孜\",\"513400\":\"凉山\"},\"510100\":{\"510104\":\"锦江\",\"510105\":\"青羊\",\"510106\":\"金牛\",\"510107\":\"武侯\",\"510108\":\"成华\",\"510109\":\"高新\",\"510112\":\"龙泉驿\",\"510113\":\"青白江\",\"510114\":\"新都\",\"510115\":\"温江\",\"510116\":\"双流\",\"510121\":\"金堂\",\"510124\":\"郫县\",\"510129\":\"大邑\",\"510131\":\"蒲江\",\"510132\":\"新津\",\"510156\":\"天府新区\",\"510180\":\"简阳\",\"510181\":\"都江堰\",\"510182\":\"彭州\",\"510183\":\"邛崃\",\"510184\":\"崇州\"},\"510300\":{\"510302\":\"自流井\",\"510303\":\"贡井\",\"510304\":\"大安\",\"510311\":\"沿滩\",\"510321\":\"荣县\",\"510322\":\"富顺\"},\"510400\":{\"510402\":\"东区\",\"510403\":\"西区\",\"510411\":\"仁和\",\"510421\":\"米易\",\"510422\":\"盐边\"},\"510500\":{\"510502\":\"江阳\",\"510503\":\"纳溪\",\"510504\":\"龙马潭\",\"510521\":\"泸县\",\"510522\":\"合江\",\"510524\":\"叙永\",\"510525\":\"古蔺\"},\"510600\":{\"510603\":\"旌阳\",\"510623\":\"中江\",\"510626\":\"罗江\",\"510681\":\"广汉\",\"510682\":\"什邡\",\"510683\":\"绵竹\"},\"510700\":{\"510703\":\"涪城\",\"510704\":\"游仙\",\"510705\":\"安州\",\"510722\":\"三台\",\"510723\":\"盐亭\",\"510725\":\"梓潼\",\"510726\":\"北川\",\"510727\":\"平武\",\"510781\":\"江油\"},\"510800\":{\"510802\":\"利州\",\"510811\":\"昭化\",\"510812\":\"朝天\",\"510821\":\"旺苍\",\"510822\":\"青川\",\"510823\":\"剑阁\",\"510824\":\"苍溪\"},\"510900\":{\"510903\":\"船山\",\"510904\":\"安居\",\"510921\":\"蓬溪\",\"510922\":\"射洪\",\"510923\":\"大英\"},\"511000\":{\"511002\":\"中区\",\"511011\":\"东兴\",\"511024\":\"威远\",\"511025\":\"资中\",\"511028\":\"隆昌\"},\"511100\":{\"511102\":\"中区\",\"511111\":\"沙湾\",\"511112\":\"五通桥\",\"511113\":\"金口河\",\"511123\":\"犍为\",\"511124\":\"井研\",\"511126\":\"夹江\",\"511129\":\"沐川\",\"511132\":\"峨边\",\"511133\":\"马边\",\"511181\":\"峨眉山\"},\"511300\":{\"511302\":\"顺庆\",\"511303\":\"高坪\",\"511304\":\"嘉陵\",\"511321\":\"南部\",\"511322\":\"营山\",\"511323\":\"蓬安\",\"511324\":\"仪陇\",\"511325\":\"西充\",\"511381\":\"阆中\"},\"511400\":{\"511402\":\"东坡\",\"511403\":\"彭山\",\"511421\":\"仁寿\",\"511423\":\"洪雅\",\"511424\":\"丹棱\",\"511425\":\"青神\"},\"511500\":{\"511502\":\"翠屏\",\"511503\":\"南溪\",\"511521\":\"宜宾\",\"511523\":\"江安\",\"511524\":\"长宁\",\"511525\":\"高县\",\"511526\":\"珙县\",\"511527\":\"筠连\",\"511528\":\"兴文\",\"511529\":\"屏山\"},\"511600\":{\"511602\":\"广安\",\"511603\":\"前锋\",\"511621\":\"岳池\",\"511622\":\"武胜\",\"511623\":\"邻水\",\"511681\":\"华蓥\"},\"511700\":{\"511702\":\"通川\",\"511703\":\"达川\",\"511722\":\"宣汉\",\"511723\":\"开江\",\"511724\":\"大竹\",\"511725\":\"渠县\",\"511781\":\"万源\"},\"511800\":{\"511802\":\"雨城\",\"511803\":\"名山\",\"511822\":\"荥经\",\"511823\":\"汉源\",\"511824\":\"石棉\",\"511825\":\"天全\",\"511826\":\"芦山\",\"511827\":\"宝兴\"},\"511900\":{\"511902\":\"巴州\",\"511903\":\"恩阳\",\"511921\":\"通江\",\"511922\":\"南江\",\"511923\":\"平昌\"},\"512000\":{\"512002\":\"雁江\",\"512021\":\"安岳\",\"512022\":\"乐至\"},\"513200\":{\"513201\":\"马尔康\",\"513221\":\"汶川\",\"513222\":\"理县\",\"513223\":\"茂县\",\"513224\":\"松潘\",\"513225\":\"九寨沟\",\"513226\":\"金川\",\"513227\":\"小金\",\"513228\":\"黑水\",\"513230\":\"壤塘\",\"513231\":\"阿坝\",\"513232\":\"若尔盖\",\"513233\":\"红原\"},\"513300\":{\"513301\":\"康定\",\"513322\":\"泸定\",\"513323\":\"丹巴\",\"513324\":\"九龙\",\"513325\":\"雅江\",\"513326\":\"道孚\",\"513327\":\"炉霍\",\"513328\":\"甘孜\",\"513329\":\"新龙\",\"513330\":\"德格\",\"513331\":\"白玉\",\"513332\":\"石渠\",\"513333\":\"色达\",\"513334\":\"理塘\",\"513335\":\"巴塘\",\"513336\":\"乡城\",\"513337\":\"稻城\",\"513338\":\"得荣\"},\"513400\":{\"513401\":\"西昌\",\"513422\":\"木里\",\"513423\":\"盐源\",\"513424\":\"德昌\",\"513425\":\"会理\",\"513426\":\"会东\",\"513427\":\"宁南\",\"513428\":\"普格\",\"513429\":\"布拖\",\"513430\":\"金阳\",\"513431\":\"昭觉\",\"513432\":\"喜德\",\"513433\":\"冕宁\",\"513434\":\"越西\",\"513435\":\"甘洛\",\"513436\":\"美姑\",\"513437\":\"雷波\"},\"520000\":{\"520100\":\"贵阳\",\"520200\":\"六盘水\",\"520300\":\"遵义\",\"520400\":\"安顺\",\"520500\":\"毕节\",\"520600\":\"铜仁\",\"522300\":\"黔西南\",\"522600\":\"黔东南\",\"522700\":\"黔南\"},\"520100\":{\"520102\":\"南明\",\"520103\":\"云岩\",\"520111\":\"花溪\",\"520112\":\"乌当\",\"520113\":\"白云\",\"520115\":\"观山湖\",\"520121\":\"开阳\",\"520122\":\"息烽\",\"520123\":\"修文\",\"520181\":\"清镇\"},\"520200\":{\"520201\":\"钟山\",\"520203\":\"六枝特\",\"520221\":\"水城\",\"520222\":\"盘县\"},\"520300\":{\"520302\":\"红花岗\",\"520303\":\"汇川\",\"520304\":\"播州\",\"520322\":\"桐梓\",\"520323\":\"绥阳\",\"520324\":\"正安\",\"520325\":\"道真\",\"520326\":\"务川\",\"520327\":\"凤冈\",\"520328\":\"湄潭\",\"520329\":\"余庆\",\"520330\":\"习水\",\"520381\":\"赤水\",\"520382\":\"仁怀\"},\"520400\":{\"520402\":\"西秀\",\"520403\":\"平坝\",\"520422\":\"普定\",\"520423\":\"镇宁\",\"520424\":\"关岭\",\"520425\":\"紫云\"},\"520500\":{\"520502\":\"七星关\",\"520521\":\"大方\",\"520522\":\"黔西\",\"520523\":\"金沙\",\"520524\":\"织金\",\"520525\":\"纳雍\",\"520526\":\"威宁\",\"520527\":\"赫章\"},\"520600\":{\"520602\":\"碧江\",\"520603\":\"万山\",\"520621\":\"江口\",\"520622\":\"玉屏\",\"520623\":\"石阡\",\"520624\":\"思南\",\"520625\":\"印江\",\"520626\":\"德江\",\"520627\":\"沿河\",\"520628\":\"松桃\"},\"522300\":{\"522301\":\"兴义\",\"522322\":\"兴仁\",\"522323\":\"普安\",\"522324\":\"晴隆\",\"522325\":\"贞丰\",\"522326\":\"望谟\",\"522327\":\"册亨\",\"522328\":\"安龙\"},\"522600\":{\"522601\":\"凯里\",\"522622\":\"黄平\",\"522623\":\"施秉\",\"522624\":\"三穗\",\"522625\":\"镇远\",\"522626\":\"岑巩\",\"522627\":\"天柱\",\"522628\":\"锦屏\",\"522629\":\"剑河\",\"522630\":\"台江\",\"522631\":\"黎平\",\"522632\":\"榕江\",\"522633\":\"从江\",\"522634\":\"雷山\",\"522635\":\"麻江\",\"522636\":\"丹寨\"},\"522700\":{\"522701\":\"都匀\",\"522702\":\"福泉\",\"522722\":\"荔波\",\"522723\":\"贵定\",\"522725\":\"瓮安\",\"522726\":\"独山\",\"522727\":\"平塘\",\"522728\":\"罗甸\",\"522729\":\"长顺\",\"522730\":\"龙里\",\"522731\":\"惠水\",\"522732\":\"三都\"},\"530000\":{\"530100\":\"昆明\",\"530300\":\"曲靖\",\"530400\":\"玉溪\",\"530500\":\"保山\",\"530600\":\"昭通\",\"530700\":\"丽江\",\"530800\":\"普洱\",\"530900\":\"临沧\",\"532300\":\"楚雄\",\"532500\":\"红河\",\"532600\":\"文山\",\"532800\":\"西双版纳\",\"532900\":\"大理\",\"533100\":\"德宏\",\"533300\":\"怒江\",\"533400\":\"迪庆\"},\"530100\":{\"530102\":\"五华\",\"530103\":\"盘龙\",\"530111\":\"官渡\",\"530112\":\"西山\",\"530113\":\"东川\",\"530114\":\"呈贡\",\"530122\":\"晋宁\",\"530124\":\"富民\",\"530125\":\"宜良\",\"530126\":\"石林\",\"530127\":\"嵩明\",\"530128\":\"禄劝\",\"530129\":\"寻甸\",\"530181\":\"安宁\"},\"530300\":{\"530302\":\"麒麟\",\"530303\":\"沾益\",\"530321\":\"马龙\",\"530322\":\"陆良\",\"530323\":\"师宗\",\"530324\":\"罗平\",\"530325\":\"富源\",\"530326\":\"会泽\",\"530381\":\"宣威\"},\"530400\":{\"530402\":\"红塔\",\"530403\":\"江川\",\"530422\":\"澄江\",\"530423\":\"通海\",\"530424\":\"华宁\",\"530425\":\"易门\",\"530426\":\"峨山\",\"530427\":\"新平\",\"530428\":\"元江\"},\"530500\":{\"530502\":\"隆阳\",\"530521\":\"施甸\",\"530523\":\"龙陵\",\"530524\":\"昌宁\",\"530581\":\"腾冲\"},\"530600\":{\"530602\":\"昭阳\",\"530621\":\"鲁甸\",\"530622\":\"巧家\",\"530623\":\"盐津\",\"530624\":\"大关\",\"530625\":\"永善\",\"530626\":\"绥江\",\"530627\":\"镇雄\",\"530628\":\"彝良\",\"530629\":\"威信\",\"530630\":\"水富\"},\"530700\":{\"530702\":\"古城\",\"530721\":\"玉龙\",\"530722\":\"永胜\",\"530723\":\"华坪\",\"530724\":\"宁蒗\"},\"530800\":{\"530802\":\"思茅\",\"530821\":\"宁洱\",\"530822\":\"墨江\",\"530823\":\"景东\",\"530824\":\"景谷\",\"530825\":\"镇沅\",\"530826\":\"江城\",\"530827\":\"孟连\",\"530828\":\"澜沧\",\"530829\":\"西盟\"},\"530900\":{\"530902\":\"临翔\",\"530921\":\"凤庆\",\"530922\":\"云县\",\"530923\":\"永德\",\"530924\":\"镇康\",\"530925\":\"双江\",\"530926\":\"耿马\",\"530927\":\"沧源\"},\"532300\":{\"532301\":\"楚雄\",\"532322\":\"双柏\",\"532323\":\"牟定\",\"532324\":\"南华\",\"532325\":\"姚安\",\"532326\":\"大姚\",\"532327\":\"永仁\",\"532328\":\"元谋\",\"532329\":\"武定\",\"532331\":\"禄丰\"},\"532500\":{\"532501\":\"个旧\",\"532502\":\"开远\",\"532503\":\"蒙自\",\"532504\":\"弥勒\",\"532523\":\"屏边\",\"532524\":\"建水\",\"532525\":\"石屏\",\"532527\":\"泸西\",\"532528\":\"元阳\",\"532529\":\"红河\",\"532530\":\"金平\",\"532531\":\"绿春\",\"532532\":\"河口\"},\"532600\":{\"532601\":\"文山\",\"532622\":\"砚山\",\"532623\":\"西畴\",\"532624\":\"麻栗坡\",\"532625\":\"马关\",\"532626\":\"丘北\",\"532627\":\"广南\",\"532628\":\"富宁\"},\"532800\":{\"532801\":\"景洪\",\"532822\":\"勐海\",\"532823\":\"勐腊\"},\"532900\":{\"532901\":\"大理\",\"532922\":\"漾濞\",\"532923\":\"祥云\",\"532924\":\"宾川\",\"532925\":\"弥渡\",\"532926\":\"南涧\",\"532927\":\"巍山\",\"532928\":\"永平\",\"532929\":\"云龙\",\"532930\":\"洱源\",\"532931\":\"剑川\",\"532932\":\"鹤庆\"},\"533100\":{\"533102\":\"瑞丽\",\"533103\":\"芒\",\"533122\":\"梁河\",\"533123\":\"盈江\",\"533124\":\"陇川\"},\"533300\":{\"533301\":\"泸水\",\"533323\":\"福贡\",\"533324\":\"贡山\",\"533325\":\"兰坪\"},\"533400\":{\"533401\":\"香格里拉\",\"533422\":\"德钦\",\"533423\":\"维西\"},\"540000\":{\"540100\":\"拉萨\",\"540200\":\"日喀则\",\"540300\":\"昌都\",\"540400\":\"林芝\",\"540500\":\"山南\",\"542400\":\"那曲\",\"542500\":\"阿里\"},\"540100\":{\"540102\":\"城关\",\"540103\":\"堆龙德庆\",\"540121\":\"林周\",\"540122\":\"当雄\",\"540123\":\"尼木\",\"540124\":\"曲水\",\"540126\":\"达孜\",\"540127\":\"墨竹工卡\"},\"540200\":{\"540202\":\"桑珠孜\",\"540221\":\"南木林\",\"540222\":\"江孜\",\"540223\":\"定日\",\"540224\":\"萨迦\",\"540225\":\"拉孜\",\"540226\":\"昂仁\",\"540227\":\"谢通门\",\"540228\":\"白朗\",\"540229\":\"仁布\",\"540230\":\"康马\",\"540231\":\"定结\",\"540232\":\"仲巴\",\"540233\":\"亚东\",\"540234\":\"吉隆\",\"540235\":\"聂拉木\",\"540236\":\"萨嘎\",\"540237\":\"岗巴\"},\"540300\":{\"540302\":\"卡若\",\"540321\":\"江达\",\"540322\":\"贡觉\",\"540323\":\"类乌齐\",\"540324\":\"丁青\",\"540325\":\"察雅\",\"540326\":\"八宿\",\"540327\":\"左贡\",\"540328\":\"芒康\",\"540329\":\"洛隆\",\"540330\":\"边坝\"},\"540400\":{\"540402\":\"巴宜\",\"540421\":\"工布江达\",\"540422\":\"米林\",\"540423\":\"墨脱\",\"540424\":\"波密\",\"540425\":\"察隅\",\"540426\":\"朗县\"},\"540500\":{\"540502\":\"乃东\",\"540521\":\"扎囊\",\"540522\":\"贡嘎\",\"540523\":\"桑日\",\"540524\":\"琼结\",\"540525\":\"曲松\",\"540526\":\"措美\",\"540527\":\"洛扎\",\"540528\":\"加查\",\"540529\":\"隆子\",\"540530\":\"错那\",\"540531\":\"浪卡子\"},\"542400\":{\"542421\":\"那曲\",\"542422\":\"嘉黎\",\"542423\":\"比如\",\"542424\":\"聂荣\",\"542425\":\"安多\",\"542426\":\"申扎\",\"542427\":\"索县\",\"542428\":\"班戈\",\"542429\":\"巴青\",\"542430\":\"尼玛\",\"542431\":\"双湖\"},\"542500\":{\"542521\":\"普兰\",\"542522\":\"札达\",\"542523\":\"噶尔\",\"542524\":\"日土\",\"542525\":\"革吉\",\"542526\":\"改则\",\"542527\":\"措勤\"},\"610000\":{\"610100\":\"西安\",\"610200\":\"铜川\",\"610300\":\"宝鸡\",\"610400\":\"咸阳\",\"610500\":\"渭南\",\"610600\":\"延安\",\"610700\":\"汉中\",\"610800\":\"榆林\",\"610900\":\"安康\",\"611000\":\"商洛\"},\"610100\":{\"610102\":\"新城\",\"610103\":\"碑林\",\"610104\":\"莲湖\",\"610111\":\"灞桥\",\"610112\":\"未央\",\"610113\":\"雁塔\",\"610114\":\"阎良\",\"610115\":\"临潼\",\"610116\":\"长安\",\"610117\":\"高陵\",\"610122\":\"蓝田\",\"610124\":\"周至\",\"610125\":\"户县\"},\"610200\":{\"610202\":\"王益\",\"610203\":\"印台\",\"610204\":\"耀州\",\"610222\":\"宜君\"},\"610300\":{\"610302\":\"渭滨\",\"610303\":\"金台\",\"610304\":\"陈仓\",\"610322\":\"凤翔\",\"610323\":\"岐山\",\"610324\":\"扶风\",\"610326\":\"眉县\",\"610327\":\"陇县\",\"610328\":\"千阳\",\"610329\":\"麟游\",\"610330\":\"凤县\",\"610331\":\"太白\"},\"610400\":{\"610402\":\"秦都\",\"610403\":\"杨陵\",\"610404\":\"渭城\",\"610422\":\"三原\",\"610423\":\"泾阳\",\"610424\":\"乾县\",\"610425\":\"礼泉\",\"610426\":\"永寿\",\"610427\":\"彬县\",\"610428\":\"长武\",\"610429\":\"旬邑\",\"610430\":\"淳化\",\"610431\":\"武功\",\"610481\":\"兴平\"},\"610500\":{\"610502\":\"临渭\",\"610503\":\"华州\",\"610522\":\"潼关\",\"610523\":\"大荔\",\"610524\":\"合阳\",\"610525\":\"澄城\",\"610526\":\"蒲城\",\"610527\":\"白水\",\"610528\":\"富平\",\"610581\":\"韩城\",\"610582\":\"华阴\"},\"610600\":{\"610602\":\"宝塔\",\"610621\":\"延长\",\"610622\":\"延川\",\"610623\":\"子长\",\"610624\":\"安塞\",\"610625\":\"志丹\",\"610626\":\"吴起\",\"610627\":\"甘泉\",\"610628\":\"富县\",\"610629\":\"洛川\",\"610630\":\"宜川\",\"610631\":\"黄龙\",\"610632\":\"黄陵\"},\"610700\":{\"610702\":\"汉台\",\"610721\":\"南郑\",\"610722\":\"城固\",\"610723\":\"洋县\",\"610724\":\"西乡\",\"610725\":\"勉县\",\"610726\":\"宁强\",\"610727\":\"略阳\",\"610728\":\"镇巴\",\"610729\":\"留坝\",\"610730\":\"佛坪\"},\"610800\":{\"610802\":\"榆阳\",\"610803\":\"横山\",\"610821\":\"神木\",\"610822\":\"府谷\",\"610824\":\"靖边\",\"610825\":\"定边\",\"610826\":\"绥德\",\"610827\":\"米脂\",\"610828\":\"佳县\",\"610829\":\"吴堡\",\"610830\":\"清涧\",\"610831\":\"子洲\"},\"610900\":{\"610902\":\"汉滨\",\"610921\":\"汉阴\",\"610922\":\"石泉\",\"610923\":\"宁陕\",\"610924\":\"紫阳\",\"610925\":\"岚皋\",\"610926\":\"平利\",\"610927\":\"镇坪\",\"610928\":\"旬阳\",\"610929\":\"白河\"},\"611000\":{\"611002\":\"商州\",\"611021\":\"洛南\",\"611022\":\"丹凤\",\"611023\":\"商南\",\"611024\":\"山阳\",\"611025\":\"镇安\",\"611026\":\"柞水\"},\"620000\":{\"620100\":\"兰州\",\"620200\":\"嘉峪关\",\"620300\":\"金昌\",\"620400\":\"白银\",\"620500\":\"天水\",\"620600\":\"武威\",\"620700\":\"张掖\",\"620800\":\"平凉\",\"620900\":\"酒泉\",\"621000\":\"庆阳\",\"621100\":\"定西\",\"621200\":\"陇南\",\"622900\":\"临夏\",\"623000\":\"甘南\"},\"620100\":{\"620102\":\"城关\",\"620103\":\"七里河\",\"620104\":\"西固\",\"620105\":\"安宁\",\"620111\":\"红古\",\"620121\":\"永登\",\"620122\":\"皋兰\",\"620123\":\"榆中\"},\"620300\":{\"620302\":\"金川\",\"620321\":\"永昌\"},\"620400\":{\"620402\":\"白银\",\"620403\":\"平川\",\"620421\":\"靖远\",\"620422\":\"会宁\",\"620423\":\"景泰\"},\"620500\":{\"620502\":\"秦州\",\"620503\":\"麦积\",\"620521\":\"清水\",\"620522\":\"秦安\",\"620523\":\"甘谷\",\"620524\":\"武山\",\"620525\":\"张家川\"},\"620600\":{\"620602\":\"凉州\",\"620621\":\"民勤\",\"620622\":\"古浪\",\"620623\":\"天祝\"},\"620700\":{\"620702\":\"甘州\",\"620721\":\"肃南\",\"620722\":\"民乐\",\"620723\":\"临泽\",\"620724\":\"高台\",\"620725\":\"山丹\"},\"620800\":{\"620802\":\"崆峒\",\"620821\":\"泾川\",\"620822\":\"灵台\",\"620823\":\"崇信\",\"620824\":\"华亭\",\"620825\":\"庄浪\",\"620826\":\"静宁\"},\"620900\":{\"620902\":\"肃州\",\"620921\":\"金塔\",\"620922\":\"瓜州\",\"620923\":\"肃北\",\"620924\":\"阿克塞\",\"620981\":\"玉门\",\"620982\":\"敦煌\"},\"621000\":{\"621002\":\"西峰\",\"621021\":\"庆城\",\"621022\":\"环县\",\"621023\":\"华池\",\"621024\":\"合水\",\"621025\":\"正宁\",\"621026\":\"宁县\",\"621027\":\"镇原\"},\"621100\":{\"621102\":\"安定\",\"621121\":\"通渭\",\"621122\":\"陇西\",\"621123\":\"渭源\",\"621124\":\"临洮\",\"621125\":\"漳县\",\"621126\":\"岷县\"},\"621200\":{\"621202\":\"武都\",\"621221\":\"成县\",\"621222\":\"文县\",\"621223\":\"宕昌\",\"621224\":\"康县\",\"621225\":\"西和\",\"621226\":\"礼县\",\"621227\":\"徽县\",\"621228\":\"两当\"},\"622900\":{\"622901\":\"临夏\",\"622921\":\"临夏\",\"622922\":\"康乐\",\"622923\":\"永靖\",\"622924\":\"广河\",\"622925\":\"和政\",\"622926\":\"东乡族自治\",\"622927\":\"积石山\"},\"623000\":{\"623001\":\"合作\",\"623021\":\"临潭\",\"623022\":\"卓尼\",\"623023\":\"舟曲\",\"623024\":\"迭部\",\"623025\":\"玛曲\",\"623026\":\"碌曲\",\"623027\":\"夏河\"},\"630000\":{\"630100\":\"西宁\",\"630200\":\"海东\",\"632200\":\"海北\",\"632300\":\"黄南\",\"632500\":\"海南\",\"632600\":\"果洛\",\"632700\":\"玉树\",\"632800\":\"海西\"},\"630100\":{\"630102\":\"城东\",\"630103\":\"城中\",\"630104\":\"城西\",\"630105\":\"城北\",\"630121\":\"大通\",\"630122\":\"湟中\",\"630123\":\"湟源\"},\"630200\":{\"630202\":\"乐都\",\"630203\":\"平安\",\"630222\":\"民和\",\"630223\":\"互助\",\"630224\":\"化隆\",\"630225\":\"循化\"},\"632200\":{\"632221\":\"门源\",\"632222\":\"祁连\",\"632223\":\"海晏\",\"632224\":\"刚察\"},\"632300\":{\"632321\":\"同仁\",\"632322\":\"尖扎\",\"632323\":\"泽库\",\"632324\":\"河南\"},\"632500\":{\"632521\":\"共和\",\"632522\":\"同德\",\"632523\":\"贵德\",\"632524\":\"兴海\",\"632525\":\"贵南\"},\"632600\":{\"632621\":\"玛沁\",\"632622\":\"班玛\",\"632623\":\"甘德\",\"632624\":\"达日\",\"632625\":\"久治\",\"632626\":\"玛多\"},\"632700\":{\"632701\":\"玉树\",\"632722\":\"杂多\",\"632723\":\"称多\",\"632724\":\"治多\",\"632725\":\"囊谦\",\"632726\":\"曲麻莱\"},\"632800\":{\"632801\":\"格尔木\",\"632802\":\"德令哈\",\"632821\":\"乌兰\",\"632822\":\"都兰\",\"632823\":\"天峻\",\"632825\":\"海西\"},\"640000\":{\"640100\":\"银川\",\"640200\":\"石嘴山\",\"640300\":\"吴忠\",\"640400\":\"固原\",\"640500\":\"中卫\"},\"640100\":{\"640104\":\"兴庆\",\"640105\":\"西夏\",\"640106\":\"金凤\",\"640121\":\"永宁\",\"640122\":\"贺兰\",\"640181\":\"灵武\"},\"640200\":{\"640202\":\"大武口\",\"640205\":\"惠农\",\"640221\":\"平罗\"},\"640300\":{\"640302\":\"利通\",\"640303\":\"红寺堡\",\"640323\":\"盐池\",\"640324\":\"同心\",\"640381\":\"青铜峡\"},\"640400\":{\"640402\":\"原州\",\"640422\":\"西吉\",\"640423\":\"隆德\",\"640424\":\"泾源\",\"640425\":\"彭阳\"},\"640500\":{\"640502\":\"沙坡头\",\"640521\":\"中宁\",\"640522\":\"海原\"},\"650000\":{\"650100\":\"乌鲁木齐\",\"650200\":\"克拉玛依\",\"650400\":\"吐鲁番\",\"650500\":\"哈密\",\"652300\":\"昌吉\",\"652700\":\"博尔塔拉\",\"652800\":\"巴音郭楞\",\"652900\":\"阿克苏\",\"653000\":\"克孜勒苏柯尔克孜\",\"653100\":\"喀什\",\"653200\":\"和田\",\"654000\":\"伊犁\",\"654200\":\"塔城\",\"654300\":\"阿勒泰\",\"659001\":\"石河子\",\"659002\":\"阿拉尔\",\"659003\":\"图木舒克\",\"659004\":\"五家渠\",\"659005\":\"北屯\",\"659006\":\"铁门关\",\"659007\":\"双河\",\"659008\":\"可克达拉\",\"659009\":\"昆玉\"},\"650100\":{\"650102\":\"天山\",\"650103\":\"沙依巴克\",\"650104\":\"新区\",\"650105\":\"水磨沟\",\"650106\":\"头屯河\",\"650107\":\"达坂城\",\"650109\":\"米东\",\"650121\":\"乌鲁木齐\"},\"650200\":{\"650202\":\"独山子\",\"650203\":\"克拉玛依\",\"650204\":\"白碱滩\",\"650205\":\"乌尔禾\"},\"650400\":{\"650402\":\"高昌\",\"650421\":\"鄯善\",\"650422\":\"托克逊\"},\"650500\":{\"650502\":\"伊州\",\"650521\":\"巴里坤\",\"650522\":\"伊吾\"},\"652300\":{\"652301\":\"昌吉\",\"652302\":\"阜康\",\"652323\":\"呼图壁\",\"652324\":\"玛纳斯\",\"652325\":\"奇台\",\"652327\":\"吉木萨尔\",\"652328\":\"木垒\"},\"652700\":{\"652701\":\"博乐\",\"652702\":\"阿拉山口\",\"652722\":\"精河\",\"652723\":\"温泉\"},\"652800\":{\"652801\":\"库尔勒\",\"652822\":\"轮台\",\"652823\":\"尉犁\",\"652824\":\"若羌\",\"652825\":\"且末\",\"652826\":\"焉耆\",\"652827\":\"和静\",\"652828\":\"和硕\",\"652829\":\"博湖\"},\"652900\":{\"652901\":\"阿克苏\",\"652922\":\"温宿\",\"652923\":\"库车\",\"652924\":\"沙雅\",\"652925\":\"新和\",\"652926\":\"拜城\",\"652927\":\"乌什\",\"652928\":\"阿瓦提\",\"652929\":\"柯坪\"},\"653000\":{\"653001\":\"阿图什\",\"653022\":\"阿克陶\",\"653023\":\"阿合奇\",\"653024\":\"乌恰\"},\"653100\":{\"653101\":\"喀什\",\"653121\":\"疏附\",\"653122\":\"疏勒\",\"653123\":\"英吉沙\",\"653124\":\"泽普\",\"653125\":\"莎车\",\"653126\":\"叶城\",\"653127\":\"麦盖提\",\"653128\":\"岳普湖\",\"653129\":\"伽师\",\"653130\":\"巴楚\",\"653131\":\"塔什库尔干塔吉克\"},\"653200\":{\"653201\":\"和田\",\"653221\":\"和田\",\"653222\":\"墨玉\",\"653223\":\"皮山\",\"653224\":\"洛浦\",\"653225\":\"策勒\",\"653226\":\"于田\",\"653227\":\"民丰\"},\"654000\":{\"654002\":\"伊宁\",\"654003\":\"奎屯\",\"654004\":\"霍尔果斯\",\"654021\":\"伊宁\",\"654022\":\"察布查尔锡伯\",\"654023\":\"霍城\",\"654024\":\"巩留\",\"654025\":\"新源\",\"654026\":\"昭苏\",\"654027\":\"特克斯\",\"654028\":\"尼勒克\"},\"654200\":{\"654201\":\"塔城\",\"654202\":\"乌苏\",\"654221\":\"额敏\",\"654223\":\"沙湾\",\"654224\":\"托里\",\"654225\":\"裕民\",\"654226\":\"和布克赛尔蒙古\"},\"654300\":{\"654301\":\"阿勒泰\",\"654321\":\"布尔津\",\"654322\":\"富蕴\",\"654323\":\"福海\",\"654324\":\"哈巴河\",\"654325\":\"青河\",\"654326\":\"吉木乃\"},\"810000\":{\"810001\":\"中西\",\"810002\":\"湾仔\",\"810003\":\"东区\",\"810004\":\"南区\",\"810005\":\"油尖旺\",\"810006\":\"深水埗\",\"810007\":\"九龙城\",\"810008\":\"黄大仙\",\"810009\":\"观塘\",\"810010\":\"荃湾\",\"810011\":\"屯门\",\"810012\":\"元朗\",\"810013\":\"北区\",\"810014\":\"大埔\",\"810015\":\"西贡\",\"810016\":\"沙田\",\"810017\":\"葵青\",\"810018\":\"离岛\"},\"820000\":{\"820001\":\"花地玛堂\",\"820002\":\"花王堂\",\"820003\":\"望德堂\",\"820004\":\"大堂\",\"820005\":\"风顺堂\",\"820006\":\"嘉模堂\",\"820007\":\"路凼填海\",\"820008\":\"圣方济各堂\"}}";
//        Map<String, Map<String, String>> addressInfos = JsonUtils.readValue(content, Map.class);
//        System.out.println(JsonUtils.dump(addressInfos));
//        initAddressTree(addressInfos);
//        PatientDeliveryService.districtAddressNodeMap.forEach((s, addressNode) -> {
//            System.out.println(addressNode.getAddressId() + " " + addressNode.getAddressName());
//        });
//        PatientDeliveryService.districtAddressNodeMap = AddressUtils.buildAddressTree(addressInfos);
//        AddressNode node = PatientDeliveryService.districtAddressNodeMap.get("441900");
//        PatientImportReq patientImportReq = new PatientImportReq();
//        System.out.println(JsonUtils.dump(patientImportReq));
//
//
//        String[] addressStr = "12312321/123123/1231231".split("/", 4);
//        System.out.println(addressStr.length);
    }


    private void initAddressTree(Map<String, Map<String, String>> addressInfos) {
        //上1级/下1级
        AddressNode addressRootNode = new AddressNode();
        addressRootNode.setAddressId("100000");
        addressRootNode.setAddressName("中国");
        addressRootNode.setParent(null);
        generateAddressTree(addressRootNode, addressInfos);
//        System.out.println(PatientDeliveryService.districtAddressNodeMap.size());
    }

    private void generateAddressTree(AddressNode addressRootNode, Map<String, Map<String, String>> addressInfos) {
        Map<String, String> childKeyValues = addressInfos.get(addressRootNode.getAddressId());
        if (childKeyValues != null) {
            List<AddressNode> childNodes = new ArrayList<>();
            childKeyValues.forEach((addressId, addressName) -> {
                AddressNode childAddressNode = new AddressNode();
                childAddressNode.setAddressId(addressId);
                childAddressNode.setAddressName(addressName);
                childAddressNode.setParent(addressRootNode);
                childNodes.add(childAddressNode);
                //省 市 区
                if (!StringUtils.equals(childAddressNode.getAddressId(), addressRootNode.getAddressId())) {
                    generateAddressTree(childAddressNode, addressInfos);
//                    PatientDeliveryService.districtAddressNodeMap.put(addressRootNode.getAddressId(), addressRootNode);
                }
            });
            addressRootNode.setChildNodes(childNodes);
        } else {
            System.out.println(addressRootNode.getAddressId() + " " + addressRootNode.getAddressName() + " " + addressRootNode.getParent().getAddressName());
//            PatientDeliveryService.districtAddressNodeMap.put(addressRootNode.getAddressId(), addressRootNode);
        }
    }


    //
//    @Test
//    public void testBasic() {
//        Flux.just("Hello", "World").subscribe(System.out::println);
//        Flux.fromArray(new Integer[]{1, 2, 3}).subscribe(System.out::println);
//        Flux.empty().subscribe(System.out::println);
//        Flux.range(1, 10).subscribe(System.out::println);
//        Flux.interval(Duration.of(10, ChronoUnit.SECONDS)).subscribe(System.out::println);
//    }
//
    @Test
    public void test11() {
//        String regStr = "/api(/v2)?/goods/.*/(export|import)(/stocks)?";
//        String str = "/api/goods/stocks";
//        System.out.println(Pattern.compile(regStr).matcher(str).matches());


        Instant currentTime = Instant.now(Clock.systemDefaultZone());
        System.out.println(currentTime.toEpochMilli());

        Instant currentTime1 = Instant.now();
        System.out.println(currentTime1.toEpochMilli());

        Random random = new Random();
        int i = 0;
        while (i++ < 10) {
            System.out.println(random.nextInt(30 * 60));
        }

        Instant todayEndTime = LocalDateTime.of(LocalDate.now(), LocalTime.MAX).toInstant(ZoneOffset.ofHours(8));
        System.out.println(todayEndTime);
        Instant instant = LocalDateTime.of(LocalDate.now(), LocalTime.MAX).toInstant(ZoneOffset.UTC);
        System.out.println(instant);
    }


    @Test
    public void test4() {
//        String formatDate = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
//        System.out.println(formatDate);

        Date date = new Date();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd").withZone(ZoneId.systemDefault());
        System.out.println(dateTimeFormatter.format(date.toInstant()));

        FastDateFormat formatter = FastDateFormat.getInstance("yyyy-MM-dd");
        System.out.println(formatter.format(date));
    }

    @Test
    public void testIdCardUtil() {
        System.out.println(IdcardUtil.getBirth("******************"));
    }


    @Test
    public void testSN() {
        System.out.println(CommonUtil.resetSnForPatient("12345678"));
    }


    @Test
    public void testLong() {
        System.out.println(Long.parseLong("001"));
    }


    public static class TreeNode {
        public int value;
        public TreeNode left;
        public TreeNode right;
    }

    @Test
    public void testTree() {
        // 中序遍历
        TreeNode root = new TreeNode();
        testTree1(root);

    }

    private void testTree1(TreeNode root) {
        testTree1(root.left);
        System.out.println(root.value);
        testTree1(root.right);
    }

    //非递归
    private void testTree2(TreeNode root) {
        Stack<TreeNode> treeNodes = new Stack<>();
        TreeNode temp = root;
        while (temp != null || !treeNodes.isEmpty()) {
            while (temp != null) {
                treeNodes.push(temp);
                temp = root.left;
            }

            temp = treeNodes.peek();
            System.out.println(temp.value);
            treeNodes.pop();
            temp = temp.right;
        }
    }

    @Test
    public void testAgeToBirthday() throws ParseException {
        System.out.println(CommonUtil.getBirthDayStrFromAge(33, 7, 10));
        System.out.println(CommonUtil.getBirthDayStrFromAge(21, 1, 21));
        System.out.println(CommonUtil.getBirthDayStrFromAge(21, 1, 24));


        String birthday = "2000-01-21";
        Date b1 = new SimpleDateFormat("yyyy-MM-dd").parse(birthday);
        Calendar now = Calendar.getInstance();
        Calendar b = Calendar.getInstance();
        b.setTime(b1);
        int year = now.get(Calendar.YEAR) - b.get(Calendar.YEAR);
        int month = now.get(Calendar.MONTH) - b.get(Calendar.MONTH);
        int day = now.get(Calendar.DAY_OF_MONTH) - b.get(Calendar.DAY_OF_MONTH);
        if (month < 0) {
            month = 12 - b.get(Calendar.MONTH) + now.get(Calendar.MONTH);
            year -= 1;
        }
        if (day < 0) {
            day = b.getMaximum(Calendar.DAY_OF_MONTH) - b.get(Calendar.DAY_OF_MONTH) + now.get(Calendar.DAY_OF_MONTH);
            month -= 1;
        }
        System.out.println(year + "岁" + month + "个月" + day + "天");

        CisPatientAge age = CisPatientAge.fromBirthday(birthday);
        System.out.println(age.getYear() + "岁" + age.getMonth() + "个月" + age.getDay() + "天");
    }

    @Test
    public void test111() {
        QueryParam queryParam = new QueryParam();
        queryParam.setFamilyDoctorOverdueBeginTime(new Date());
        System.out.println(JsonUtils.dump(queryParam));
    }

    @Test
    public void test222() {
        try {
            String time1 = "2021-12-06 10:47:23";
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date1 = simpleDateFormat.parse(time1);
            Calendar c = Calendar.getInstance();
            c.setTime(date1);
            c.add(Calendar.YEAR, -38);
            c.add(Calendar.MONTH, -1);
            c.add(Calendar.DAY_OF_MONTH, -9);

            System.out.println(simpleDateFormat.format(c.getTime()));
        } catch (ParseException e) {
            e.printStackTrace();
        }

    }


    @Test
    public void test333() {
        try {
            String time1 = "2021-02-01 08:45:55";
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date1 = simpleDateFormat.parse(time1);
            Calendar c = Calendar.getInstance();
            c.setTime(date1);

            c.add(Calendar.YEAR, 0);
            c.add(Calendar.MONTH, 0);
            c.add(Calendar.DAY_OF_MONTH, 31);
            System.out.println(simpleDateFormat.format(c.getTime()));

        } catch (ParseException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void test666() {
        cn.abcyun.bis.rpc.sdk.cis.model.patient.CisPatientAge age = fromBirthday("1983-10-27");
        System.out.println(age);
        try {
            String time1 = "2021-12-06 10:47:23";
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            Date date1 = simpleDateFormat.parse(time1);
//            Date date1= new Date();
            Calendar c = Calendar.getInstance();
            c.setTime(date1);
            c.add(Calendar.DAY_OF_MONTH, -9);
            c.add(Calendar.MONTH, -1);
            c.add(Calendar.YEAR, -38);
            System.out.println(simpleDateFormat.format(c.getTime()));

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static cn.abcyun.bis.rpc.sdk.cis.model.patient.CisPatientAge fromBirthday(String birthday) {
        cn.abcyun.bis.rpc.sdk.cis.model.patient.CisPatientAge age = new cn.abcyun.bis.rpc.sdk.cis.model.patient.CisPatientAge();
        if (TextUtils.isEmpty(birthday)) {
            return age;
        }

        try {
            LocalDate birthdayDate = LocalDate.parse(birthday, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            LocalDate now = LocalDate.parse("2021-12-06");
//            LocalDate now = LocalDate.now();
            Period diff = Period.between(birthdayDate, now);
            age.setYear(diff.getYears());
            age.setMonth(diff.getMonths());
            age.setDay(diff.getDays());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return age;
    }

    @Test
    public void createDat() {
        ByteArrayOutputStream gzip = new ByteArrayOutputStream();

        try {
            try (GZIPOutputStream gzipos = new GZIPOutputStream(gzip)) {
                String json = "{}";
                gzipos.write(json.getBytes("UTF-8"));
            }

            byte[] dat = Base64.getMimeEncoder().encode(gzip.toByteArray());

            try (ByteArrayInputStream input = new ByteArrayInputStream(dat);
                 FileOutputStream output = new FileOutputStream("region.dat")) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = input.read(buffer)) != -1) {
                    output.write(buffer, 0, bytesRead);
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    @Test
    public void testPinyin() {
        System.out.println(PinyinUtil.getPinyin("覃思源"));
    }
}
