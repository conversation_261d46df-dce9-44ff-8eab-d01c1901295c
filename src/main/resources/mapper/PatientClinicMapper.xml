<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.abcyun.cis.crm.dao.PatientClinicMapper">

    <insert id="insertOrUpdate">
        insert into v2_patient_clinic (clinic_id,
                                       chain_id,
                                       patient_id,
                                       last_active_date,
                                       created_by,
                                       created,
                                       last_modified_by,
                                       last_modified,
                                       last_outpatient_date,
                                       <if test="arrearsFlag != null">
                                            arrears_flag,
                                       </if>
                                       consultant_id,
                                       last_charge_date,
                                       duty_therapist_id,
                                       primary_therapist_id,
                                       first_outpatient_doctor_id,
                                       first_charge_date,
                                       first_examination_date,
                                       first_his_charge_settle_date)
        values (#{clinicId},
                #{chainId},
                #{patientId},
                #{lastActiveDate},
                #{createdBy},
                #{created},
                #{lastModifiedBy},
                #{lastModified},
                #{lastOutpatientDate},
                <if test="arrearsFlag != null">
                    #{arrearsFlag},
                </if>
                #{consultantId},
                #{lastChargeDate},
                #{dutyTherapistId},
                #{primaryTherapistId},
                #{firstOutpatientDoctorId},
                #{firstChargeDate},
                #{firstExaminationDate},
                #{firstHisChargeSettleDate}
        )
        on duplicate key update
        <if test="lastActiveDate != null">
            last_active_date = #{lastActiveDate},
        </if>
        <if test="lastOutpatientDate != null">
            last_outpatient_date = #{lastOutpatientDate},
        </if>
        <if test="arrearsFlag != null">
            arrears_flag = #{arrearsFlag},
        </if>
        <if test="consultantId != null">
            consultant_id = #{consultantId},
        </if>
        <if test="lastChargeDate != null">
            last_charge_date = #{lastChargeDate},
        </if>
        <if test="dutyTherapistId != null">
            duty_therapist_id = #{dutyTherapistId},
        </if>
        <if test="primaryTherapistId != null">
            primary_therapist_id = #{primaryTherapistId},
        </if>
        <if test="firstOutpatientDoctorId != null">
            first_outpatient_doctor_id = #{firstOutpatientDoctorId},
        </if>
        <if test="firstOutpatientDate != null">
            first_outpatient_date = #{firstOutpatientDate},
        </if>
        <if test="firstChargeDate != null">
            first_charge_date = #{firstChargeDate},
        </if>
        <if test="firstExaminationDate != null">
            first_examination_date = #{firstExaminationDate},
        </if>
        <if test="firstHisChargeSettleDate != null">
            first_his_charge_settle_date = #{firstHisChargeSettleDate},
        </if>
        last_modified_by = #{lastModifiedBy},
        last_modified    = #{lastModified}
    </insert>
</mapper>