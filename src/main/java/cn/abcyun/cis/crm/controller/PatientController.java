package cn.abcyun.cis.crm.controller;

import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.CardPatientComposeView;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.PromotionCardPatientListView;
import cn.abcyun.bis.rpc.sdk.property.service.PropertyService;
import cn.abcyun.cis.commons.CisServiceResponse;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.commons.model.ShebaoCardInfo;
import cn.abcyun.cis.commons.rpc.crm.UpdatePatientChildCareInfoReq;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.cis.crm.api.*;
import cn.abcyun.cis.crm.api.family.UpsertPatientFamilyReq;
import cn.abcyun.cis.crm.api.patient.*;
import cn.abcyun.cis.crm.common.CommonUtil;
import cn.abcyun.cis.crm.common.CrmServiceError;
import cn.abcyun.cis.crm.common.PatientUtil;
import cn.abcyun.cis.crm.entity.*;
import cn.abcyun.cis.crm.exception.CrmCustomException;
import cn.abcyun.cis.crm.facade.PatientFacade;
import cn.abcyun.cis.crm.model.FamilyMemberShare;
import cn.abcyun.cis.crm.service.*;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponse;
import cn.abcyun.common.model.AbcServiceResponseBody;
import io.swagger.annotations.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/api/v2/crm/patients")
@Slf4j
@Api(value = "PatientController", description = "患者前端接口", produces = "application/json")
public class PatientController {

    @Autowired
    private PatientService patientService;

    @Autowired
    private PatientMemberService patientMemberService;

    @Autowired
    private PatientBusinessStatService patientBusinessStatService;

    @Autowired
    private PatientFamilyService patientFamilyService;

    @Autowired
    private PromotionService promotionService;

    @Autowired
    private PatientFacade patientFacade;

    @Autowired
    private PropertyService propertyService;

    /**
     * 获取患者列表
     */
    @PostMapping(value = "/abstracts")
    public AbcServiceResponse<PatientAbstractList> getPatientAbstractList(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                          @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                          @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                          @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode,
                                                                          @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int hisType,
                                                                          @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE, defaultValue = "0") Integer clinicType,
                                                                          @RequestBody QueryParam queryParam) {
        queryParam.setClinicId(clinicId);
        queryParam.setChainId(chainId);
        queryParam.setOperatorId(employeeId);
        queryParam.setClinicType(clinicType);
        queryParam.setHisType(hisType);
        PatientAbstractList patientAbstractList = patientService.getPatientAbstractListFromEsAndStat(queryParam, viewMode, clinicType);
        return new AbcServiceResponse<>(patientAbstractList);
    }

    /**
     * 查询患者信息
     *
     * @param patientId            患者ID
     * @param needChronicArchives
     * @param needWxInfo           是否需要微信信息
     * @param needChildCareRecords 是否需要儿保记录
     * @param showFamilyDoctor
     * @param promotionCardList
     * @param isPatientMergeReq    是否患者合并请求
     */
    @ApiOperation("查询患者信息")
    @GetMapping(value = "/{patientId}")
    public AbcServiceResponse<PatientInfoView> getPatient(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                          @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                          @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode,
                                                          @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) int hisType,
                                                          @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
                                                          @PathVariable("patientId") String patientId,
                                                          @RequestParam(value = "chronicArchives", defaultValue = "false", required = false) boolean needChronicArchives,
                                                          @RequestParam(value = "wx", defaultValue = "false", required = false) boolean needWxInfo,
                                                          @RequestParam(value = "childCareRecords", defaultValue = "false", required = false) boolean needChildCareRecords,
                                                          @RequestParam(value = "showFamilyDoctor", defaultValue = "false", required = false) boolean showFamilyDoctor,
                                                          @RequestParam(value = "promotionCardList", defaultValue = "false", required = false) boolean promotionCardList,
                                                          @RequestParam(value = "isPatientMerge", defaultValue = "false", required = false) boolean isPatientMergeReq) throws CrmCustomException {
        PatientInfoView patientInfo = patientService.getPatientInfoView(hisType, chainId, clinicId, patientId, needWxInfo, needChildCareRecords, needChronicArchives, showFamilyDoctor, promotionCardList, isPatientMergeReq, viewMode, clinicType);
        // 获取会员信息
        if (patientInfo.getIsMember() == 1) {
            PatientMemberInfo memberInfo = patientMemberService.getPatientMemberInfo(patientId, chainId);
            patientInfo.setMemberInfo(memberInfo);
        }
        return new AbcServiceResponse<>(patientInfo);
    }

    /**
     * 查询患者业务统计数据（口腔在患者档案处直接显示所有业务的数量，使用）
     */
    @ApiImplicitParams({
            @ApiImplicitParam(value = "患者ID", name = "patientId", required = true, paramType = "path", dataType = "string", dataTypeClass = String.class)
    })
    @ApiOperation("查询患者业务统计数据")
    @GetMapping(value = "/{patientId}/business-stat")
    public AbcServiceResponse<PatientBusinessStatView> getPatientBusinessStat(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                              @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                              @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
                                                                              @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode,
                                                                              @PathVariable("patientId") String patientId) {
        PatientBusinessStatView patientBusinessStat = patientBusinessStatService.getPatientBusinessStat(chainId, clinicId, patientId, clinicType, viewMode);
        return new AbcServiceResponse<>(patientBusinessStat);
    }

    /**
     * 查询患者病历统计数据
     */
    @ApiImplicitParams({
            @ApiImplicitParam(value = "患者ID", name = "patientId", required = true, paramType = "path", dataType = "string", dataTypeClass = String.class)
    })
    @ApiOperation("查询患者病历统计数据")
    @GetMapping(value = "/{patientId}/business-stat/medical-record")
    public AbcServiceResponse<PatientMedicalRecordBusinessStatView> getPatientMedicalRecordBusinessStat(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                                        @PathVariable("patientId") String patientId) {
        PatientMedicalRecordBusinessStatView patientBusinessStat = patientBusinessStatService.getPatientMedicalRecordBusinessStat(chainId, clinicId, patientId);
        return new AbcServiceResponse<>(patientBusinessStat);
    }

    /**
     * 查询患者收费统计数据
     */
    @ApiImplicitParams({
            @ApiImplicitParam(value = "患者ID", name = "patientId", required = true, paramType = "path", dataType = "string", dataTypeClass = String.class)
    })
    @ApiOperation("查询患者收费统计数据")
    @GetMapping(value = "/{patientId}/business-stat/charge")
    public AbcServiceResponse<PatientChargeBusinessStatView> getPatientChargeBusinessStat(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                          @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                          @PathVariable("patientId") String patientId) {
        PatientChargeBusinessStatView patientBusinessStat = patientBusinessStatService.getPatientChargeBusinessStat(chainId, clinicId, patientId);
        return new AbcServiceResponse<>(patientBusinessStat);
    }


    /**
     * 更新患者信息
     */
    @PutMapping("/{patientId}")
    @LogReqAndRsp(longTimeLog = true)
    public AbcServiceResponse<PatientInfoView> updatePatientInfo(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer hisType,
                                                                 @PathVariable("patientId") String patientId,
                                                                 @RequestBody @Valid UpdatePatientBasicInfoReq req) {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(req.getIdCard())) {
            req.setIdCard(req.getIdCard().trim());
        }
        // 1.校验请求参数
        PatientUtil.checkInvalidPatientBasicInfo(clinicId, propertyService,
                req.getName(), req.getSex(), req.getAge(), req.getMobile(),
                req.getBirthday(), req.getIdCardType(), req.getIdCard(), req.getSourceId(), req.getMarital(),
                req.getWeight(), req.getProfession(), req.getCompany(), req.getSn(),
                req.getRemark(), req.getAddressDetail(), req.getEthnicity(), req.getVisitReason(),
                req.getConsultantId(), req.getMemberTypeId(), hisType);
        PatientInfoView updatePatientInfo = patientFacade.updatePatientInfo(req, patientId, chainId, clinicId, employeeId, hisType);
        return new AbcServiceResponse<>(updatePatientInfo);
    }

    /**
     * 获取患者会员信息
     */
    @GetMapping("/{patientId}/member")
    public AbcServiceResponse<PatientInfoView> getPatientMember(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                                @PathVariable String patientId) {
        PatientInfoView patientInfo = patientMemberService.getPatientMember(chainId, patientId);
        return new AbcServiceResponse<>(patientInfo);
    }

    /**
     * 查询患者关联的可用会员信息
     */
    @GetMapping("/{patientId}/relate-available-members")
    public AbcServiceResponse<AbcListPage<PatientMemberInfo>> getPatientRelateAvailableMembers(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                                                               @PathVariable String patientId) {
        AbcListPage<PatientMemberInfo> abcListPage = new AbcListPage<>();
        abcListPage.setRows(patientMemberService.getPatientRelateAvailableMembers(chainId, patientId));
        return new AbcServiceResponse<>(abcListPage);
    }

    /**
     * 更新患者会员信息
     */
    @PutMapping("/{patientId}/member")
    @LogReqAndRsp(longTimeLog = true)
    public AbcServiceResponse<PatientInfo> updatePatientMember(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer hisType,
                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                               @PathVariable String patientId,
                                                               @RequestBody @Valid PatientBasicInfo req) {
        req.setId(patientId);
        req.setChainId(chainId);
        req.setClinicId(clinicId);
        req.setLastModifiedBy(employeeId);
        req.setCreatedBy(employeeId);
        CommonUtil.checkBirthdayAndAge(req.getBirthday(), req.getAge(), hisType != Organ.HisType.CIS_HIS_TYPE_PHARMACY);
        PatientInfo patientInfo = patientFacade.updatePatientMember(req, employeeId);
        patientInfo.setWxOpenId(null);
        return new AbcServiceResponse<>(patientInfo);
    }

    /**
     * 新建患者
     */
    @PostMapping(value = "")
    @LogReqAndRsp(longTimeLog = true)
    public AbcServiceResponse<PatientInfoView> createPatient(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer hisType,
                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                             @RequestBody @Valid CreatePatientReq req) throws CrmCustomException {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(req.getIdCard())) {
            req.setIdCard(req.getIdCard().trim());
        }
        CommonUtil.checkBirthdayAndAge(req.getBirthday(), req.getAge(), hisType != Organ.HisType.CIS_HIS_TYPE_PHARMACY);
        // 1.校验请求参数
        PatientUtil.checkInvalidPatientBasicInfo(clinicId, propertyService,
                req.getName(), req.getSex(), req.getAge(), req.getMobile(),
                req.getBirthday(), req.getIdCardType(), req.getIdCard(), req.getSourceId(), req.getMarital(),
                req.getWeight(), req.getProfession(), req.getCompany(), req.getSn(),
                req.getRemark(), req.getAddressDetail(), req.getEthnicity(), req.getVisitReason(),
                req.getConsultantId(), req.getMemberTypeId(), hisType);
        PatientInfoView patientInfo = patientFacade.createPatientExternal(req, chainId, clinicId, employeeId, hisType);
        return new AbcServiceResponse<>(patientInfo);
    }

    /**
     * 获取下一个有效的sn
     */
    @PostMapping(value = "/sn/next")
    @ApiOperation(value = "获取下一个有效的sn")
    public AbcServiceResponse<NextAvailableSnRsp> generateNextAvailableSn(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                          @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                          @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) throws CrmCustomException {
        NextAvailableSnRsp rsp = patientService.generateNextAvailableSn(chainId, clinicId, employeeId);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 创建或更新患者
     */
    @PutMapping(value = "")
    @LogReqAndRsp(longTimeLog = true)
    public AbcServiceResponse<PatientInfo> createOrUpdatePatient(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer hisType,
                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                 @RequestBody @Valid PatientBasicInfo info) throws CrmCustomException {
        if (org.apache.commons.lang3.StringUtils.isNotBlank(info.getIdCard())) {
            info.setIdCard(info.getIdCard().trim());
        }
        CommonUtil.checkBirthdayAndAge(info.getBirthday(), info.getAge(), hisType != Organ.HisType.CIS_HIS_TYPE_PHARMACY);
        // 1.校验请求参数
        PatientUtil.checkInvalidPatientBasicInfo(clinicId, propertyService,
                info.getName(), info.getSex(), info.getAge(), info.getMobile(),
                info.getBirthday(), info.getIdCardType(), info.getIdCard(), info.getSourceId(), info.getMarital(),
                info.getWeight(), info.getProfession(), info.getCompany(), info.getSn(),
                info.getRemark(), info.getAddressDetail(), info.getEthnicity(), info.getVisitReason(),
                info.getConsultantId(), info.getMemberTypeId(), hisType);
        PatientInfo patientInfo = patientFacade.createOrUpdatePatient(info, chainId, clinicId, employeeId, null, hisType);
        patientInfo.setWxOpenId(null);
        return new AbcServiceResponse<>(patientInfo);
    }

    /**
     * 新增会员
     */
    @PostMapping(value = "/member")
    @LogReqAndRsp(longTimeLog = true)
    public AbcServiceResponse<PatientInfo> addPatientMember(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                            @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                            @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                            @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer hisType,
                                                            @RequestBody @Valid PatientBasicInfo patientBasicInfo) throws CrmCustomException {

        if (patientBasicInfo == null || patientBasicInfo.getName() == null || patientBasicInfo.getMemberInfo() == null) {
            throw new CrmCustomException(CrmServiceError.PARAMETER_REQUIRED);
        }

        if (StringUtils.isEmpty(patientBasicInfo.getMobile())) {// 会员手机号码必填
            throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_MOBILE_REQUIRED);
        }

        if (!CommonUtil.isValidPhoneNumber(patientBasicInfo.getMobile(), patientBasicInfo.getCountryCode())) {
            throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_MOBILE_FORMAT_ERROR);
        }
        patientBasicInfo.setChainId(chainId);
        patientBasicInfo.setClinicId(clinicId);
        PatientInfo patientInfo = patientFacade.addPatientMember(patientBasicInfo, chainId, clinicId, employeeId, hisType);
        return new AbcServiceResponse<>(patientInfo);
    }

    /**
     * 删除会员
     */
    @DeleteMapping("/{patientId}/member")
    @LogReqAndRsp(longTimeLog = true)
    public AbcServiceResponse<HttpStatus> deletePatientMember(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                              @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID, required = false) String clinicId,
                                                              @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                              @PathVariable String patientId) {
        patientFacade.deletePatientMember(patientId, chainId, clinicId, employeeId);
        return new AbcServiceResponse<>(HttpStatus.NO_CONTENT);
    }

    /**
     * 查询患者既往史
     */
    @GetMapping(value = "/{patientId}/pastHistory")
    public AbcServiceResponse<PatientPastHistoryRsp> getPatientPastHistory(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                           @PathVariable("patientId") String patientId) {
        return new AbcServiceResponse<>(patientService.getPatientPastHistory(chainId, patientId));
    }

    /**
     * 患者搜索
     */
    @GetMapping(value = "/query")
    public AbcServiceResponse<PatientBasicSearchList> queryPatient(@RequestParam(value = "key", required = false) String key,
                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer hisType,
                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode,
                                                                   @RequestParam(value = "offset", defaultValue = "0", required = false) Integer offset,
                                                                   @RequestParam(value = "limit", defaultValue = "20", required = false) Integer limit,
                                                                   @RequestParam(value = "clinicId", required = false) String limitedClinicId,
                                                                   @RequestParam(value = "withDefault", defaultValue = "0", required = false) int withDefault,
                                                                   @RequestParam(value = "isShowFamilyStatus", defaultValue = "false", required = false) boolean isShowFamilyStatus,
                                                                   @ApiParam(value = "扩展数据标识，支持多个筛选标记，用二进制位表示：00000001：返回工作单位，00000010：返回备注；00000100：返回地址")
                                                                   @RequestParam(value = "extDataFlag", defaultValue = "0", required = false) int extraDataFlag) {
        PatientBasicSearchList rsp = patientService.queryPatientBasicInfo(chainId, clinicId, clinicType, viewMode, hisType, employeeId, key, limitedClinicId, withDefault, isShowFamilyStatus, extraDataFlag, offset, limit);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 患者搜索（用于黑名单患者搜索，封装了黑名单标识和微信绑定状态）
     */
    @GetMapping(value = "/query/block")
    public AbcServiceResponse<AbcListPage<BlockPatientInfoView>> queryPatientForBlock(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                      @RequestParam(value = "offset", defaultValue = "0", required = false) Integer offset,
                                                                                      @RequestParam(value = "limit", defaultValue = "20", required = false) Integer limit,
                                                                                      @RequestParam(value = "key", required = false) String key) {

        AbcListPage<BlockPatientInfoView> rsp = patientService.queryPatientForBlock(chainId, clinicId, employeeId, key, offset, limit);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 根据姓名和手机查询患者
     */
    @GetMapping(value = "/query/by-name-mobile")
    public AbcServiceResponse<PatientInfo> queryPatientByNameAndMobile(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                       @RequestParam(value = "name", required = false) String name,
                                                                       @RequestParam(value = "mobile", required = false) String mobile) {
        PatientInfo patientInfo = patientService.queryPatientByNameAndMobile(chainId, name, mobile);
        return new AbcServiceResponse<>(patientInfo);
    }

    @GetMapping(value = "/query/public-health")
    @ApiOperation(value = "搜索患者-包含公卫信息", produces = "application/json")
    public AbcServiceResponse<AbcListPage<PatientSearchWithPublicHealthInfoRsp>> queryPatientWithPublicHealthInfo(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                                                  @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                                                  @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                                                  @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer hisType,
                                                                                                                  @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) int clinicType,
                                                                                                                  @RequestHeader(value = CisJWTUtils.CIS_HEADER_VIEW_MODE) int viewMode,
                                                                                                                  @RequestParam(value = "withPublicHealthInfo", defaultValue = "0", required = false) int withPublicHealthInfo,
                                                                                                                  @RequestParam(value = "offset", defaultValue = "0", required = false) Integer offset,
                                                                                                                  @RequestParam(value = "limit", defaultValue = "20", required = false) Integer limit,
                                                                                                                  @RequestParam(value = "keyword") String keyword) {
        AbcListPage<PatientSearchWithPublicHealthInfoRsp> rsp = patientService.queryPatientWithPublicHealthInfo(chainId, clinicId, employeeId,
                hisType, keyword, withPublicHealthInfo, clinicType, viewMode, offset, limit);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 更新会员卡-支付密码
     */
    @PostMapping("/{patientId}/member/password")
    public AbcServiceResponse<GeneralRsp> updatePatientMemberPassword(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                      @PathVariable String patientId, @RequestBody UpdatePatientMemberPasswordReq updatePatientMemberPasswordReq) throws CrmCustomException {
        GeneralRsp generalRsp = patientMemberService.updatePatientMemberInfo(updatePatientMemberPasswordReq, employeeId, chainId, patientId);
        return new AbcServiceResponse<>(generalRsp);
    }

    /**
     * 校验会员卡-支付密码
     */
    @PostMapping("/{patientId}/member/password/verification")
    public AbcServiceResponse<GeneralRsp> verifyPatientMemberPassword(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                                      @PathVariable String patientId,
                                                                      @RequestBody UpdatePatientMemberPasswordReq memberPasswordVerifyReq) throws CrmCustomException {
        GeneralRsp generalRsp = patientMemberService.verifyPatientMemberPassword(memberPasswordVerifyReq.getPassword(), chainId, patientId);
        return new AbcServiceResponse<>(generalRsp);
    }

    /**
     * 根据外部二维码创建或者更新患者信息
     *
     * @param clinicId   诊所id
     * @param chainId    连锁id
     * @param employeeId 员工id
     * @param req        外部二维码请求
     * @return
     * @throws CrmCustomException
     */
    @PostMapping("/basic/by-external-code")
    @LogReqAndRsp(longTimeLog = true)
    @ApiOperation(value = "根据外部二维码创建或者更新患者信息", produces = "application/json")
    public AbcServiceResponse<PatientInfo> createOrUpdatePatientInfoByQrCode(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                             @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer hisType,
                                                                             @RequestBody @Valid QRCodeForPatientCrmReq req) throws CrmCustomException {
        PatientInfo patientInfo = patientFacade.createOrUpdatePatientInfoByQrCode(chainId, clinicId, req, employeeId, hisType);
        return new AbcServiceResponse<>(patientInfo);
    }

    /**
     * 根据二维码查询患者信息
     *
     * @param clinicId 诊所id
     * @param chainId  连锁id
     * @param req      外部二维码请求
     * @return
     * @throws CrmCustomException
     */
    @PostMapping("/basic/by-external-code/query")
    @ApiOperation(value = "根据二维码查询患者信息", produces = "application/json")
    public CisServiceResponse<PatientInfo> getPatientInfoFromExternalAgencyByQrCode(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                    @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                                                    @RequestBody @Valid QRCodeForPatientCrmReq req) throws CrmCustomException {
        PatientInfo patientInfo = patientService.getPatientInfoFromExternalAgencyByQrCode(chainId, clinicId, req);
        return new CisServiceResponse<>(patientInfo);
    }

    /**
     * 根据外部二维码【武汉健康码】 绑定患者
     */
    @PutMapping("/{patientId}/basic/bind/external-code")
    public CisServiceResponse<PatientInfo> bindPatientInfoWithExternalCodeInfo(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                                               @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID, required = false) String employeeId,
                                                                               @PathVariable String patientId,
                                                                               @RequestBody @Valid PatientBindWithQrCodeCrmReq patientBindWithQrCodeCrmReq) throws CrmCustomException {
        PatientInfo patientInfo = patientFacade.bindPatientInfoWithExternalCodeInfo(chainId, clinicId, patientBindWithQrCodeCrmReq.getQrCode(), patientId, employeeId);
        PatientBindWithQrCodeCrmRsp patientBindWithQrCodeCrmRsp = new PatientBindWithQrCodeCrmRsp();
        patientBindWithQrCodeCrmRsp.setId(patientInfo.getId());
        patientBindWithQrCodeCrmRsp.setSex(patientInfo.getSex());
        patientBindWithQrCodeCrmRsp.setName(patientInfo.getName());
        patientBindWithQrCodeCrmRsp.setMobile(patientInfo.getMobile());
        patientBindWithQrCodeCrmRsp.setBirthday(patientInfo.getId());
        patientBindWithQrCodeCrmRsp.setIdCardType(patientInfo.getIdCardType());
        patientBindWithQrCodeCrmRsp.setIdCard(patientInfo.getIdCard());
        patientBindWithQrCodeCrmRsp.setExternalCodeId(patientInfo.getExternalCodeId());
        patientBindWithQrCodeCrmRsp.setAddress(patientInfo.getAddress());
        patientBindWithQrCodeCrmRsp.setQrCode(patientBindWithQrCodeCrmReq.getQrCode());
        return new CisServiceResponse<>(patientInfo);
    }

    /**
     * 解除患者-外部二维码【武汉健康码】的绑定
     */
    @PutMapping("/{patientId}/basic/unbind/external-code")
    public CisServiceResponse<PatientInfo> unbindPatientInfoWithExternalCodeInfo(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                                                 @PathVariable String patientId) throws CrmCustomException {
        PatientInfo patientInfo = patientService.unbindPatientInfoWithExternalCodeInfo(chainId, clinicId, patientId);
        return new CisServiceResponse<>(patientInfo);
    }

    /**
     * 解除患者-微信的绑定
     */
    @PutMapping("/{patientId}/basic/unbind/wx")
    public AbcServiceResponseBody<GeneralRsp> unbindPatientInfoWithWxInfo(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                          @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                          @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                          @PathVariable String patientId) throws CrmCustomException {

        GeneralRsp rsp = patientService.unbindPatientInfoWithWxInfo(chainId, clinicId, patientId, employeeId);
        return new AbcServiceResponseBody<>(rsp);
    }

    /**
     * 获取患者绑定微信的二维码
     */
    @GetMapping("/{patientId}/basic/bind/wx/url")
    public AbcServiceResponseBody<PatientBindWithWxInfoRsp> getPatientBindWithWxInfoUrl(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                        @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                                                        @PathVariable String patientId) throws CrmCustomException {
        PatientBindWithWxInfoRsp rsp = patientService.getPatientBindWithWxInfoUrl(patientId, chainId);
        return new AbcServiceResponseBody<>(rsp);
    }

    /**
     * 生成微诊所绑定二维码
     */
    @PostMapping("/bind/wx/qr-code")
    @LogReqAndRsp
    public AbcServiceResponseBody<PatientBindWithWeClinicQrCodeRsp> generatePatientBindWithWeClinicQrCode(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                                          @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                                          @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer hisType,
                                                                                                          @RequestBody PatientBindWithWeClinicQrCodeReq patientBindWithWeClinicQrCodeReq) throws CrmCustomException, NotFoundException {
        PatientBindWithWeClinicQrCodeRsp rsp = patientService.generatePatientBindWithWeClinicQrCode(patientBindWithWeClinicQrCodeReq, chainId, clinicId, hisType);
        return new AbcServiceResponseBody<>(rsp);
    }


    /**
     * 查询微信绑定状态
     */
    @GetMapping("/bind/wx/qr-code/status")
    public AbcServiceResponse<PatientBindWithWeClinicStatusRsp> getPatientBindWithWeClinicStatus(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                                 @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                                 @RequestParam String key) throws CrmCustomException {

        PatientBindWithWeClinicStatusRsp rsp = patientService.getPatientBindWithWeClinicStatus(key, chainId);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 更新患者儿保档案-标记
     */
    @PutMapping("/{patientId}/child-care/archives")
    public CisServiceResponse<PatientInfo> updatePatientChildCareArchives(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                          @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                                          @PathVariable String patientId) throws CrmCustomException {
        PatientInfo rsp = patientService.updatePatientChildCareArchives(patientId, chainId);
        return new CisServiceResponse<>(rsp);
    }

    /**
     * 更新患者-儿保档案信息
     */
    @PutMapping("/{patientId}/child-care/info")
    @LogReqAndRsp
    public CisServiceResponse<PatientChildCareInfoRsp> updatePatientChildCareInfo(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                  @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                  @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                  @PathVariable String patientId,
                                                                                  @RequestBody UpdatePatientChildCareInfoReq req) throws CrmCustomException, NotFoundException {

        PatientChildCareInfoRsp rsp = patientService.updatePatientChildCareInfo(req, patientId, chainId, employeeId);
        return new CisServiceResponse<>(rsp);
    }

    /**
     * 批量修改家庭成员接口
     *
     * @deprecated 2023-11-07 口腔门诊咨询师需求被废弃 {@link PatientController#upsertPatientFamilyMember(UpsertPatientFamilyReq, String, String, String)}
     */
    @PutMapping(value = "/family/relevance/{patientId}")
    @LogReqAndRsp
    @ApiOperation(value = "批量修改家庭成员接口", produces = "application/json", response = PatientFamilyListVO.class)
    @Deprecated
    public AbcServiceResponse<PatientFamilyListVO> updatePatientFamilyMembers(@RequestBody @Valid CreatePatientFamilyReq req,
                                                                              @PathVariable("patientId") String patientId,
                                                                              @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                              @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        return new AbcServiceResponse<>(patientFamilyService.updatePatientFamilyMembers(req.getPatientIds(), patientId, chainId, employeeId));
    }

    /**
     * 新增或修改家庭成员接口
     */
    @PostMapping(value = "/family/relevance/{patientId}/upsert")
    @LogReqAndRsp
    @ApiOperation(value = "新增或修改家庭成员接口", produces = "application/json")
    public AbcServiceResponse<PatientFamilyVo> upsertPatientFamilyMember(@RequestBody @Valid UpsertPatientFamilyReq req,
                                                                         @PathVariable("patientId") String patientId,
                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        return new AbcServiceResponse<>(patientFamilyService.addOrUpdatePatientFamilyMember(patientId, req, chainId, employeeId));
    }

    /**
     * 删除家庭成员关联接口
     */
    @DeleteMapping(value = "/family/relevance")
    @LogReqAndRsp
    @ApiOperation(value = "删除家庭成员关联接口", produces = "application/json")
    public AbcServiceResponse<PatientFamilyVo> deletePatientFamily(@RequestBody @Valid PatientFamilyDeleteReq patientFamilyDeleteReq,
                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId) {
        patientFamilyDeleteReq.setChainId(chainId);
        return new AbcServiceResponse<>(patientFacade.deletePatientFamily(patientFamilyDeleteReq.getPatientId(), patientFamilyDeleteReq.getChainId(), clinicId, patientFamilyDeleteReq.getParentId(), employeeId, null));
    }

    /**
     * 查询家庭成员集合列表
     */
    @GetMapping(value = "/family/relevance/{patientId}")
    @ApiOperation(value = "查询家庭成员集合列表", produces = "application/json", response = PatientFamilyListView.class)
    public AbcServiceResponse<PatientFamilyListView> getPatientFamilyList(@PathVariable("patientId") String patientId,
                                                                          @RequestParam(value = "needWxBindStatus", required = false, defaultValue = "false") boolean needWxBindStatus,
                                                                          @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId) {
        PatientFamilyListView patientFamilyListView = patientFamilyService.getPatientFamilyList(patientId, chainId, false, needWxBindStatus);
        return new AbcServiceResponse<>(patientFamilyListView);
    }

    /**
     * 批量查询家庭成员集合列表
     */
    @PostMapping(value = "/family/relevance/list")
    @ApiOperation(value = "查询家庭成员集合列表", produces = "application/json", response = PatientFamilyListView.class)
    public AbcServiceResponse<BatchFamilyPatientListView> batchGetPatientFamilyList(@RequestBody BatchFamilyPatientReq req) {
        return new AbcServiceResponse<>(patientFamilyService.batchGetPatientFamilyList(req));
    }

    /**
     * 查看患者-微信绑定信息【包含该患者所在的家庭】
     */
    @PostMapping(value = "/get/family/bind/wx")
    @ApiOperation(value = "查看患者-微信绑定信息【包含该患者所在的家庭】", produces = "application/json", response = PatientFamilyBindWxListRsp.class)
    public AbcServiceResponse<PatientFamilyBindWxListRsp> getPatientFamilyBindWxInfo(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                     @RequestBody BatchFamilyPatientReq req) {
        req.setChainId(chainId);
        return new AbcServiceResponse<>(patientFamilyService.getPatientFamilyBindWxInfo(req));
    }

    /**
     * 获取当前患者家庭是否绑定并且关注微信
     */
    @PostMapping(value = "/get/family/wx/status")
    @ApiOperation(value = "校验当前患者家庭是否绑定或并且关注微信", produces = "application/json", response = PatientFamilyBindWxListRsp.class)
    public AbcServiceResponse<PatientFamilyBindWxListRsp> getFamilyBindAndFollowedStatus(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                         @RequestBody BatchFamilyPatientReq req) {
        req.setChainId(chainId);
        return new AbcServiceResponse<>(patientFamilyService.getPatientFamilyBindWxInfo(req));
    }

    /**
     * 获取医保卡--绑定的患者信息
     */
    @PostMapping(value = "/bind/shebao/query")
    @ApiOperation(value = "获取医保卡--绑定的患者信息", produces = "application/json")
    public AbcServiceResponse<AbcListPage<PatientInfoView>> getPatientInfoWithShebaoInfo(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                         @RequestBody ShebaoCardInfo shebaoCardInfoReq) {
        return new AbcServiceResponse<>(patientService.getPatientInfoWithShebaoInfo(shebaoCardInfoReq.getCardNo(), shebaoCardInfoReq.getName(), shebaoCardInfoReq.getIdCardNo(), shebaoCardInfoReq.getMobile(), chainId));
    }

    /**
     * 绑定医保卡给已有患者
     */
    @PutMapping(value = "/{patientId}/bind/shebao")
    @LogReqAndRsp
    @ApiOperation(value = "绑定医保卡", produces = "application/json")
    public AbcServiceResponse<PatientInfoView> bindPatientShebaoInfo(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                     @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                     @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                     @PathVariable String patientId,
                                                                     @RequestBody ShebaoCardInfo req) {
        return new AbcServiceResponse<>(patientFacade.bindPatientShebaoInfo(req, patientId, chainId, clinicId, employeeId));
    }

    /**
     * 解绑医保卡
     */
    @DeleteMapping(value = "/{patientId}/shebao/info")
    @ApiOperation(value = "解绑医保卡", produces = "application/json")
    @LogReqAndRsp
    public AbcServiceResponse<PatientShebaoInfoVO> deletePatientShebaoInfo(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                           @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                           @PathVariable String patientId) {
        return new AbcServiceResponse<>(patientService.deletePatientShebaoInfo(patientId, chainId, employeeId));
    }

    /**
     * 通过医保卡--新建患者
     */
    @PostMapping(value = "/with-shebao-info")
    @LogReqAndRsp
    @ApiOperation(value = "读取医保卡-新增患者", produces = "application/json")
    public AbcServiceResponse<PatientInfoView> createPatientByShebaoInfo(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer hisType,
                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                         @RequestBody CreatePatientByShebaoReq req) {
        return new AbcServiceResponse<>(patientFacade.createPatientByShebaoInfo(req, chainId, clinicId, employeeId, hisType));
    }

    /**
     * 根据姓名或身份证号或手机号，获取匹配的患者信息
     */
    @GetMapping(value = "/matched/info")
    @ApiOperation(value = "获取匹配的患者信息", produces = "application/json")
    public AbcServiceResponse<AbcListPage<PatientInfoView>> getMatchedPatients(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                               @RequestParam(value = "name", required = false) String name,
                                                                               @RequestParam(value = "idCard", required = false) String idCard,
                                                                               @RequestParam(value = "mobile", required = false) String mobile) {
        AbcListPage<PatientInfoView> patientInfoVOs = patientService.getMatchedPatientsView(chainId, name, mobile, idCard);
        return new AbcServiceResponse<>(patientInfoVOs);
    }

    /**
     * 查询患者卡信息接口
     */
    @GetMapping(value = "/{patientId}/get/marketing/list")
    @ApiOperation(value = "查询患者卡信息接口", produces = "application/json", response = PatientFamilyListVO.class)
    public AbcServiceResponse<PromotionCardPatientListView> findPatientMarketingCard(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                     @PathVariable(value = "patientId") String patientId) {
        return new AbcServiceResponse<>(promotionService.listBrief(chainId, patientId));
    }

    /**
     * 查询患者营销卡详情
     */
    @GetMapping(value = "/{promotionCardId}/get/marketing/card/detail")
    @ApiOperation(value = "查询患者营销卡详情", produces = "application/json", response = PatientFamilyListVO.class)
    public AbcServiceResponse<CardPatientComposeView> findPatientMarketingCardDetail(@PathVariable(value = "promotionCardId") String promotionCardId) {
        return new AbcServiceResponse<>(promotionService.getComposeDetail(promotionCardId));
    }

    /**
     * 根据手机号后4位查询会员信息
     */
    @GetMapping(value = "/members/query/by-mobile-last4")
    @ApiOperation(value = "根据手机号后4位查询会员信息", produces = "application/json", response = PatientMemberByMobileLast4VO.class)
    public AbcServiceResponse<AbcListPage<PatientMemberByMobileLast4VO>> queryPatientMemberByMobileLast4(@RequestParam String key,
                                                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId) {
        return new AbcServiceResponse<>(patientMemberService.queryPatientMemberByMobileLast4(key, chainId, clinicId, employeeId));
    }

    //查询患者共享的主卡人信息（使用新接口替换/family/relevance/{patientId}）
    //获取该患者当前可用的会员信息（获取家长的会员卡信息）
    @GetMapping(value = "/memberShare/{sharedPatientId}")
    @Deprecated
    public CisServiceResponse<PatientAvailableMemberInfoVO> getPatientMemberSharedByInfo(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                         @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) Integer clinicType,
                                                                                         @PathVariable("sharedPatientId") String sharedPatientId) {
        PatientAvailableMemberInfoVO patientInfo = patientMemberService.getMemberSharedPatientMemberInfo(chainId, sharedPatientId, clinicId);
        return new CisServiceResponse<>(patientInfo);
    }

    //查询患者共享的主卡用户基本信息
    @GetMapping(value = "/memberShareInfo/{sharedPatientId}")
    @Deprecated
    public CisServiceResponse<FamilyMemberShare> getPatientMemberSharedByBasicInfo(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                                                   @PathVariable("sharedPatientId") String sharedPatientId) {
        FamilyMemberShare patientMemberShareInfo = patientMemberService.getMemberSharedBasicPatientInfo(chainId, sharedPatientId);
        return new CisServiceResponse<>(patientMemberShareInfo);
    }

    /**
     * 读卡--获取绑定的患者信息
     */
    @PostMapping(value = "/bind/card/query")
    @LogReqAndRsp
    @ApiOperation(value = "读卡--获取绑定的患者信息", produces = "application/json")
    public AbcServiceResponse<AbcListPage<PatientInfoView>> getPatientInfoWithCard(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                                   @RequestParam(value = "withPublicHealthInfo", defaultValue = "0", required = false) int withPublicHealthInfo,
                                                                                   @RequestBody PatientCardInfoReq req) {
        return new AbcServiceResponse<>(patientService.getPatientInfoWithCard(req, chainId, clinicId, withPublicHealthInfo));
    }

    /**
     * 通过读卡--新建患者
     */
    @PostMapping(value = "/with-card")
    @LogReqAndRsp
    @ApiOperation(value = "通过读卡--新建患者", produces = "application/json")
    public AbcServiceResponse<PatientInfoView> createPatientByCard(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_HIS_TYPE) Integer hisType,
                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                   @RequestBody @Valid CreatePatientByCardReq req) {
        return new AbcServiceResponse<>(patientFacade.createPatientByCard(req, chainId, clinicId, employeeId, hisType));
    }

    /**
     * 绑定卡信息给已有患者
     */
    @PutMapping(value = "/{patientId}/bind/card")
    @LogReqAndRsp
    @ApiOperation(value = "绑定卡信息给已有患者", produces = "application/json")
    public AbcServiceResponse<PatientInfoView> bindPatientCardInfo(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID, required = false) String clinicId,
                                                                   @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                   @PathVariable String patientId,
                                                                   @RequestBody BindPatientCardInfoReq req) {
        return new AbcServiceResponse<>(patientFacade.bindPatientCardInfo(req, patientId, chainId, clinicId, employeeId));
    }

    @PutMapping(value = "/bind/public-health")
    @LogReqAndRsp
    @ApiOperation(value = "患者档案绑定公卫档案", produces = "application/json")
    public AbcServiceResponse<GeneralRsp> bindPatientPublicHealthInfo(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID) String chainId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                      @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                                      @RequestBody @Valid BindPatientPublicHealthInfoReq req) {
        return new AbcServiceResponse<>(patientService.bindPatientPublicHealthInfo(req, chainId, clinicId, employeeId));
    }
}
