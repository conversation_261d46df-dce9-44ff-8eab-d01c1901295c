package cn.abcyun.cis.crm.service;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.ChargeRechargeRefundAvailableItem;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.CreateChargeSheetForThirdPartyReq;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.CreateChargeSheetForThirdPartyRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.RechargeRefundCallbackReq;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.ClinicWithAddress;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Employee;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.EmployeeView;
import cn.abcyun.bis.rpc.sdk.cis.model.mall.promotion.MallMemberDiscountPromotionDetail;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.PromotionMemberDiscountDetail;
import cn.abcyun.bis.rpc.sdk.common.model.BasicCommonRsp;
import cn.abcyun.bis.rpc.sdk.property.base.PropertyKey;
import cn.abcyun.bis.rpc.sdk.property.model.CrmPatientFamily;
import cn.abcyun.bis.rpc.sdk.property.model.CrmPatientSetting;
import cn.abcyun.bis.rpc.sdk.property.service.PropertyService;
import cn.abcyun.cis.commons.CisServiceResponseBody;
import cn.abcyun.cis.commons.amqp.message.PatientMessage;
import cn.abcyun.cis.commons.exception.CisCustomException;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamNotValidException;
import cn.abcyun.cis.commons.model.PatientTagTypeStyle;
import cn.abcyun.cis.commons.model.*;
import cn.abcyun.cis.commons.rpc.crm.PatientMemberRechargeReq;
import cn.abcyun.cis.commons.rpc.crm.PatientMemberRechargeRsp;
import cn.abcyun.cis.commons.util.*;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import cn.abcyun.cis.core.util.AESUtils;
import cn.abcyun.cis.crm.api.*;
import cn.abcyun.cis.crm.api.patient.member.PatientRelateMemberRsp;
import cn.abcyun.cis.crm.common.CommonUtil;
import cn.abcyun.cis.crm.common.CrmServiceError;
import cn.abcyun.cis.crm.common.PatientUtil;
import cn.abcyun.cis.crm.common.SysCommon;
import cn.abcyun.cis.crm.convertor.PatientConvertor;
import cn.abcyun.cis.crm.convertor.PatientMemberConvertor;
import cn.abcyun.cis.crm.dao.PatientMapper;
import cn.abcyun.cis.crm.dao.PatientMemberMapper;
import cn.abcyun.cis.crm.domain.context.PatientContext;
import cn.abcyun.cis.crm.domain.event.PatientCreateOrUpdateEvent;
import cn.abcyun.cis.crm.entity.*;
import cn.abcyun.cis.crm.exception.CrmCustomException;
import cn.abcyun.cis.crm.exception.CrmServiceException;
import cn.abcyun.cis.crm.model.*;
import cn.abcyun.cis.crm.mq.RocketMqProducer;
import cn.abcyun.cis.crm.redis.PatientCacheManager;
import cn.abcyun.cis.crm.repository.*;
import cn.abcyun.cis.crm.rpc.client.PropertyServiceClient;
import cn.abcyun.cis.crm.rpc.client.service.CisMallPromotionService;
import cn.abcyun.cis.crm.rpc.client.service.CisScClinicFeignClient;
import cn.abcyun.cis.crm.rpc.entity.PatientConfig;
import cn.abcyun.cis.crm.rpc.entity.PatientEsDto;
import cn.abcyun.cis.crm.rpc.entity.PatientImportReq;
import cn.abcyun.cis.crm.rpc.entity.PatientImportRsp;
import cn.abcyun.cis.crm.rpc.entity.open.OpenApiPatientMemberPayReq;
import cn.abcyun.cis.crm.rpc.entity.open.OpenApiPatientMemberRefundReq;
import cn.abcyun.cis.crm.rpc.entity.open.OpenApiUpdatePatientMemberReq;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import cn.abcyun.common.log.marker.AbcLogMarker;
import cn.abcyun.common.model.AbcListPage;
import cn.hutool.extra.spring.SpringUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PatientMemberService {

    @Resource
    private PatientMapper patientMapper;

    @Resource
    private PatientMemberMapper patientMemberMapper;

    @Autowired
    private PatientTagService patientTagService;

    @Autowired
    private PatientService patientService;

    @Autowired
    private ChargeService chargeService;

    @Autowired
    private AbcIdGenerator abcIdGenerator;

    @Autowired
    private PatientSourceTypeService patientSourceTypeService;

    @Autowired
    private PropertyServiceClient propertyServiceClient;

    @Autowired
    private PatientMemberRepository patientMemberRepository;

    @Autowired
    private PatientRepository patientRepository;

    @Autowired
    private PatientSnService patientSnService;

    @Autowired
    private PatientPointsService patientPointsService;

    @Autowired
    private PatientFamilyService patientFamilyService;

    @Autowired
    private ClinicService clinicService;

    @Autowired
    private PatientMemberBillRepository patientMemberBillRepository;

    @Autowired
    private PatientMigrationMemberService patientMigrationMemberService;

    @Autowired
    private CisScClinicFeignClient cisScClinicFeignClient;

    @Autowired
    private PatientMemberTypeRepository patientMemberTypeRepository;

    @Autowired
    private PatientShebaoService patientShebaoService;

    @Autowired
    private PropertyService propertyService;

    @Autowired
    private SearchService searchService;

    @Autowired
    private PatientMemberTypeService patientMemberTypeService;

    @Autowired
    private PatientMemberOrderRepository patientMemberOrderRepository;

    @Autowired
    private PromotionService promotionService;

    @Autowired
    private PatientEntityService patientEntityService;

    @Autowired
    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private RocketMqProducer rocketMqProducer;

    @Value("${abc.db-secret}")
    private String dbSecret;

    @Autowired
    private PatientCacheManager patientCacheManager;

    @Autowired
    private PatientExtendService patientExtendService;

    @Autowired
    private PatientPublicHealthInfoService publicHealthInfoService;
    @Autowired
    private CisMallPromotionService cisMallPromotionService;
    @Autowired
    private PatientMemberBillItemRepository patientMemberBillItemRepository;

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public PatientMemberInfo getPatientMemberInfo(String patientId, String chainId) {
        // memberType patientPromotion
        PatientMemberInfo memberInfo = patientMemberMapper.selectPatientMemberInfoById(chainId, patientId);

        //get discountBenefit
        if (memberInfo != null) {
            memberInfo.setCreatedClinicName(clinicService.getOrganName(memberInfo.getCreatedClinicId()));
            PatientMemberTypeInfo memberTypeInfo = memberInfo.getMemberTypeInfo();
            if (memberTypeInfo == null || memberTypeInfo.getMemberTypeId() == null) {
                log.warn("get member [{}]/[{}] type info null.", patientId, chainId);
            }
        }
        return memberInfo;
    }

    /**
     * 新增或修改患者会员
     *
     * @param patientBasicInfo 患者信息
     * @param employeeId       员工ID
     * @param requireMobile    是否需要手机号
     * @param context          患者上下文
     * @return 患者信息
     */
    @Transactional
    public PatientInfo updatePatientMember(PatientBasicInfo patientBasicInfo, String employeeId, boolean requireMobile, PatientContext context) {
        //1. 查看基本信息，确定是否修改. 当前只修改固定的字段
        PatientInfo patientInfoFromDB = patientMapper.selectPatientBasicInfoById(patientBasicInfo.getChainId(), patientBasicInfo.getId());
        if (patientInfoFromDB == null) {
            log.warn("患者 {} 不存在", patientBasicInfo.getId());
            throw new CrmCustomException(CrmServiceError.PATIENT_NOT_FOUND);
        }
        /*
            由于历史原因，所以不是所有的路径都校验手机号必填，目前只有开放平台路径会在 updatePatientMember 时校验患者必须有手机号
         */
        if (requireMobile && StringUtils.isBlank(patientInfoFromDB.getMobile())) {
            throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_MOBILE_REQUIRED);
        }

        PatientMemberType patientMemberType = patientMemberTypeService.getPatientMemberTypeById(patientBasicInfo.getMemberInfo().getMemberTypeId(), patientBasicInfo.getChainId());
        if (patientMemberType == null) {
            throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_TYPE_NOT_EXIST);
        }

        fillPatientSourceInfo(patientBasicInfo, patientInfoFromDB);

        CisPatientInfo oldPatientInfo = PatientConvertor.patientInfoToCisPatientInfo(patientInfoFromDB);

        patientBasicInfo.setMemberFlag(1);
        int rst = updatePatientInfoForPatientMember(patientInfoFromDB, patientBasicInfo);

        PatientMember memberInfoReq = patientBasicInfo.getMemberInfo();
        memberInfoReq.setPrincipal(null);
        memberInfoReq.setPresent(null);
        memberInfoReq.setBalance(null);

        //2. 更新会员信息 如果会员没有，createPatientMember
        PatientMemberInfo memberInfoFromDB = patientMemberMapper.selectAllPatientMemberInfo(patientBasicInfo.getChainId(), patientBasicInfo.getId());
        if (memberInfoFromDB == null) {
            int createRst = createPatientMember(patientBasicInfo, employeeId, false, context);
            if (createRst > 0) {
                rst = createRst;
            }
        } else {
            modifyPatientMemberInfo(memberInfoFromDB, patientBasicInfo, patientInfoFromDB.getMemberFlag(), context);
        }

        //patient-clinic
        addPatientClinicIfNoExist(patientBasicInfo);
        PatientMemberInfo memberInfo = patientMemberMapper.selectPatientMemberInfoById(patientBasicInfo.getChainId(), patientBasicInfo.getId());

        if (rst > 0) {
            CisPatientInfo newPatientInfo = PatientConvertor.patientInfoToCisPatientInfo(patientInfoFromDB);
            PatientConvertor.copyPatientInfoToCisPatientInfo(patientBasicInfo, newPatientInfo);
            rocketMqProducer.notifyPatientMessageV2(oldPatientInfo, newPatientInfo, employeeId, PatientMessage.MSG_TYPE_PATIENT_UPDATE, patientBasicInfo.getChainId(), patientBasicInfo.getClinicId());
            if (context != null) {
                context.addUpdatePatientAction(oldPatientInfo, newPatientInfo);
            }
        }
        patientInfoFromDB.setMemberInfo(memberInfo);
        patientInfoFromDB.setAge(CisPatientAge.fromBirthday(patientInfoFromDB.getBirthday()));
        return patientInfoFromDB;
    }

    private void fillPatientSourceInfo(PatientBasicInfo patientBasicInfo, PatientInfo patientInfoFromDB) {
        PatientSourceTypeService patientSourceTypeService = SpringUtil.getBean(PatientSourceTypeService.class);
        String sourceId = patientBasicInfo.getSourceId();
        String sourceFrom = patientBasicInfo.getSourceFrom();
        PatientSourceVO dbPatientSource = patientInfoFromDB.getPatientSource();
        if (TextUtils.isEmpty(sourceId) && dbPatientSource != null) {
            sourceId = dbPatientSource.getId();
            sourceFrom = dbPatientSource.getSourceFrom();
        }

        if (TextUtils.isEmpty(sourceId)) {
            return;
        }

        PatientSourceVO patientSourceVO = patientSourceTypeService.getPatientSourceVO(sourceId, patientInfoFromDB.getChainId(), sourceFrom);
        CisPatientSource patientSource = PatientConvertor.patientSourceTypeDetailVOToCIsPatientSource(patientSourceVO, patientInfoFromDB.getChainId());
        patientBasicInfo.setPatientSource(patientSource);
    }

    private void addPatientClinicIfNoExist(PatientBasicInfo patientBasicInfo) {
        PatientClinic patientClinic = patientMapper.selectPatientClinic(patientBasicInfo.getClinicId(), patientBasicInfo.getId());
        if (patientClinic == null) {
            FillUtils.fillCreatedBy(patientBasicInfo, patientBasicInfo.getLastModifiedBy());
            patientMapper.insertPatientClinic(patientBasicInfo);
        }
    }

    private void modifyPatientMemberInfo(PatientMemberInfo memberInfoFromDB,
                                         PatientBasicInfo patientBasicInfo,
                                         Integer isMemberDB,
                                         PatientContext context) throws CrmServiceException {
        //会员信息修改 三个字段: member_type_id remark memberShareInfos
        //1. 修改pm member_type_id remark
        PatientMember patientMember = patientBasicInfo.getMemberInfo();
        if (patientMember != null && (patientMember.getMemberTypeId() != null || patientMember.getRemark() != null)) {
            patientMember.setChainId(patientBasicInfo.getChainId());
            patientMember.setPatientId(patientBasicInfo.getId());
            patientMember.setStatus(1);
            patientMember.setLastModified(new Date());
            patientMember.setLastModifiedBy(patientBasicInfo.getLastModifiedBy());

            //如果是重新开通会员
            if (memberInfoFromDB.getStatus() != 1) {
                patientMember.setCreatedClinicId(patientBasicInfo.getClinicId());
                patientMember.setPrincipal(BigDecimal.ZERO);
                patientMember.setPresent(BigDecimal.ZERO);
                patientMember.setBalance(BigDecimal.ZERO);
                if (StringUtils.isEmpty(patientMember.getRemark())) {
                    patientMember.setRemark("");
                }
                patientMember.setCreated(new Date());
                patientMember.setCreatedBy(patientBasicInfo.getLastModifiedBy());
            }

            PatientMemberTypeInfo memberTypeInfoFromDB = memberInfoFromDB.getMemberTypeInfo();
            if (!StringUtils.equals(patientMember.getMemberTypeId(), memberTypeInfoFromDB.getMemberTypeId())
                    || !StringUtils.equals(patientMember.getRemark(), memberInfoFromDB.getRemark())
                    || (isMemberDB == null || isMemberDB != 1)
                    || memberInfoFromDB.getStatus() == 99) {
                patientMemberMapper.updatePatientMemberInfo(patientMember);

                if (context != null) {
                    context.setPatientMemberAction(memberInfoFromDB.getStatus() == 99 ? PatientContext.PatientMemberAction.ADD : PatientContext.PatientMemberAction.UPDATE);
                    context.setMemberTypeId(patientMember.getMemberTypeId());
                    if (context.getPatientMemberAction() == PatientContext.PatientMemberAction.UPDATE) {
                        context.setBeforePatientMember(PatientMemberConvertor.INST.toPatientMember(memberInfoFromDB));
                    }
                }
            }
        }
    }

    //会员页面基本信息修改
    private int updatePatientInfoForPatientMember(PatientInfo patientInfoFromDB, PatientBasicInfo patientBasicInfo) {
        boolean needUpdate = CommonUtil.checkNeedUpdate(patientInfoFromDB, patientBasicInfo);
        if (!needUpdate) {
            return 0;
        }

        //mobile不相同 或者 电话
        patientService.checkUniquePatient(patientInfoFromDB, patientBasicInfo);

        //检查请求sn是否修改，是否冲突
        patientSnService.checkCustomInputSnReq(patientInfoFromDB.getChainId(), patientInfoFromDB.getId(), patientBasicInfo.getLastModifiedBy(), patientBasicInfo.getSn(), patientInfoFromDB.getSn());
        patientBasicInfo.setSnNo(CommonUtil.getSnNo(patientBasicInfo.getSn()));
        setNamePinyinForPatientBasicInfo(patientBasicInfo);
        FillUtils.fillLastModifiedBy(patientBasicInfo, patientBasicInfo.getLastModifiedBy());
        int rst = patientMapper.updatePatientInfo(patientBasicInfo);
        resetPatientInfoFromDBAfterUpdate(patientBasicInfo, patientInfoFromDB);
        patientCacheManager.cleanPatientCache(patientBasicInfo.getId());
        return rst;
    }

    private void resetPatientInfoFromDBAfterUpdate(PatientBasicInfo patientBasicInfo, PatientInfo patientInfoFromDB) {
        if (patientBasicInfo.getName() != null) {
            patientInfoFromDB.setName(patientBasicInfo.getName());
            patientInfoFromDB.setNamePy(patientBasicInfo.getNamePy());
            patientInfoFromDB.setNamePyFirst(patientBasicInfo.getNamePyFirst());
        }

        if (patientBasicInfo.getMobile() != null) {
            patientInfoFromDB.setMobile(patientBasicInfo.getMobile());
        }

        if (patientBasicInfo.getBirthday() != null) {
            patientInfoFromDB.setBirthday(patientBasicInfo.getBirthday());
        }

        if (patientBasicInfo.getSex() != null) {
            patientInfoFromDB.setSex(patientBasicInfo.getSex());
        }
    }

    private int createPatientMember(PatientBasicInfo patientBasicInfo, String employeeId, boolean isImportPatient, PatientContext context) throws CrmServiceException {
        PatientMember memberInfo = patientBasicInfo.getMemberInfo();
        if (memberInfo == null || memberInfo.getMemberTypeId() == null) {
            log.info("patient member info from req is null");
            return -1;
        }

        //1. 插入会员记录
        Date now = new Date();
        memberInfo.setChainId(patientBasicInfo.getChainId());
        memberInfo.setPatientId(patientBasicInfo.getId());
        memberInfo.setStatus(1);
        memberInfo.setCreatedClinicId(patientBasicInfo.getClinicId());
        memberInfo.setCreated(now);
        memberInfo.setLastModified(now);
        memberInfo.setCreatedBy(employeeId);
        memberInfo.setLastModifiedBy(employeeId);

        if (memberInfo.getPrincipal() == null) {
            memberInfo.setPrincipal(BigDecimal.ZERO);
        }

        if (memberInfo.getPresent() == null) {
            memberInfo.setPresent(BigDecimal.ZERO);
        }
        // 保护一下setBalance值
        memberInfo.setBalance(memberInfo.getPrincipal().add(memberInfo.getPresent()));
        int rst = patientMemberMapper.insertPatientMember(memberInfo);
        if (rst == 1 && context != null) {
            context.setPatientMemberAction(PatientContext.PatientMemberAction.ADD).setMemberTypeId(memberInfo.getMemberTypeId());
        }

        //2. 修改p-memberFlag状态
        if (!isImportPatient) {
            rst = patientMapper.updatePatientMemberFlag(patientBasicInfo.getId(), (short) 1, patientBasicInfo.getLastModifiedBy());
            patientBasicInfo.setMemberFlag(1);
            patientCacheManager.cleanPatientCache(patientBasicInfo.getId());
            // 这种场景不认为是修改患者信息，因为 memberFlag 是冗余字段
//            if (context != null) {
//                context.setPatientAction(PatientContext.PatientAction.UPDATE);
//            }
        }

        return rst;
    }

    private String generatePasswordMd5Str(String password, String salt) {
        return DigestUtils.md2Hex(salt + password);
    }

    @Transactional
    public void deletePatientMember(String patientId, String chainId, String clinicId, String employeeId, PatientContext context) throws CrmServiceException {
        PatientBasicInfo patientBasicInfo = patientMemberMapper.selectPatientBasicInfoById(chainId, patientId);
        if (patientBasicInfo == null || patientBasicInfo.getMemberInfo() == null
                || patientBasicInfo.getMemberFlag() == null
                || patientBasicInfo.getMemberFlag() != 1) {
            log.info("no member for delete");
            return;
        }

        CisPatientInfo oldPatientInfo = PatientConvertor.patientBasicInfoToCisPatientInfo(patientBasicInfo);

        PatientMember memberInfo = patientBasicInfo.getMemberInfo();
        if (memberInfo == null) {
            if (patientBasicInfo.getMemberFlag() == null || patientBasicInfo.getMemberFlag() != 0) {
                //更新会员flag
                patientMemberMapper.updatePatientMemberFlag(patientId, (byte) 0, employeeId);
            }
        } else {
            if (memberInfo.getPrincipal().doubleValue() != 0.00 || memberInfo.getPresent().doubleValue() != 0.00) {
                log.warn("principal or preset or points is not zero, can't delete member");
                throw new CrmServiceException(405, "Principal or preset or points is not zero");
            }

            //1. 更新会员flag pm
            patientMemberMapper.updatePatientMemberFlag(patientId, (byte) 0, employeeId);
            PatientMember patientMember = new PatientMember();
            patientMember.setChainId(chainId);
            patientMember.setPatientId(patientId);
            patientMember.setStatus(99);
            patientMember.setPresent(BigDecimal.ZERO);
            patientMember.setPrincipal(BigDecimal.ZERO);
            patientMember.setPassword("");
            patientMember.setLastModifiedBy(employeeId);
            patientMemberMapper.updatePatientMemberInfo(patientMember);
        }
        patientCacheManager.cleanPatientCache(patientId);

        CisPatientInfo newPatientInfo = new CisPatientInfo();
        BeanUtils.copyProperties(oldPatientInfo, newPatientInfo);
        newPatientInfo.setIsMember(0);
        rocketMqProducer.notifyPatientMessageV2(oldPatientInfo, newPatientInfo, employeeId, PatientMessage.MSG_TYPE_PATIENT_UPDATE, chainId, clinicId);

        if (context != null) {
            context.setPatientMemberAction(PatientContext.PatientMemberAction.DELETE);
            context.setBeforePatientMember(memberInfo);
            // 这种场景不认为是修改患者信息，因为 memberFlag 是冗余字段
//            context.setPatientAction(PatientContext.PatientAction.UPDATE);
        }
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public PatientInfoView getPatientMember(String chainId, String patientId) {
        Patient patient = patientEntityService.getPatientById(patientId, chainId);
        if (patient == null) {
            log.warn("患者不存在 {}", patientId);
            return null;
        }

        PatientMemberInfo memberInfo = null;
        PatientMember patientMember = getPatientMemberModel(patientId, chainId);
        if (patientMember == null) {
            log.warn("患者 {} 会员信息为空", patientId);
        } else {
            PatientMemberType memberType = patientMemberTypeService.getPatientMemberTypeById(patientMember.getMemberTypeId(), chainId);
            if (memberType == null) {
                log.warn("会员卡 {} 不存在", patientMember.getMemberTypeId());
            } else {
                memberInfo = new PatientMemberInfo();
                BeanUtils.copyProperties(patientMember, memberInfo);
                memberInfo.setMemberCardId(patient.getMobile());
                memberInfo.setMemberTypeInfo(PatientMemberTypeInfo.from(memberType));
            }
        }

        PatientPoints patientPoints = patientPointsService.getPatientPoints(patientId, chainId);
        PatientInfoView patientInfoView = patientService.convertPatientInfoVO(patient);
        if (memberInfo != null) {
            patientInfoView.setMemberInfo(memberInfo);
            patientInfoView.setIsMember(1);
            patientInfoView.setMemberFlag(1);
        } else {
            patientInfoView.setIsMember(0);
            patientInfoView.setMemberFlag(0);
        }
        if (patientPoints != null && patientInfoView.getMemberInfo() != null) {
            patientInfoView.setPoints(patientPoints.getPoints());
            patientInfoView.getMemberInfo().setPoints(patientPoints.getPoints());
            patientInfoView.getMemberInfo().setPointsTotal(patientPoints.getPointsTotal());
        }
        return patientInfoView;
    }

    private PatientMember getPatientMemberModel(String patientId, String chainId) {
        return patientMemberRepository.findPatientMemberByPatientIdAndChainIdAndStatus(patientId, chainId, 1);
    }

    public PatientMemberInfo getPatientMemberInfoBasic(String chainId, String patientId) {
        return patientMemberMapper.selectPatientMemberInfoById(chainId, patientId);
    }

    public PatientInfo addPatientMember(PatientBasicInfo patientBasicInfo,
                                        String chainId,
                                        String clinicId,
                                        String employeeId,
                                        Integer hisType,
                                        PatientContext context) throws CrmCustomException {
        //请求检查
        boolean useDefaultAge = hisType != Organ.HisType.CIS_HIS_TYPE_PHARMACY;
        CommonUtil.checkMobile(patientBasicInfo.getMobile(), patientBasicInfo.getCountryCode());
        CommonUtil.checkBirthdayAndAge(patientBasicInfo.getBirthday(), patientBasicInfo.getAge(), useDefaultAge);
        if (StringUtils.isBlank(patientBasicInfo.getBirthday()) && patientBasicInfo.getAge() != null) {
            patientBasicInfo.setBirthday(CommonUtil.getBirthDayStrFromAge(patientBasicInfo.getAge(), useDefaultAge));
        }

        patientBasicInfo.setMemberFlag(1);
        FillUtils.fillCreatedBy(patientBasicInfo, employeeId);
        //如果没有患者id，就是新增患者
        if (StringUtils.isEmpty(patientBasicInfo.getId())) {
            patientBasicInfo = addPatient(patientBasicInfo, chainId, clinicId, employeeId, true, hisType, context);
            patientBasicInfo.setMemberInfo(patientBasicInfo.getMemberInfo());
            patientBasicInfo.setMemberSharePatients(patientBasicInfo.getMemberSharePatients());
        }

        if (patientBasicInfo.getMemberInfo() == null) {
            throw new CrmCustomException(CrmServiceError.BAD_PARAMETER);
        }

        patientBasicInfo.getMemberInfo().setCreatedClinicId(clinicId);
        return updatePatientMember(patientBasicInfo, employeeId, false, context);
    }

    private void setNamePinyinForPatientBasicInfo(PatientBasicInfo patientBasicInfo) {
        if (StringUtils.isNotEmpty(patientBasicInfo.getName())) {
            Pair<String, String> pair = CommonUtil.toPinyin(patientBasicInfo.getName());
            patientBasicInfo.setNamePy(pair.getRight());
            patientBasicInfo.setNamePyFirst(pair.getLeft());
        }

//        patientBasicInfo.setIdCardCipher(AESUtils.encryptToBase64String(patientBasicInfo.getIdCard(), dbSecret));
//        patientBasicInfo.setMobileCipher(AESUtils.encryptToBase64String(patientBasicInfo.getMobile(), dbSecret));
        patientBasicInfo.setIdCardLast6(CommonUtil.getIdCardLast6(patientBasicInfo.getIdCard()));
    }

    /**
     * @param patientBasicInfo
     * @param chainId
     * @param clinicId
     * @param employeeId
     * @param isNeedCheckUnique
     * @param context
     * @return
     * @throws CrmServiceException
     * @throws CrmCustomException
     */
    @Transactional(rollbackFor = Exception.class)
    public PatientBasicInfo addPatient(PatientBasicInfo patientBasicInfo,
                                       String chainId,
                                       String clinicId,
                                       String employeeId,
                                       boolean isNeedCheckUnique,
                                       Integer hisType,
                                       PatientContext context) throws CrmServiceException, CrmCustomException {
        if (StringUtils.isEmpty(patientBasicInfo.getName()) || StringUtils.isEmpty(patientBasicInfo.getSex()) || StringUtils.isEmpty(patientBasicInfo.getBirthday())) {
            throw new CrmCustomException(CrmServiceError.PARAMETER_REQUIRED);
        }
        if (isNeedCheckUnique) {
            patientService.checkUniquePatient(null, patientBasicInfo);
        }
        PatientUtil.checkInvalidPatientInfoReq(patientBasicInfo);
        setPatientBasicInfoBySheBaoCardInfo(patientBasicInfo, patientBasicInfo.getShebaoCardInfo(), null);
        patientBasicInfo.setId(abcIdGenerator.getUUID());
        patientBasicInfo.setClinicId(clinicId);
        patientBasicInfo.setChainId(chainId);
        patientBasicInfo.setCreatedClinicId(clinicId);
        patientBasicInfo.setActiveClinicId(clinicId);
        patientBasicInfo.setActiveDate(new Date());
        setNamePinyinForPatientBasicInfo(patientBasicInfo);
        patientBasicInfo.setStatus((byte) 1);
        patientBasicInfo.setMemberFlag(patientBasicInfo.getMemberFlag() == null ? 0 : patientBasicInfo.getMemberFlag());
        PatientUtil.setPatientCertificate(patientBasicInfo);
        FillUtils.fillCreatedBy(patientBasicInfo, employeeId);
        patientService.setNextSnForPatient(patientBasicInfo);
        int rst = patientMapper.insertPatient(patientBasicInfo);
        if (rst <= 0) {
            log.warn("No row affected.[{}]", rst);
        } else {
            patientMapper.insertPatientClinic(patientBasicInfo);
            patientPointsService.createPatientPoints(patientBasicInfo);
            autoUpdateMember(patientBasicInfo, clinicId, chainId, employeeId, hisType, context);

            CisPatientInfo newPatientInfo = PatientConvertor.patientBasicInfoToCisPatientInfo(patientBasicInfo);
            rocketMqProducer.notifyPatientMessageV2(null, newPatientInfo, employeeId, PatientMessage.MSG_TYPE_PATIENT_CREATED, chainId, clinicId);
            if (context != null) {
                context.setChainId(chainId).setClinicId(clinicId).addCreatePatientAction(newPatientInfo);
            }
        }

        return patientBasicInfo;
    }

    //todo
    public PatientAvailableMemberInfoVO getMemberSharedPatientMemberInfo(String chainId, String sharedPatientId, String clinicId) {
        if (SysCommon.UNKNOWN_ID.equals(sharedPatientId)) {
            throw new NotFoundException("not fount patient");
        }
        //获取
        Patient patientById = patientService.getPatientById(sharedPatientId, chainId);
        if (Objects.isNull(patientById)) {
            throw new NotFoundException("not fount patient");
        }
        PatientMemberInfo memberInfo = null;
        if (patientById.getIsMember() == 1) {
            // 自己就是会员
            memberInfo = patientMemberMapper.selectPatientMemberInfoById(chainId, sharedPatientId);
        } else {
            // 查询家长是否会员
            PatientFamily patientFamily = patientFamilyService.getPatientFamilyByPatientIdAndChainId(sharedPatientId, chainId);
            if (Objects.nonNull(patientFamily) && StringUtils.isNotBlank(patientFamily.getParentId())) {
                // 表示他是家庭成员，查询家长是否会员
                Patient parentPatient = patientService.getPatientById(patientFamily.getParentId(), chainId);
                if (Objects.isNull(parentPatient)) {
                    throw new CrmServiceException(CrmServiceError.PARENT_PATIENT_NOT_FOUND);
                }
                if (parentPatient.getIsMember() == 1) {
                    memberInfo = patientMemberMapper.selectPatientMemberInfoById(chainId, parentPatient.getId());
                }
            }
        }

        PatientAvailableMemberInfoVO patientInfo = new PatientAvailableMemberInfoVO();
        patientInfo.setId(sharedPatientId);
        patientInfo.setChainId(chainId);
        patientInfo.setMemberInfo(memberInfo);
        return patientInfo;
    }

    //如果是共享人，需要具有特殊的标记，isMember=2
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public FamilyMemberShare getMemberSharedBasicPatientInfo(String chainId, String sharedPatientId) {
        FamilyMemberShare familyMemberShare = new FamilyMemberShare();
        familyMemberShare.setChainId(chainId);
        familyMemberShare.setStatus(1);
        familyMemberShare.setSharedPatientId(sharedPatientId);
        PatientFamilyListView patientFamilyListView = patientFamilyService.getPatientFamilyList(sharedPatientId, chainId);
        PatientFamilyView mainPatientFamilyView = null;
        if (Objects.nonNull(patientFamilyListView) && !CollectionUtils.isEmpty(patientFamilyListView.getFamilyPatients())) {
            for (PatientFamilyView familyPatient : patientFamilyListView.getFamilyPatients()) {
                if (familyPatient.getPatientId().equals(sharedPatientId)) {
                    mainPatientFamilyView = familyPatient;
                }
            }
            if (Objects.isNull(mainPatientFamilyView)) {
                return familyMemberShare;
            }
            // 如果本身是会员
            if (mainPatientFamilyView.getIsMember() == 1) {
                familyMemberShare.setSharedPatientName(mainPatientFamilyView.getName());
                familyMemberShare.setSharedPatientId(mainPatientFamilyView.getPatientId());
                return familyMemberShare;
            }
            // 患者为家庭成员,并且家长是会员
            if (mainPatientFamilyView.getFamilyRole() == 2) {
                for (PatientFamilyView familyPatient : patientFamilyListView.getFamilyPatients()) {
                    if (familyPatient.getFamilyRole() == 1 && familyPatient.getIsMember() == 1) {
                        familyMemberShare.setPatientId(familyPatient.getPatientId());
                        familyMemberShare.setPatientName(familyPatient.getName());
                        familyMemberShare.setSharedPatientName(mainPatientFamilyView.getName());
                        familyMemberShare.setSharedPatientId(mainPatientFamilyView.getPatientId());
                        return familyMemberShare;
                    }
                }
            }
        }
        return familyMemberShare;
    }

    // 查询家庭可支付人信息
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public PatientMemberInfo getFamilyMemberInfo(String chainId, String sharedPatientId) {
        FamilyMemberShare familyMemberShare = new FamilyMemberShare();
        familyMemberShare.setChainId(chainId);
        familyMemberShare.setStatus(1);
        familyMemberShare.setSharedPatientId(sharedPatientId);
        PatientFamilyListView patientFamilyListView = patientFamilyService.getPatientFamilyList(sharedPatientId, chainId);
        PatientFamilyView mainPatientFamilyView = null;
        if (Objects.nonNull(patientFamilyListView) && !CollectionUtils.isEmpty(patientFamilyListView.getFamilyPatients())) {
            for (PatientFamilyView familyPatient : patientFamilyListView.getFamilyPatients()) {
                if (familyPatient.getPatientId().equals(sharedPatientId)) {
                    mainPatientFamilyView = familyPatient;
                }
            }
            if (Objects.isNull(mainPatientFamilyView)) {
                return new PatientMemberInfo();
            }
            // 如果本身是会员
            if (mainPatientFamilyView.getIsMember() == 1) {
                PatientMemberInfo patientMemberInfoById = getPatientMemberInfoById(chainId, mainPatientFamilyView.getPatientId());
                return patientMemberInfoById;
            }

            // 患者为家庭成员,并且家长是会员
            if (mainPatientFamilyView.getFamilyRole() == 2) {
                for (PatientFamilyView familyPatient : patientFamilyListView.getFamilyPatients()) {
                    if (familyPatient.getFamilyRole() == 1 && familyPatient.getIsMember() == 1) {
                        PatientMemberInfo patientMemberInfoById = getPatientMemberInfoById(chainId, familyPatient.getPatientId());
                        return patientMemberInfoById;
                    }
                }
            }
        }
        return new PatientMemberInfo();
    }

    @Transactional(rollbackFor = Exception.class)
    public PatientInfo createOrUpdatePatient(PatientBasicInfo patientBasicInfo,
                                             String chainId,
                                             String clinicId,
                                             String employeeId,
                                             Integer hisType,
                                             PatientContext context) throws NotFoundException, CrmCustomException {
        PatientInfo oldPatientInfo = this.getMatchedPatientInfo(patientBasicInfo, chainId);
        this.setPatientBasicInfo(patientBasicInfo, chainId, clinicId, employeeId, oldPatientInfo, hisType);
        this.setPatientBasicInfoBySheBaoCardInfo(patientBasicInfo, patientBasicInfo.getShebaoCardInfo(), oldPatientInfo);
        // 主要是校验格式的正确性
        PatientUtil.checkInvalidPatientInfoReq(patientBasicInfo);
        //创建或更新患者信息
        int updateRet = 0;
        if (oldPatientInfo == null) {
            this.addPatient(patientBasicInfo, chainId, clinicId, employeeId, false, hisType, context);
        } else {
            //rpc 调用 导入患者，不再更新患者年龄
            if (patientBasicInfo.getImportFlag() == 1 && StringUtils.isNotEmpty(oldPatientInfo.getBirthday())) {
                patientBasicInfo.setBirthday(null);
            }
            updateRet = this.updatePatient(oldPatientInfo, patientBasicInfo, employeeId);
        }
        // 更新患者扩展字段
        patientExtendService.updatePatientExtendByPatientBasicInfo(patientBasicInfo, chainId, employeeId);

        //更新患者tag信息
        updatePatientTags(patientBasicInfo, chainId, employeeId);

        //更新患者社保卡信息
        PatientShebaoInfo patientShebaoInfo = patientShebaoService.insertOrUpdatePatientShebaoInfo(patientBasicInfo.getShebaoCardInfo(), patientBasicInfo.getId(), chainId, employeeId);

        //返回患者信息
        PatientInfo patientInfo;
        if (patientBasicInfo.getImportFlag() != 1) {
            patientInfo = patientService.getPatientInfo(null, chainId, clinicId, patientBasicInfo.getId(), false);
        } else {
            patientInfo = new PatientInfo(patientBasicInfo);
            if (oldPatientInfo != null && StringUtils.isNotEmpty(oldPatientInfo.getBirthday())) {
                patientInfo.setBirthday(oldPatientInfo.getBirthday());
            }
        }

        if (patientShebaoInfo != null) {
            ShebaoCardInfo shebaoCardInfo = new ShebaoCardInfo();
            shebaoCardInfo.setName(patientInfo.getName());
            shebaoCardInfo.setSex(patientInfo.getSex());
            shebaoCardInfo.setBirthday(patientInfo.getBirthday());
            shebaoCardInfo.setIdCardType(patientInfo.getIdCardType());
            shebaoCardInfo.setIdCardNo(patientInfo.getIdCard());
            BeanUtils.copyProperties(patientShebaoInfo, shebaoCardInfo);
            patientInfo.setShebaoCardInfo(shebaoCardInfo);
        }

        if (patientBasicInfo.isNeedMatchPublicHealthInfo()) {
            publicHealthInfoService.bindPatientInfo(patientInfo, patientBasicInfo.getPublicHealthInfo(), clinicId);
        }

        if (updateRet > 0) {
            CisPatientInfo oldCisPatientInfo = PatientConvertor.patientInfoToCisPatientInfo(oldPatientInfo);
            CisPatientInfo newPatientInfo = PatientConvertor.patientInfoToCisPatientInfo(patientInfo);
            rocketMqProducer.notifyPatientMessageV2(oldCisPatientInfo, newPatientInfo, employeeId, PatientMessage.MSG_TYPE_PATIENT_UPDATE, oldPatientInfo.getChainId(), clinicId);
            if (context != null) {
                context.addUpdatePatientAction(oldCisPatientInfo, newPatientInfo);
            }
        }

        if (oldPatientInfo == null || updateRet > 0) {
            //更新患者收货地址信息
            Patient addressPatient = new Patient();
            BeanUtils.copyProperties(patientBasicInfo, addressPatient);
            applicationEventPublisher.publishEvent(new PatientCreateOrUpdateEvent().setPatient(addressPatient).setOperatorId(employeeId));
        }

        return patientInfo;
    }

    private void updatePatientTags(PatientBasicInfo patientBasicInfo, String chainId, String employeeId) {
        List<CisPatientTag> tags = patientBasicInfo.getTags();
        if (!CollectionUtils.isEmpty(tags)) {
            patientTagService.createOrUpdatePatientTags(Collections.singletonList(patientBasicInfo.getId()), tags, chainId, employeeId);
        }
    }

    /**
     * 匹配已经存在的患者信息
     * 1. 姓名+手机号
     * 2. 姓名+证件号
     */
    private PatientInfo getMatchedPatientInfo(PatientBasicInfo patientBasicInfo, String chainId) {
        PatientInfo oldPatientInfo = null;
        // 1. 通过id匹配
        if (StringUtils.isNotEmpty(patientBasicInfo.getId())) {
            oldPatientInfo = patientMapper.selectPatientBasicInfoById(chainId, patientBasicInfo.getId());
            if (oldPatientInfo == null) {
                throw new CrmCustomException(CrmServiceError.PATIENT_NOT_FOUND);
            }
        }
        // 2. 通过姓名+手机号匹配
        if (oldPatientInfo == null && StringUtils.isNotBlank(patientBasicInfo.getMobile())) {
            oldPatientInfo = patientMapper.selectPatientInfoByNameAndMobileBasic(chainId, patientBasicInfo.getName(), patientBasicInfo.getMobile(), null);
        }
        // 3. 通过姓名+证件号匹配
        if (oldPatientInfo == null && StringUtils.isNotBlank(patientBasicInfo.getIdCard())) {
            oldPatientInfo = patientMapper.selectPatientBasicInfoByIdCard(chainId, patientBasicInfo.getName(), patientBasicInfo.getIdCard(), null);
        }
        return oldPatientInfo;
    }

    //更新会员的患者基本信息
    @Transactional(rollbackFor = Exception.class)
    public int updatePatient(PatientInfo oldPatientInfo, PatientBasicInfo patientBasicInfo, String employeeId) throws CrmCustomException {
//        checkWxOpenIdUpdate(patientBasicInfo.getWxOpenId(), oldPatientInfo.getWxOpenId(), patientBasicInfo.getChainId());
        patientBasicInfo.setId(oldPatientInfo.getId());
        setNamePinyinForPatientBasicInfo(patientBasicInfo);
        boolean needUpdate = CommonUtil.checkNeedUpdate(oldPatientInfo, patientBasicInfo);
        if (!needUpdate) {
            return 0;
        } else {
            FillUtils.fillLastModifiedBy(patientBasicInfo, employeeId);
        }
        PatientUtil.setPatientCertificate(patientBasicInfo);
        patientSnService.checkCustomInputSnReq(oldPatientInfo.getChainId(), oldPatientInfo.getId(), employeeId, patientBasicInfo.getSn(), oldPatientInfo.getSn());
        patientBasicInfo.setSnNo(CommonUtil.getSnNo(patientBasicInfo.getSn()));
        int ret = patientMapper.updatePatientInfo(patientBasicInfo);
        patientCacheManager.cleanPatientCache(oldPatientInfo.getId());
        if (ret <= 0) {
            log.warn("no update patient info. ret [{}]", ret);
        } else {
            FillUtils.fillCreatedBy(patientBasicInfo, employeeId);
            addPatientClinicIfNoExist(patientBasicInfo);
            patientPointsService.updatePatientPoints(patientBasicInfo);
        }
        return ret;
    }

//    private void checkWxOpenIdUpdate(String wxOpenIdReq, String oldWxOpenId, String chainId) throws CrmCustomException {
//        if (StringUtils.isEmpty(wxOpenIdReq)) {
//            return;
//        }
//
//        if (StringUtils.isNotEmpty(oldWxOpenId)) {
//            if (!StringUtils.equals(wxOpenIdReq, oldWxOpenId)) {
//                throw new CrmCustomException(CrmServiceError.PATIENT_WX_EXIST);
//            } else {
//                //未更新，不做检查，直接放过
//                return;
//            }
//        }
//
//        int wxBindPatientCount = patientRepository.countAllByWxOpenIdAndChainIdAndStatus(wxOpenIdReq, chainId, 1);
//        if (wxBindPatientCount > 10) {
//            throw new CrmCustomException(CrmServiceError.PATIENT_WX_BIND_LIMIT);
//        }
//    }

    private void setPatientBasicInfoBySheBaoCardInfo(PatientBasicInfo patientBasicInfo, ShebaoCardInfo shebaoCardInfo, PatientInfo oldPatientInfo) {
        if (shebaoCardInfo == null) {
            return;
        }

        if (!CommonUtil.isValidPhoneNumber(shebaoCardInfo.getMobile())) {
            shebaoCardInfo.setMobile(null);
        }

        if (oldPatientInfo == null) {
            if (StringUtils.isEmpty(patientBasicInfo.getName())) {
                patientBasicInfo.setName(shebaoCardInfo.getName());
            }

            if (StringUtils.isEmpty(patientBasicInfo.getSex())) {
                patientBasicInfo.setSex(shebaoCardInfo.getSex());
            }

            if (StringUtils.isEmpty(patientBasicInfo.getIdCardType())) {
                patientBasicInfo.setIdCardType(shebaoCardInfo.getIdCardType());
            }

            if (StringUtils.isEmpty(patientBasicInfo.getIdCard())) {
                patientBasicInfo.setIdCard(shebaoCardInfo.getIdCardNo());
            }

            if (StringUtils.isEmpty(patientBasicInfo.getBirthday())) {
                patientBasicInfo.setBirthday(shebaoCardInfo.getBirthday());
            }

            if (StringUtils.isEmpty(patientBasicInfo.getMobile())) {
                patientBasicInfo.setMobile(shebaoCardInfo.getMobile());
            }
        } else {
            if (StringUtils.isEmpty(oldPatientInfo.getSex())) {
                patientBasicInfo.setSex(shebaoCardInfo.getSex());
            }

            if (StringUtils.isEmpty(oldPatientInfo.getIdCardType())) {
                patientBasicInfo.setIdCardType(shebaoCardInfo.getIdCardType());
            }

            if (StringUtils.isEmpty(oldPatientInfo.getIdCard())) {
                patientBasicInfo.setIdCard(shebaoCardInfo.getIdCardNo());
            }

            if (StringUtils.isEmpty(oldPatientInfo.getBirthday())) {
                patientBasicInfo.setBirthday(shebaoCardInfo.getBirthday());
            }

            if (StringUtils.isEmpty(oldPatientInfo.getMobile())) {
                patientBasicInfo.setMobile(shebaoCardInfo.getMobile());
            }
        }
    }

    private void setPatientBasicInfo(PatientBasicInfo patientBasicInfo, String chainId, String clinicId, String employeeId, PatientInfo oldPatientInfo, Integer hisType) {
        if (patientBasicInfo.getPatientSource() != null) {
            //insert Or update 请求，不覆盖患者来源信息
            if (oldPatientInfo == null || oldPatientInfo.getPatientSource() == null || StringUtils.isEmpty(oldPatientInfo.getPatientSource().getId())) {
                patientBasicInfo.setSourceId(patientBasicInfo.getPatientSource().getId());
                patientBasicInfo.setSourceFrom(patientBasicInfo.getPatientSource().getSourceFrom());
            }
        }

        patientBasicInfo.setChainId(chainId);
        patientBasicInfo.setClinicId(clinicId);

        String birthday = CommonUtil.getBirthday(patientBasicInfo.getAge(), patientBasicInfo.getBirthday(), hisType != Organ.HisType.CIS_HIS_TYPE_PHARMACY);
        patientBasicInfo.setBirthday(birthday);

        CisPatientAddress address = patientBasicInfo.getAddress();
        if (address != null) {
            BeanUtils.copyProperties(address, patientBasicInfo);
        }

        ShebaoCardInfo shebaoCardInfo = patientBasicInfo.getShebaoCardInfo();
        if (shebaoCardInfo != null) {
            if (StringUtils.isEmpty(patientBasicInfo.getMobile()) || !CommonUtil.isValidPhoneNumber(patientBasicInfo.getMobile(), patientBasicInfo.getCountryCode())) {
                shebaoCardInfo.setMobile(null);
            }

            if (oldPatientInfo == null || StringUtils.equals(shebaoCardInfo.getName(), oldPatientInfo.getName())) {
                patientBasicInfo.setShebaoCardInfo(shebaoCardInfo);
            } else {
                patientBasicInfo.setShebaoCardInfo(null);
            }
        }
    }

    @Transactional
    public void deletePatientMemberList(List<String> patientIds, String chainId) {
        int ret = patientMemberMapper.updatePatientMemberListStatus(patientIds, chainId, 99);
        log.info("delete patient member. chainId-[{}], paitentIds-[{}], ret-[{}]", chainId, patientIds, ret);
    }

    public List<PatientMemberInfo> getPatientMemberInfoList(List<String> patientIds, String chainId) {
        return patientMemberMapper.selectPatientMemberInfoListByPatientIds(patientIds, chainId);
    }

    public void insertOrUpdatePatientMemberMerged(PatientMember patientMember) {
        int ret = patientMemberMapper.insertOrUpdatePatientMemberMerged(patientMember);
        log.info("insert or update patient member [{}]", ret);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public PatientMemberInfo getPatientMemberInfoById(String chainId, String patientId) {
        return getPatientMemberInfoById(chainId, patientId, null);
    }

    /**
     * 查询会员信息
     *
     * @param chainId          连锁ID
     * @param patientId        患者ID
     * @param needDiscountInfo 是否需要折扣信息 null/1:需要;0:不需要
     * @return 患者会员信息
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public PatientMemberInfo getPatientMemberInfoById(String chainId, String patientId, Integer needDiscountInfo) {
        PatientMemberInfo patientMemberInfo = patientMemberMapper.selectPatientMemberInfoById(chainId, patientId);
        if (patientMemberInfo != null && patientMemberInfo.getMemberTypeInfo() != null) {
            if (needDiscountInfo == null || needDiscountInfo == 1) {
                patientMemberInfo.setCreatedClinicName(clinicService.getOrganName(patientMemberInfo.getCreatedClinicId()));
                PromotionMemberDiscountDetail discountDetail = promotionService.getPromotionMemberDiscount(patientMemberInfo.getMemberTypeInfo().getMemberTypeId(), chainId);
                if (discountDetail != null) {
                    patientMemberInfo.getMemberTypeInfo().setDiscountBenefits(discountDetail.getDiscountBenefits());
                }
                MallMemberDiscountPromotionDetail mallDiscountDetail = cisMallPromotionService.getPromotionMemberDiscount(patientMemberInfo.getMemberTypeInfo().getMemberTypeId(), chainId);
                if (Objects.nonNull(mallDiscountDetail)) {
                    patientMemberInfo.getMemberTypeInfo().setMallDiscountBenefits(mallDiscountDetail.getDiscountBenefitsDisplay());
                    patientMemberInfo.getMemberTypeInfo().setMallDiscountBenefitsList(mallDiscountDetail.getDiscountBenefitsList());
                }

            }
        }
        return patientMemberInfo;
    }

    @Transactional
    public PatientMemberRechargeOrderRsp createPatientMemberRechargeOrder(PatientMemberRechargeOrderReq req) {

        String chainId = req.getChainId();
        String clinicId = req.getClinicId();
        String patientId = req.getPatientId();

        req.setPrincipal(req.getPrincipal() == null ? BigDecimal.ZERO : req.getPrincipal());
        req.setPresent(req.getPresent() == null ? BigDecimal.ZERO : req.getPresent());

        if (req.getPresent().compareTo(BigDecimal.ZERO) == 0 && req.getPrincipal().compareTo(BigDecimal.ZERO) == 0) {
            log.warn("不允许充值0元");
            throw new CrmCustomException(CrmServiceError.BAD_PARAMETER);
        }

        setPatientMemberRechargePresent(req);

        //查找会员
        PatientInfo patientInfo = patientService.getPatientBasicInfoByPatientId(chainId, patientId);
        if (patientInfo == null || patientInfo.getIsMember() != 1) {
            throw new CrmCustomException(CrmServiceError.PATIENT_NOT_FOUND);
        }

        //生成订单
        PatientMemberOrder patientMemberOrder = doCreatePatientMemberRechargeOrder(chainId, clinicId, req);
        if (patientMemberOrder == null) {
            throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_ORDER_CREATE_FAILED);
        }

        CreateChargeSheetForThirdPartyRsp createChargeSheetForThirdPartyRsp = null;
        try {
            createChargeSheetForThirdPartyRsp = chargeService.createMemberRechargeOrderToCharge(patientMemberOrder, patientInfo);
        } finally {
            updatePatientMemberRechargeOrderAfterCallChargeApi(patientMemberOrder, createChargeSheetForThirdPartyRsp);
        }

        PatientMemberRechargeOrderRsp memberChargeOrderRsp = new PatientMemberRechargeOrderRsp();
        memberChargeOrderRsp.setPatientId(patientMemberOrder.getPatientId());
        memberChargeOrderRsp.setChainId(patientMemberOrder.getChainId());
        memberChargeOrderRsp.setClinicId(patientMemberOrder.getClinicId());
        memberChargeOrderRsp.setMemberRechargeOrderId(patientMemberOrder.getId());
        memberChargeOrderRsp.setChargeSheetId(createChargeSheetForThirdPartyRsp.getChargeSheetId());
        memberChargeOrderRsp.setStatus(patientMemberOrder.getStatus());
        return memberChargeOrderRsp;
    }

    private void setPatientMemberRechargePresent(PatientMemberRechargeOrderReq req) {
        if (req.getOrderSource() != PatientMemberOrder.OrderSource.WE_CLINIC) {
            return;
        }

        CrmPatientSetting crmPatientSetting = propertyService.getPropertyValueByKey(PropertyKey.CRM_PATIENT, req.getChainId(), CrmPatientSetting.class);
        if (crmPatientSetting == null
                || crmPatientSetting.getEnableMemberStepRecharge() == 0
                || crmPatientSetting.getMemberStepRechargeRules() == null) {
            return;
        }

        List<PatientMemberStepRechargeRule> rules = JsonUtils.readValue(crmPatientSetting.getMemberStepRechargeRules(), new TypeReference<List<PatientMemberStepRechargeRule>>() {
        });
        if (CollectionUtils.isEmpty(rules)) {
            return;
        }

//        List<PatientMemberStepRechargeRule> sortedRules = rules.stream()
//                .filter(item -> item.getPrincipal() != null)
//                .sorted(Comparator.comparing(PatientMemberStepRechargeRule::getPrincipal))
//                .collect(Collectors.toList());

//        PatientMemberStepRechargeRule rechargeRule = null;
//        for (PatientMemberStepRechargeRule sortedRule : sortedRules) {
//            if (req.getPrincipal().compareTo(sortedRule.getPrincipal()) == 0) {
//                rechargeRule = sortedRule;
//                break;
//            } else if (req.getPrincipal().compareTo(sortedRule.getPrincipal()) > 0) {
//                rechargeRule = sortedRule;
//            } else {
//                break;
//            }
//        }

        PatientMemberStepRechargeRule rechargeRule = rules.stream()
                .filter(item -> item.getPrincipal() != null && item.getPrincipal().compareTo(req.getPrincipal()) == 0)
                .findFirst().orElse(null);
        if (rechargeRule != null && rechargeRule.getPresent() != null) {
            req.setPresent(rechargeRule.getPresent());
        }
    }

    private void updatePatientMemberRechargeOrderAfterCallChargeApi(PatientMemberOrder patientMemberOrder,
                                                                    CreateChargeSheetForThirdPartyRsp createChargeSheetForThirdPartyRsp) {

        Date newLastModified = new Date();
        if (createChargeSheetForThirdPartyRsp == null) {
            patientMemberOrder.setStatus(PatientMemberOrder.OrderStatus.CALL_API_FAILED);
        } else {
            patientMemberOrder.setChargeSheetId(createChargeSheetForThirdPartyRsp.getChargeSheetId());
        }

        int ret = patientMemberMapper.updatePatientMemberOrder(patientMemberOrder, newLastModified);
        if (ret <= 0) {
            log.warn("update patient member order failed. [{}] [{}]", ret, patientMemberOrder);
        }
    }

    private PatientMemberOrder doCreatePatientMemberRechargeOrder(String chainId,
                                                                  String clinicId,
                                                                  PatientMemberRechargeOrderReq memberRechargeOrderReq) {
        PatientMemberOrder patientMemberOrder = new PatientMemberOrder();
        patientMemberOrder.setId(abcIdGenerator.getUUID());
        patientMemberOrder.setChainId(chainId);
        patientMemberOrder.setClinicId(clinicId);
        patientMemberOrder.setPatientId(memberRechargeOrderReq.getPatientId());
        patientMemberOrder.setPrincipal(memberRechargeOrderReq.getPrincipal());
        patientMemberOrder.setPresent(memberRechargeOrderReq.getPresent() == null ? BigDecimal.ZERO : memberRechargeOrderReq.getPresent());
        patientMemberOrder.setStatus(PatientMemberOrder.OrderStatus.NO_PAY);
        patientMemberOrder.setOrderSource(memberRechargeOrderReq.getOrderSource());
        Date now = new Date();
        patientMemberOrder.setCreated(now);
        patientMemberOrder.setLastModified(now);
        patientMemberOrder.setCreatedBy(memberRechargeOrderReq.getOperatorId());
        patientMemberOrder.setSellerUserId(memberRechargeOrderReq.getSellerUserId());
        patientMemberOrder.setChargeComment(memberRechargeOrderReq.getChargeComment());
        int ret = patientMemberMapper.insertPatientMemberOrder(patientMemberOrder);
        if (ret <= 0) {
            log.error("create patient member order failed.");
            return null;
        }

        return patientMemberOrder;
    }

    @Transactional
    public PatientMemberRechargeRsp updatePatientMemberRechargeOrderByChargeSheetId(String chargeSheetId, PatientMemberRechargeReq memberRechargeReq) {
        PatientMemberRechargeRsp rechargeRsp = new PatientMemberRechargeRsp();
        PatientMemberOrder patientMemberOrder = patientMemberMapper.selectPatientMemberOrderByChargeSheetId(chargeSheetId);
        if (patientMemberOrder == null || patientMemberOrder.getStatus() != PatientMemberOrder.OrderStatus.NO_PAY) {
            log.error("update patient member order failed. {} / {}", patientMemberOrder, memberRechargeReq);
            rechargeRsp.setRet(PatientMemberRechargeRsp.RetCode.FAILED);
            rechargeRsp.setMessage("重复更新订单！");
            return rechargeRsp;
        }

        if (memberRechargeReq.getAmount().compareTo(patientMemberOrder.getPrincipal()) != 0) {
            log.error("error patient member order {} / {}", patientMemberOrder, memberRechargeReq);
            throw new CrmCustomException(CrmServiceError.BAD_PARAMETER);
        }

        PatientInfo patientInfo = patientService.getPatientBasicInfoByPatientId(patientMemberOrder.getChainId(), patientMemberOrder.getPatientId());

        PatientMemberBill memberBill = doPatientMemberRecharge(patientMemberOrder, memberRechargeReq, rechargeRsp);
        if (memberBill != null && memberBill.getPrincipal().equals(memberRechargeReq.getAmount())) {
            sendMemberRechargeSuccessMsg(memberBill, patientInfo);
        }
        rechargeRsp.setRet(PatientMemberRechargeRsp.RetCode.SUCCESS);
        return rechargeRsp;
    }

    private void sendMemberRechargeSuccessMsg(PatientMemberBill memberBill, PatientInfo patientInfo) {
        try {
            ClinicWithAddress clinicWithAddress = clinicService.queryClinicInfo(memberBill.getClinicId());
            if (clinicWithAddress == null || StringUtils.isEmpty(clinicWithAddress.getName())) {
                return;
            }
//            patientMessageProducer.sendMemberRechargeSuccessSmsMsg(memberBill, clinicWithAddress.getName(), patientInfo);
//            patientMessageProducer.sendMemberRechargeSuccessWxMsg(memberBill, clinicWithAddress.getName(), patientInfo);
            rocketMqProducer.sendMemberRechargeSuccessSmsMsg(memberBill, clinicWithAddress.getName(), patientInfo);
            rocketMqProducer.sendMemberRechargeSuccessWxMsg(memberBill, clinicWithAddress.getName(), patientInfo);
        } catch (Exception e) {
            log.warn("send message recharge success msg failed. [{}]", memberBill, e);
        }
    }

    private PatientMemberBill doPatientMemberRecharge(PatientMemberOrder patientMemberOrder,
                                                      PatientMemberRechargeReq memberRechargeReq,
                                                      PatientMemberRechargeRsp rechargeRsp) {

        String memberBillId = abcIdGenerator.getUUID();

        Date newLastModified = new Date();
        //1. 更新订单状态
        if (memberRechargeReq.getPayStatus() == PatientMemberRechargeReq.PayStatus.SUCCESS) {
            patientMemberOrder.setStatus(PatientMemberOrder.OrderStatus.PAY);
        } else {
            patientMemberOrder.setStatus(PatientMemberOrder.OrderStatus.FAILED);
        }

        //强保证，必须更新成功后才有后面的更新，且只可能会被更新1次
        patientMemberOrder.setMemberBillId(memberBillId);
        int ret = patientMemberMapper.updatePatientMemberOrder(patientMemberOrder, newLastModified);
        if (ret <= 0 || patientMemberOrder.getStatus() != PatientMemberOrder.OrderStatus.PAY) {
            log.warn("update patient member recharge order failed. [{}] [{}]", ret, patientMemberOrder);
            rechargeRsp.setRet(PatientMemberRechargeRsp.RetCode.SUCCESS);
            return null;
        }

        //2. 充值
        String chainId = memberRechargeReq.getChainId();
        String patientId = memberRechargeReq.getPatientId();
        BigDecimal amount = memberRechargeReq.getAmount();
        log.info("本金充值金额 [{}]", amount);
        int updatePatientMemberRet = patientMemberMapper.updatePatientMemberBalance(chainId, patientId, amount, patientMemberOrder.getPresent(), null);
        if (updatePatientMemberRet <= 0) {
            log.warn("update patient member balance failed. [{}] [{}]", updatePatientMemberRet, memberRechargeReq);
            rechargeRsp.setRet(PatientMemberRechargeRsp.RetCode.FAILED);
            return null;
        }

        //3. 记账
        PatientMemberInfo patientMemberInfo = patientMemberMapper.selectPatientMemberInfo(chainId, patientId);
        PatientMemberBill memberBill = createPatientMemberBill(patientMemberOrder, memberRechargeReq, patientMemberInfo);
        log.warn("update patient member balance success. [{}]", memberBill);
        return memberBill;
    }

    private PatientMemberBill createPatientMemberBill(PatientMemberOrder patientMemberOrder,
                                                      PatientMemberRechargeReq memberRechargeReq,
                                                      PatientMemberInfo patientMemberInfo) {

        PatientMemberBill memberBill = new PatientMemberBill();
        memberBill.setId(patientMemberOrder.getMemberBillId());
        memberBill.setChainId(patientMemberOrder.getChainId());
        memberBill.setClinicId(patientMemberOrder.getClinicId());
        memberBill.setAction(PatientMemberBill.Action.MEMBER_RECHARGE);
        memberBill.setBusinessId(memberRechargeReq.getChargeTransactionId());
        memberBill.setBusinessType(PatientMemberBill.BusinessType.CHARGE);
        memberBill.setChargeSheetId(patientMemberOrder.getChargeSheetId());
        memberBill.setPatientOrderId(memberRechargeReq.getPatientOrderId());
        memberBill.setPatientId(patientMemberOrder.getPatientId());
        memberBill.setType(PatientMemberBill.Type.IN);
        memberBill.setPrincipal(memberRechargeReq.getAmount());
        memberBill.setPresent(patientMemberOrder.getPresent());
        memberBill.setPayMode(memberRechargeReq.getPayMode());
        memberBill.setPaySubMode(memberRechargeReq.getPaySubMode());
        memberBill.setPrincipalBalance(patientMemberInfo.getPrincipal());
        memberBill.setPresentBalance(patientMemberInfo.getPresent());
        memberBill.setChargeComment(patientMemberOrder.getChargeComment());
        memberBill.setStatus(PatientMemberBill.Status.SUCCESS);

        if (StringUtils.isNotBlank(patientMemberOrder.getSellerUserId()) && !SysCommon.UNKNOWN_ID.equals(patientMemberOrder.getSellerUserId())) {
            String employeeName = clinicService.getEmployeeName(patientMemberOrder.getChainId(), patientMemberOrder.getSellerUserId());
            memberBill.setSellerUserName(employeeName);
        }

        if (StringUtils.isBlank(memberBill.getSellerUserName())) {
            memberBill.setSellerUserName("未指定");
        }
        memberBill.setSellerUserId(patientMemberOrder.getSellerUserId());
        memberBill.setThirdPartTransactionId(memberRechargeReq.getThirdPartTransactionId());
        memberBill.setCreated(new Date());
        if (StringUtils.isNotEmpty(patientMemberOrder.getCreatedBy())) {
            memberBill.setCreatedBy(patientMemberOrder.getCreatedBy());
        } else {
            memberBill.setCreatedBy("00000000000000000000000000000000");
        }
        memberBill.setPaySource(patientMemberOrder.getOrderSource());
        memberBill.setChargeTransactionId(memberRechargeReq.getChargeTransactionId());
        memberBill.setTransactionPatientId(patientMemberOrder.getPatientId());

        int ret = patientMemberMapper.insertPatientMemberBill(memberBill);
        if (ret <= 0) {
            log.warn("insert patient member bill failed. [{}]", memberBill);
            return null;
        }
        return memberBill;
    }

    @Transactional
    public PatientImportRsp importPatient(PatientImportReq patientImportReq, PatientContext context) throws CrmCustomException {

        checkPatientImportReq(patientImportReq);
        String chainId = patientImportReq.getChainId();
        String clinicId = patientImportReq.getCreatedClinicId();
        String employeeId = SysCommon.UNKNOWN_ID;

        PatientBasicInfo patientBasicInfo = convertToPatientBasicInfoFromPatientImportReq(patientImportReq, employeeId);

        //1 创建患者
        PatientInfo oldPatientInfo = null;
        if (StringUtils.isNotEmpty(patientBasicInfo.getMobile())) {
            oldPatientInfo = patientMapper.selectPatientInfoByNameAndMobileBasic(patientBasicInfo.getChainId(), patientBasicInfo.getName(), patientBasicInfo.getMobile(), null);
        }
        CisPatientInfo beforePatientInfo = PatientConvertor.patientInfoToCisPatientInfo(oldPatientInfo);

        if (oldPatientInfo == null) {
            Organ organ = clinicService.getOrgan(chainId);
            addPatient(patientBasicInfo, chainId, clinicId, employeeId, true, organ.getHisType(), context);
        } else {
            //防止误刷患者标识 从1刷成0
            if (oldPatientInfo.getMemberFlag() != null && oldPatientInfo.getMemberFlag() == 1) {
                patientBasicInfo.setMemberFlag(oldPatientInfo.getMemberFlag());
            }
            updatePatient(oldPatientInfo, patientBasicInfo, employeeId);

            if (context != null) {
                CisPatientInfo afterPatientInfo = PatientConvertor.cloneCisPatientInfo(beforePatientInfo);
                context.addUpdatePatientAction(beforePatientInfo, afterPatientInfo);
            }
        }

        //更新患者tag信息
        updatePatientTags(patientBasicInfo, chainId, employeeId);

        //2. 增加会员信息 会员标识为1且会员信息不为空
        if (patientBasicInfo.getMemberFlag() == 1 && patientBasicInfo.getMemberInfo() != null) {
            if (oldPatientInfo != null) {
                PatientMemberInfo patientMember = patientMemberMapper.selectAllPatientMemberInfo(chainId, patientBasicInfo.getId());
                if (patientMember != null && patientMember.getStatus() == 1) {
                    //不允许重复导入会员
                    throw new CrmCustomException(CrmServiceError.CREATE_MEMBER_CONFLICT);
                } else {
                    log.warn("del patient member where status != 1 {}", patientMember);
                    patientMemberMapper.deleteByChainIdAndPatientId(chainId, patientBasicInfo.getId());
                }
            }

            int rst = createPatientMember(patientBasicInfo, SysCommon.UNKNOWN_ID, true, context);
            if (rst == -1) {
                throw new CrmCustomException(CrmServiceError.CREATE_MEMBER_ERROR);
            }

            //记录充值金额
            PatientMember memberInfo = patientBasicInfo.getMemberInfo();
            if (memberInfo != null && (BigDecimal.ZERO.compareTo(memberInfo.getPrincipal()) != 0 || BigDecimal.ZERO.compareTo(memberInfo.getPresent()) != 0)) {
                createInitPatientMemberBill(memberInfo);
            }
        }

        //3. 增加家庭成员关联信息
        if (StringUtils.isNotEmpty(patientImportReq.getParentId()) && StringUtils.isNotEmpty(patientBasicInfo.getId())) {
            patientFamilyService.addPatientToFamily(patientBasicInfo.getId(), patientImportReq.getParentId(), patientImportReq.getChainId(), SysCommon.UNKNOWN_ID, null);
        }

        PatientImportRsp patientImportRsp = new PatientImportRsp();
        patientImportRsp.setCode(200);
        return patientImportRsp;
    }


    private void createInitPatientMemberBill(PatientMember memberInfo) throws CrmCustomException {
        PatientMemberBill memberBill = new PatientMemberBill();
        memberBill.setId(abcIdGenerator.getUUID());
        memberBill.setChainId(memberInfo.getChainId());
        memberBill.setClinicId(memberInfo.getCreatedClinicId());
        memberBill.setAction(PatientMemberBill.Action.MEMBER_RECHARGE);
        memberBill.setPatientId(memberInfo.getPatientId());
        memberBill.setType(PatientMemberBill.Type.IN);
        memberBill.setPrincipal(memberInfo.getPrincipal());
        memberBill.setPresent(memberInfo.getPresent());
        memberBill.setPayMode(PatientMemberBill.PayMode.CASH);
        memberBill.setBusinessType(PatientMemberBill.BusinessType.CHARGE);
        memberBill.setPaySubMode(PatientMemberBill.PayMode.UN_KNOWN);
        memberBill.setPrincipalBalance(memberInfo.getPrincipal());
        memberBill.setPresentBalance(memberInfo.getPresent());
        memberBill.setCreatedBy("00000000000000000000000000000000");
        memberBill.setSellerUserName("未指定");
        memberBill.setCreated(memberInfo.getCreated());
        memberBill.setPaySource(PatientMemberBill.PaySource.PC);
        memberBill.setStatus(PatientMemberBill.Status.SUCCESS);

        int ret = patientMemberMapper.insertPatientMemberBill(memberBill);
        if (ret <= 0) {
            log.warn("insert patient member bill failed. [{}]", memberBill);
            throw new CrmCustomException(CrmServiceError.CREATE_MEMBER_BILL_FAILED);
        }
    }

    private PatientBasicInfo convertToPatientBasicInfoFromPatientImportReq(PatientImportReq patientImportReq, String employeeId) {
        PatientBasicInfo patientBasicInfo = new PatientBasicInfo();
        patientBasicInfo.setImportFlag(1);
        patientBasicInfo.setName(patientImportReq.getName());
        patientBasicInfo.setSex(patientImportReq.getSex());
        patientBasicInfo.setMobile(patientImportReq.getMobile());
        patientBasicInfo.setIdCard(patientImportReq.getIdCard());
        patientBasicInfo.setChainId(patientImportReq.getChainId());
        patientBasicInfo.setClinicId(patientImportReq.getCreatedClinicId());
        patientBasicInfo.setCreatedClinicId(patientImportReq.getCreatedClinicId());
        if (patientImportReq.getBirthday() != null) {
            patientBasicInfo.setBirthday(DateFormatUtils.format(patientImportReq.getBirthday(), "yyyy-MM-dd"));
        }
        Address address = patientImportReq.getAddressInfo();
        if (address != null) {
            patientBasicInfo.setAddressProvinceId(address.getAddressProvinceId());
            patientBasicInfo.setAddressProvinceName(address.getAddressProvinceName());
            patientBasicInfo.setAddressCityId(address.getAddressCityId());
            patientBasicInfo.setAddressCityName(address.getAddressCityName());
            patientBasicInfo.setAddressDistrictId(address.getAddressDistrictId());
            patientBasicInfo.setAddressDistrictName(address.getAddressDistrictName());
            patientBasicInfo.setAddressDetail(address.getAddressDetail());
        }
        patientBasicInfo.setCreatedBy(employeeId);
        patientBasicInfo.setLastModifiedBy(employeeId);
        if (StringUtils.isNotEmpty(patientImportReq.getSourceId())) {
            patientBasicInfo.setSourceId(patientImportReq.getSourceId());
        }
        patientBasicInfo.setMemberFlag(patientImportReq.getIsMember());

        if (patientImportReq.getIsMember() == 1) {
            PatientMember patientMemberInfo = new PatientMember();
            patientMemberInfo.setMemberTypeId(patientImportReq.getMemberTypeId());
            patientMemberInfo.setPrincipal(patientImportReq.getMemberPrincipal());
            patientMemberInfo.setPresent(patientImportReq.getMemberPresent());
            patientMemberInfo.setCreatedClinicId(patientImportReq.getCreatedClinicId());
            patientMemberInfo.setPrincipal(patientImportReq.getMemberPrincipal());
            patientMemberInfo.setRemark(patientImportReq.getMemberRemark());
            patientBasicInfo.setMemberInfo(patientMemberInfo);
        }
        patientBasicInfo.setSn(patientImportReq.getSn());
        patientBasicInfo.setRemark(patientImportReq.getRemark());
        patientBasicInfo.setProfession(patientImportReq.getProfession());
        patientBasicInfo.setPoints(patientImportReq.getMemberPoints());
        patientBasicInfo.setCompany(patientImportReq.getCompany());
        patientBasicInfo.setTags(patientImportReq.getTags());
        return patientBasicInfo;
    }

    private void checkPatientImportReq(PatientImportReq patientImportReq) throws CrmCustomException {

        if (StringUtils.isEmpty(patientImportReq.getName())
                || StringUtils.isEmpty(patientImportReq.getSex())
                || (!"男".equals(patientImportReq.getSex()) && !"女".equals(patientImportReq.getSex()))
                || StringUtils.isEmpty(patientImportReq.getChainId())
                || StringUtils.isEmpty(patientImportReq.getCreatedClinicId())) {
            throw new CrmCustomException(CrmServiceError.BAD_PARAMETER);
        }
        if (StringUtils.isNotEmpty(patientImportReq.getMemberTypeName())) {
            patientImportReq.setIsMember(1);
        }

        if (patientImportReq.getMemberPrincipal() == null) {
            patientImportReq.setMemberPrincipal(BigDecimal.ZERO);
        }

        if (patientImportReq.getMemberPresent() == null) {
            patientImportReq.setMemberPresent(BigDecimal.ZERO);
        }

        if (patientImportReq.getIsMember() == 0) {
            if (patientImportReq.getMemberPrincipal().compareTo(BigDecimal.ZERO) != 0
                    || patientImportReq.getMemberPresent().compareTo(BigDecimal.ZERO) != 0
                    || StringUtils.isNotEmpty(patientImportReq.getMemberRemark())) {
                throw new CrmCustomException(CrmServiceError.BAD_PARAMETER);
            }
        }

        setPatientSourceId(patientImportReq);
        if (patientImportReq.getIsMember() == 1) {
            if (StringUtils.isEmpty(patientImportReq.getMobile())) {
                throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_MOBILE_REQUIRED);
            }
            setPatientMemberTypeId(patientImportReq);
        }

        if (StringUtils.isNotEmpty(patientImportReq.getAddress())) {
            Address address = new Address();
            String[] addressStr = patientImportReq.getAddress().split("/", 4);
            if (addressStr.length != 4) {
                address.setAddressDetail(patientImportReq.getAddress());
            } else {
                //暂时先不处理code
                address.setAddressProvinceName(addressStr[0]);
                address.setAddressCityName(addressStr[1]);
                address.setAddressDistrictName(addressStr[2]);
                address.setAddressDetail(addressStr[3]);
            }
            patientImportReq.setAddressInfo(address);
        }

        if (StringUtils.isNotEmpty(patientImportReq.getMemberShareName()) && StringUtils.isEmpty(patientImportReq.getMemberShareMobile())) {
            throw new CrmCustomException(CrmServiceError.BAD_MEMBER_SHARE_PARAMETER);
        }

        PatientUtil.checkSn(patientImportReq.getSn());

        //检查 并设置家庭成员信息
        if (StringUtils.isNotEmpty(patientImportReq.getMemberShareName())) {
            PatientInfo parentPatientInfo = patientMapper.selectPatientBasicInfoByNameAndMobile(patientImportReq.getChainId(), patientImportReq.getMemberShareName(), patientImportReq.getMemberShareMobile(), null);
            if (parentPatientInfo == null) {
                throw new CrmCustomException(CrmServiceError.MEMBER_SHARE_REQUIRED_PARAMETER);
            }

            PatientFamily parentFamily = patientFamilyService.getPatientFamilyByPatientIdAndChainId(parentPatientInfo.getId(), patientImportReq.getChainId());
            if (parentFamily != null && StringUtils.isNotEmpty(parentFamily.getParentId())) {
                log.warn("this parent req has exist in one family, but not parent {}", parentFamily);
                throw new CrmCustomException(CrmServiceError.PATIENT_FAMILY_NOT_PARENT);
            }
            patientImportReq.setParentId(parentPatientInfo.getId());
        }

        //检查标签名称
        if (!CollectionUtils.isEmpty(patientImportReq.getTagNames())) {
            List<CisPatientTag> tags = new ArrayList<>();
            List<PatientTagType> patientTagTypes = patientTagService.getChildPatientTagTypesByChainId(patientImportReq.getChainId());
            Map<String, PatientTagType> patientTagTypeMap = ListUtils.toMap(patientTagTypes, PatientTagType::getName);
            for (String tagName : patientImportReq.getTagNames()) {
                PatientTagType patientTagType = patientTagTypeMap.get(tagName);
                if (patientTagType == null) {
                    throw new CrmCustomException(CrmServiceError.PATIENT_TAG_NAME_NO_EXIST);
                }
                CisPatientTag patientTag = new CisPatientTag();
                patientTag.setTagId(patientTagType.getId());
                patientTag.setTagName(patientTagType.getName());
                patientTag.setViewMode(patientTagType.getViewMode());
                if (patientTagType.getStyle() != null) {
                    PatientTagTypeStyle style = new PatientTagTypeStyle();
                    BeanUtils.copyProperties(patientTagType.getStyle(), style);
                    patientTag.setStyle(style);
                }
                tags.add(patientTag);
            }
            patientImportReq.setTags(tags);
        }

        // 检查生日
        if (patientImportReq.getBirthday() != null) {
            String birthday = DateFormatUtils.format(patientImportReq.getBirthday(), "yyyy-MM-dd");
            CommonUtil.checkBirthday(birthday);
        }
    }

    private void setPatientMemberTypeId(PatientImportReq patientImportReq) throws CrmCustomException {
        List<PatientMemberTypeInfo> patientMemberTypeInfos = patientMemberMapper.selectAllPatientMemberInfoByChainIds(patientImportReq.getChainId());
        Map<String, PatientMemberTypeInfo> patientMemberTypeInfoMap = ListUtils.toMap(patientMemberTypeInfos, PatientMemberTypeInfo::getMemberTypeName);
        PatientMemberTypeInfo patientMemberTypeInfo = patientMemberTypeInfoMap.get(patientImportReq.getMemberTypeName());
        if (patientMemberTypeInfo == null) {
            throw new CrmCustomException(CrmServiceError.BAD_MEMBER_TYPE_PARAMETER);
        }
        patientImportReq.setMemberTypeId(patientMemberTypeInfo.getMemberTypeId());
    }

    private void setPatientSourceId(PatientImportReq patientImportReq) throws CrmCustomException {
        if (StringUtils.isEmpty(patientImportReq.getSource())) {
            return;
        }
        List<PatientSourceType> sourceTypesOneLevel = patientSourceTypeService.getAllSourceTypes(patientImportReq.getChainId()).stream().filter(patientSourceType -> patientSourceType.getLevel() == PatientSourceType.Level.ONE_LEVEL).collect(Collectors.toList());
        List<PatientSourceType> sourceTypesTwoLevel = patientSourceTypeService.getAllSourceTypes(patientImportReq.getChainId()).stream().filter(patientSourceType -> patientSourceType.getLevel() == PatientSourceType.Level.TWO_LEVEL).collect(Collectors.toList());

        Map<String, String> sourceTypeMap = sourceTypesOneLevel.stream().collect(Collectors.toMap(PatientSourceType::getName, PatientSourceType::getId, (a, b) -> a));

        Map<String, List<PatientSourceType>> twoLevelMap = ListUtils.groupByKey(sourceTypesTwoLevel, PatientSourceType::getParentId);

        if (!sourceTypeMap.containsKey(patientImportReq.getSource())) {
            throw new CrmCustomException(CrmServiceError.BAD_SOURCE_PARAMETER);
        }
        String oneLevelId = sourceTypeMap.get(patientImportReq.getSource());
        if (StringUtils.isEmpty(patientImportReq.getSourceSecondLevel())) {
            patientImportReq.setSourceId(oneLevelId);
        } else {
            List<PatientSourceType> twoTypeLists = twoLevelMap.get(oneLevelId);
            Map<String, PatientSourceType> patientSourceTypeMap = ListUtils.toMap(twoTypeLists, PatientSourceType::getName);

            PatientSourceType patientSourceType = patientSourceTypeMap.get(patientImportReq.getSourceSecondLevel());

            if (Objects.isNull(patientSourceType)) {
                throw new CrmCustomException(CrmServiceError.BAD_SOURCE_PARAMETER);
            }
            patientImportReq.setSourceId(patientSourceType.getId());
            if (PatientSourceType.RelatedType.SOURCE_FROM_RELATE_EMPLOYEE_TYPES.contains(patientSourceType.getRelatedType())) {
                EmployeeView employee = cisScClinicFeignClient.getChainEmployeeByMobile(patientImportReq.getChainId(), patientImportReq.getMobile());

                if (Objects.isNull(employee)) {
                    throw new CrmCustomException(CrmServiceError.BAD_SOURCE_PARAMETER);
                }

                patientImportReq.setSourceFrom(employee.getId());
            }


        }


    }

    public GeneralRsp updatePatientMemberInfo(UpdatePatientMemberPasswordReq updatePatientMemberPasswordReq, String employeeId, String chainId, String patientId) throws CrmCustomException {

        PatientBasicInfo patientBasicInfo = patientMemberMapper.selectPatientBasicInfoById(chainId, patientId);
        if (patientBasicInfo == null) {
            throw new CrmCustomException(CrmServiceError.PATIENT_NOT_FOUND);
        }

        if (patientBasicInfo.getMemberInfo() == null || patientBasicInfo.getMemberInfo().getStatus() >= 90) {
            throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_NOT_FOUND);
        }

        if (StringUtils.isEmpty(updatePatientMemberPasswordReq.getPassword())) {
            throw new CrmCustomException(CrmServiceError.PARAMETER_REQUIRED);
        }

        PatientMember patientMember = patientBasicInfo.getMemberInfo();
        patientMember.setPassword(generatePasswordMd5Str(updatePatientMemberPasswordReq.getPassword(), patientBasicInfo.getId()));
        patientMember.setLastModifiedBy(employeeId);

        int rst = patientMemberMapper.updatePatientMemberInfo(patientMember);
        if (rst <= 0) {
            log.warn("update patient member [{}] failed. [{}]", patientId, updatePatientMemberPasswordReq);
        }
        GeneralRsp generalRsp = new GeneralRsp();
        generalRsp.setCode(200);
        generalRsp.setMessage("修改成功");
        return generalRsp;
    }

    //action pay
    @Transactional
    public PatientMemberCardPayRsp rpcPatientMemberPay(String patientId, PatientMemberCardPayReq req) throws CrmCustomException {

        // 1、校验参数
        req.preDealParams().validateParams();

        PatientBasicInfo patientBasicInfo = patientMemberMapper.selectPatientBasicInfoById(req.getChainId(), patientId);
        if (patientBasicInfo == null) {
            throw new CrmCustomException(CrmServiceError.PATIENT_NOT_FOUND);
        }

        if (patientBasicInfo.getMemberInfo() == null || patientBasicInfo.getMemberInfo().getStatus() >= 90) {
            throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_NOT_FOUND);
        }

        //1. 校验密码 密码校验是否开启 如果开启，进行密码校验
        boolean isMemberPasswordEnable = rpcGetMemberPasswordEnable(SysCommon.CHAIN_SCOPE, SysCommon.CRM_PATIENT_MEMBER_PASSWORD_KEY, req.getChainId());
        if (isMemberPasswordEnable) {
            checkPatientMemberPassword(patientBasicInfo, req.getChainId(), req.getPassword());
        }

        //2. 支付
        return doPatientMemberBalanceChange(patientBasicInfo, req);
    }

    @Transactional
    public PatientMemberCardPayRsp doPatientMemberBalanceChange(PatientBasicInfo patientBasicInfo, PatientMemberCardPayReq req) throws CrmCustomException {

        PatientMember memberInfo = patientBasicInfo.getMemberInfo();
        if (memberInfo.getPrincipal() == null) {
            memberInfo.setPrincipal(BigDecimal.ZERO);
        }

        if (memberInfo.getPresent() == null) {
            memberInfo.setPresent(BigDecimal.ZERO);
        }

        req.setCash(req.getCash().abs());
        BigDecimal total = memberInfo.getPrincipal().add(memberInfo.getPresent()).setScale(2, BigDecimal.ROUND_HALF_UP);
        if (req.getCash().compareTo(total) > 0) {
            throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_BALANCE_INSUFFICIENT);
        }

        //1. 扣除本金or赠金
        BigDecimal principalNeedPay;
        BigDecimal presentNeedPay;
        List<PatientMemberBillItem> billItems = Lists.newArrayList();
        // 未传items时，兼容老逻辑
        if (CollectionUtils.isEmpty(req.getItems())) {
            if (memberInfo.getPrincipal().compareTo(req.getCash()) >= 0) {
                principalNeedPay = req.getCash();
                presentNeedPay = BigDecimal.ZERO;
            } else {
                principalNeedPay = memberInfo.getPrincipal();
                presentNeedPay = req.getCash().subtract(memberInfo.getPrincipal());
            }
        } else {
            billItems = generatePatientMemberBillItems(req.getItems(), memberInfo, req.getOperatorId(), req.getChainId(), req.getClinicId());
            // 计算支付的总本金和总赠金，需要把父item 排除掉
            principalNeedPay = BigDecimal.ZERO;
            presentNeedPay = BigDecimal.ZERO;
            Set<Long> allParentItemIds = billItems.stream().map(PatientMemberBillItem::getParentItemId).filter(Objects::nonNull).collect(Collectors.toSet());
            for (PatientMemberBillItem billItem : billItems) {
                if (allParentItemIds.contains(billItem.getId())) {
                    continue;
                }
                principalNeedPay = MathUtils.wrapBigDecimalAdd(principalNeedPay, billItem.getPrincipal());
                presentNeedPay = MathUtils.wrapBigDecimalAdd(presentNeedPay, billItem.getPresent());
            }
        }

        log.info("patient member need pay [{}] / [{}]", principalNeedPay, presentNeedPay);
        int rst = patientMemberMapper.updatePatientMemberBalance(memberInfo.getChainId(), memberInfo.getPatientId(),
                BigDecimal.ZERO.subtract(principalNeedPay), BigDecimal.ZERO.subtract(presentNeedPay), req.getOperatorId());
        if (rst <= 0) {
            log.warn("update patient member [{}] balance failed. [{}]", memberInfo, rst);
            throw new CrmServiceException(CrmServiceError.PATIENT_MEMBER_BALANCE_CHANGE_FAILED);
        }

        //2. 记录流水
        BigDecimal principalBalance = memberInfo.getPrincipal().subtract(principalNeedPay);
        BigDecimal presentBalance = memberInfo.getPresent().subtract(presentNeedPay);
        PatientMemberBill patientMemberBill = generatePatientMemberBill(memberInfo, req, principalNeedPay,
                presentNeedPay, principalBalance, presentBalance, PatientMemberBill.Action.CHARGE, PatientMemberBill.Type.OUT, req.getSourceType());

        int rst1 = patientMemberMapper.insertPatientMemberBill(patientMemberBill);
        if (rst1 <= 0) {
            log.warn("insert patient member bill [{}] balance. [{}]", patientMemberBill, rst);
        }
        // 设置billItems的billId
        if (!CollectionUtils.isEmpty(billItems)) {
            billItems.forEach(billItem -> billItem.setBillId(patientMemberBill.getId()));
            patientMemberBillItemRepository.saveAll(billItems);
        }

        //3. 发送会员卡消费消息
        sendMemberBalanceChangeMsg(patientMemberBill, patientBasicInfo);

        PatientMemberCardPayRsp rsp = new PatientMemberCardPayRsp();
        rsp.setTransactionId(patientMemberBill.getId());
        rsp.setPrincipal(principalNeedPay);
        rsp.setPrincipalBalance(principalBalance);
        rsp.setPresent(presentNeedPay);
        rsp.setPresentBalance(presentBalance);
        rsp.setBusinessId(req.getBusinessId());
        rsp.setBusinessType(req.getBusinessType());
        return rsp;
    }

    private List<PatientMemberBillItem> generatePatientMemberBillItems(List<PatientMemberCardPayItemReq> items,
                                                                       PatientMember memberInfo,
                                                                       String operatorId,
                                                                       String chainId,
                                                                       String clinicId) {
        // 1、摊费到每个item上
        List<MemberCardAmountFlatHelper.FlatCell> flattedCells = flatPrincipalAndPresentToItems(items, memberInfo);

        // 2、根据items 生成billItems
        Map<String, MemberCardAmountFlatHelper.FlatCell> businessItemIdFlatCellMap = ListUtils.toMap(flattedCells, MemberCardAmountFlatHelper.FlatCell::getId);
        return items.stream()
                .flatMap(itemReq -> genBillItemsFromPayItemReq(chainId, clinicId, null, itemReq, businessItemIdFlatCellMap, operatorId).stream())
                .collect(Collectors.toList());
    }

    private List<PatientMemberBillItem> genBillItemsFromPayItemReq(String chainId,
                                                                   String clinicId,
                                                                   Long parentItemId,
                                                                   PatientMemberCardPayItemReq itemReq,
                                                                   Map<String, MemberCardAmountFlatHelper.FlatCell> businessItemIdFlatCellMap,
                                                                   String operatorId) {
        List<PatientMemberBillItem> billItems = Lists.newArrayList();

        MemberCardAmountFlatHelper.FlatCell flatCell = businessItemIdFlatCellMap.get(itemReq.getBusinessItemId());
        PatientMemberBillItem billItem = new PatientMemberBillItem()
                .setId(abcIdGenerator.getUIDLong())
                .setChainId(chainId)
                .setClinicId(clinicId)
                .setBusinessItemId(itemReq.getBusinessItemId())
                .setParentItemId(parentItemId)
                .setPrincipal(Objects.nonNull(flatCell) ? flatCell.getFlatPrincipalAmount() : BigDecimal.ZERO)
                .setPresent(Objects.nonNull(flatCell) ? flatCell.getFlatPresentAmount() : BigDecimal.ZERO)
                .setCreatedBy(operatorId)
                .setCreated(Instant.now());
        billItems.add(billItem);
        // 如果没有children
        if (CollectionUtils.isEmpty(itemReq.getChildren())) {
            return billItems;
        }
        itemReq.getChildren()
                .stream()
                .flatMap(childItemReq -> genBillItemsFromPayItemReq(chainId, clinicId, billItem.getId(), childItemReq, businessItemIdFlatCellMap, operatorId).stream())
                .forEach(billItems::add);

        // 将billItem对应的直属子项金额汇总出来
        BigDecimal parentPrincipal = BigDecimal.ZERO;
        BigDecimal parentPresent = BigDecimal.ZERO;
        for (PatientMemberBillItem item : billItems) {
            if (!Objects.equals(item.getParentItemId(), billItem.getId())) {
                continue;
            }
            parentPrincipal = MathUtils.wrapBigDecimalAdd(parentPrincipal, item.getPrincipal());
            parentPresent = MathUtils.wrapBigDecimalAdd(parentPresent, item.getPresent());
        }
        billItem.setPrincipal(parentPrincipal).setPresent(parentPresent);

        return billItems;
    }

    private List<MemberCardAmountFlatHelper.FlatCell> flatPrincipalAndPresentToItems(List<PatientMemberCardPayItemReq> items, PatientMember memberInfo) {
        // 1、获取实际参与摊费的items,排除母项，并且按照useRuleType分组
        Map<Integer, List<PatientMemberCardPayItemReq>> useRuleTypeItemReqsMap = items.stream()
                .flatMap(itemReq -> itemReq.genActualParticipateFlatPriceItems().stream())
                .collect(Collectors.groupingBy(PatientMemberCardPayItemReq::getUseRuleType));

        // 2、循环分摊
        AtomicReference<BigDecimal> leftMemberPrincipal = new AtomicReference<>(MathUtils.wrapBigDecimalOrZero(memberInfo.getPrincipal()));
        AtomicReference<BigDecimal> leftMemberPresent = new AtomicReference<>(MathUtils.wrapBigDecimalOrZero(memberInfo.getPresent()));
        return useRuleTypeItemReqsMap.entrySet()
                .stream()
                .sorted(Comparator.comparingInt(Map.Entry::getKey))
                .flatMap(entry -> {
                    int useRuleType = entry.getKey();
                    BigDecimal useRuleTypeTotalPrice = BigDecimal.ZERO;
                    List<MemberCardAmountFlatHelper.FlatCell> useRuleTypeNeedFlatCells = Lists.newArrayList();
                    for (PatientMemberCardPayItemReq payItemReq : entry.getValue()) {
                        useRuleTypeNeedFlatCells.add(new MemberCardAmountFlatHelper.FlatCell()
                                .setId(payItemReq.getBusinessItemId())
                                .setTotalPrice(MathUtils.wrapBigDecimalOrZero(payItemReq.getAmount()))
                                .setMaxFlatPrice(MathUtils.wrapBigDecimalOrZero(payItemReq.getAmount()))
                        );
                        useRuleTypeTotalPrice = MathUtils.wrapBigDecimalAdd(useRuleTypeTotalPrice, payItemReq.getAmount());
                    }
                    // 计算本金、赠金分别需要多少
                    BigDecimal thisNeedPrincipal = BigDecimal.ZERO;
                    BigDecimal thisNeedPresent = BigDecimal.ZERO;
                    // 优先本金
                    if (Objects.equals(useRuleType, PatientMemberCardPayItemReq.UseRuleType.PRINCIPAL_PRIORITY)) {
                        thisNeedPrincipal = MathUtils.min(useRuleTypeTotalPrice, leftMemberPrincipal.get());
                        thisNeedPresent = useRuleTypeTotalPrice.subtract(thisNeedPrincipal);
                    } else if (Objects.equals(useRuleType, PatientMemberCardPayItemReq.UseRuleType.PRESENT_PRIORITY)) {
                        // 优先赠金
                        thisNeedPresent = MathUtils.min(useRuleTypeTotalPrice, leftMemberPresent.get());
                        thisNeedPrincipal = useRuleTypeTotalPrice.subtract(thisNeedPresent);
                    } else {
                        // 按比例分摊，为了保证useRuleTypeTotalPrice能一次摊完，并且不超过剩余本金、赠金，直接使用UP
                        if (MathUtils.wrapBigDecimalCompare(leftMemberPrincipal.get().add(leftMemberPresent.get()), BigDecimal.ZERO) > 0) {
                            thisNeedPrincipal = leftMemberPrincipal.get().multiply(useRuleTypeTotalPrice).divide(leftMemberPrincipal.get().add(leftMemberPresent.get()), 2, RoundingMode.UP);
                            thisNeedPrincipal = MathUtils.min(MathUtils.min(thisNeedPrincipal, leftMemberPrincipal.get()), useRuleTypeTotalPrice);
                            thisNeedPresent = useRuleTypeTotalPrice.subtract(thisNeedPrincipal);
                        }
                    }
                    // 校验本次支付是否超过剩余本金、赠金
                    if (MathUtils.wrapBigDecimalCompare(thisNeedPrincipal, leftMemberPrincipal.get()) > 0) {
                        log.error(AbcLogMarker.MARKER_MESSAGE_INDEX, "useRuleType:{}, thisNeedPrincipal:{} > leftMemberPrincipal:{}", useRuleType, thisNeedPrincipal, leftMemberPrincipal);
                        throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_BALANCE_INSUFFICIENT);
                    }
                    if (MathUtils.wrapBigDecimalCompare(thisNeedPresent, leftMemberPresent.get()) > 0) {
                        log.error(AbcLogMarker.MARKER_MESSAGE_INDEX, "useRuleType:{}, thisNeedPresent:{} > thisNeedPresent:{}", useRuleType, thisNeedPresent, leftMemberPresent);
                        throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_BALANCE_INSUFFICIENT);
                    }

                    // 摊费
                    new MemberCardAmountFlatHelper(thisNeedPresent, thisNeedPrincipal).flat(useRuleTypeNeedFlatCells);
                    // 剩余本金、赠金减少
                    leftMemberPrincipal.set(MathUtils.wrapBigDecimalSubtract(leftMemberPrincipal.get(), thisNeedPrincipal));
                    leftMemberPresent.set(MathUtils.wrapBigDecimalSubtract(leftMemberPresent.get(), thisNeedPresent));
                    return useRuleTypeNeedFlatCells.stream();
                })
                .collect(Collectors.toList());
    }

    private void sendMemberBalanceChangeMsg(PatientMemberBill memberBill, PatientBasicInfo patientBasicInfo) {
        try {
            ClinicWithAddress clinicWithAddress = clinicService.queryClinicInfo(memberBill.getClinicId());
            if (clinicWithAddress == null || StringUtils.isEmpty(clinicWithAddress.getName())) {
                return;
            }
//            patientMessageProducer.sendMemberPaySuccessSmsMsg(memberBill, clinicWithAddress.getName(), patientBasicInfo);
//            patientMessageProducer.sendMemberPaySuccessWxMsg(memberBill, clinicWithAddress.getName(), patientBasicInfo);
            rocketMqProducer.sendMemberPaySuccessSmsMsg(memberBill, clinicWithAddress.getName(), patientBasicInfo);
            rocketMqProducer.sendMemberPaySuccessWxMsg(memberBill, clinicWithAddress.getName(), patientBasicInfo);
        } catch (Exception e) {
            log.warn("send message recharge success msg failed. [{}]", memberBill);
            e.printStackTrace();
        }
    }

    private PatientMemberBill generatePatientMemberBill(PatientMember memberInfo,
                                                        PatientMemberCardPayReq req,
                                                        BigDecimal principalNeedPay,
                                                        BigDecimal presentNeedPay,
                                                        BigDecimal principalBalance,
                                                        BigDecimal presentBalance,
                                                        String action,
                                                        int type,
                                                        Integer sourceType) {

        PatientMemberBill memberBill = new PatientMemberBill();
        memberBill.setId(abcIdGenerator.getUUID());
        memberBill.setChainId(req.getChainId());
        memberBill.setClinicId(req.getClinicId());
        memberBill.setPatientOrderId(req.getPatientOrderId());
        memberBill.setChargeSheetId(req.getChargeSheetId());
        memberBill.setChargeTransactionId(req.getBusinessId());
        memberBill.setPatientId(memberInfo.getPatientId());
        memberBill.setAction(action);
        memberBill.setType(type);
        memberBill.setPayMode(PatientMemberBill.PayMode.MEMBER_CARD);
        memberBill.setPaySubMode(PatientMemberBill.PayMode.UN_KNOWN);
        memberBill.setPrincipal(principalNeedPay);
        memberBill.setPresent(presentNeedPay);
        memberBill.setPrincipalBalance(principalBalance);
        memberBill.setPresentBalance(presentBalance);
        memberBill.setCreatedBy(req.getOperatorId());
        memberBill.setDirectSellerId(req.getSellerId());
        memberBill.setDoctorId(req.getDoctorId());
        memberBill.setCreated(new Date());
        memberBill.setPaySource(req.getPaySource());
        memberBill.setBusinessId(req.getBusinessId());
        memberBill.setBusinessType(req.getBusinessType());
        memberBill.setTransactionPatientId(req.getTransactionPatientId());
        memberBill.setSourceType(sourceType);
        memberBill.setStatus(PatientMemberBill.Status.SUCCESS);
        return memberBill;
    }


    public void checkPatientMemberPassword(PatientBasicInfo patientBasicInfo, String chainId, String passwordReq) throws CrmCustomException {
        if (StringUtils.isEmpty(passwordReq)) {
            throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_PASSWORD_INVALID);
        }

        String passwordReqMd5Str = generatePasswordMd5Str(passwordReq, patientBasicInfo.getId());
        String passwordDbMd5Str = patientBasicInfo.getMemberInfo().getPassword();

        // 密码为空，使用默认密码
        String mobile = patientBasicInfo.getMobile();
        if (StringUtils.isEmpty(passwordDbMd5Str) && StringUtils.isNotBlank(mobile) && mobile.length() >= 6
                && CommonUtil.isValidPhoneNumber(mobile, patientBasicInfo.getCountryCode())) {
            passwordDbMd5Str = generatePasswordMd5Str(
                    mobile.substring(mobile.length() - 6), patientBasicInfo.getId());
        }

        if (!StringUtils.equals(passwordReqMd5Str, passwordDbMd5Str)) {
            throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_PASSWORD_INVALID);
        }
    }

    private boolean rpcGetMemberPasswordEnable(String scope, String key, String chainId) {

        try {
            log.info("rpc get scope [{}] key [{}], scopeId [{}]", scope, key, chainId);
            CisServiceResponseBody<PatientConfig> memberPasswordEnableRsp = propertyServiceClient.getConfigProperty(key, chainId);
            if (memberPasswordEnableRsp != null) {
                PatientConfig patientConfig = memberPasswordEnableRsp.getData();
                if (patientConfig == null) {
                    log.warn("rpcGetMemberPasswordEnable patient config [{}] is null", chainId);
                    return false;
                } else {
                    return patientConfig.getEnableMemberPassword() == 1;
                }
            } else {
                log.warn("rpcGetMemberPasswordEnable patient config [{}] failed", chainId);
                return false;
            }
        } catch (Exception e) {
            log.warn("get config from property failed.", e);
            return false;
        }
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public GeneralRsp verifyPatientMemberPassword(String password, String chainId, String patientId) throws CrmCustomException {

        PatientBasicInfo patientBasicInfo = patientMemberMapper.selectPatientBasicInfoById(chainId, patientId);
        if (patientBasicInfo == null) {
            throw new CrmCustomException(CrmServiceError.PATIENT_NOT_FOUND);
        }

        if (patientBasicInfo.getMemberInfo() == null || patientBasicInfo.getMemberInfo().getStatus() >= 90) {
            throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_NOT_FOUND);
        }

        //1. 校验密码 密码校验是否开启 如果开启，进行密码校验
        GeneralRsp generalRsp = new GeneralRsp();
        try {

            if (StringUtils.isEmpty(password)) {
                throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_PASSWORD_INVALID);
            }

            checkPatientMemberPassword(patientBasicInfo, chainId, password);
            generalRsp.setCode(0);
            generalRsp.setMessage("验证成功");
        } catch (CisCustomException e) {
            log.warn("password is invalid. [{}]", patientId);
            generalRsp.setCode(e.getCode());
            generalRsp.setMessage(e.getMessage());
        }
        return generalRsp;
    }


    public PatientMember findPatientMemberByPatientId(String patientId, String chainId) {
        return patientMemberRepository.findPatientMemberByPatientIdAndChainId(patientId, chainId);
    }

    public List<PatientMember> getPatientMembersByPatientIds(List<String> patientIds, String chainId) {
        if (CollectionUtils.isEmpty(patientIds) || StringUtils.isEmpty(chainId)) {
            return new ArrayList<>();
        }

        return patientMemberRepository.findAllByPatientIdInAndChainIdAndStatus(patientIds, chainId, 1);
    }

    public void save(PatientMember patientMember) {
        patientMemberRepository.save(patientMember);
    }

    public void updatePatientMemberLogAndPointsLog(List<String> sourceIds, String destinationId) {
        patientMapper.updatePatientMemberLogToDesId(sourceIds, destinationId);
        patientMapper.updatePatientPointsLogToDesId(sourceIds, destinationId);
    }

    @Transactional
    public PatientMemberCardRefundRsp rpcPatientMemberRefund(PatientMemberCardRefundReq req) {
        // 校验参数
        req.validateParams();

        String chainId = req.getChainId();
        List<String> transactionIds = req.getTransactionIds();
        // 查询患者信息
        Patient patient = patientEntityService.getPatientById(req.getMemberId(), chainId);
        if (Objects.isNull(patient)) {
            throw new CrmCustomException(CrmServiceError.PATIENT_NOT_FOUND);
        }

        // 查询患者会员信息
        PatientMember patientMember = patientEntityService.findPatientMemberById(req.getMemberId(), chainId);
        if (Objects.isNull(patientMember)) {
            throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_NOT_FOUND);
        }
        BigDecimal oldPresent = patientMember.getPresent() == null ? new BigDecimal(0) : patientMember.getPresent();
        BigDecimal oldPrincipal = patientMember.getPrincipal() == null ? new BigDecimal(0) : patientMember.getPrincipal();

        // 查询transactionIds对应的memberBill
        List<PatientMemberBill> patientMemberBills = patientMemberBillRepository.findAllByChainIdAndIdIn(chainId, transactionIds);
        if (CollectionUtils.isEmpty(patientMemberBills)) {
            throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_BILL_NOT_EXIST);
        }

        if (patientMemberBills.size() != transactionIds.size()) {
            log.warn("{} 患者流水数据异常 {}", transactionIds, patientMemberBills);
            throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_BILL_NOT_EXIST);
        }

        // 兼容未传items的老逻辑
        List<PatientMemberBillItem> billItems = Lists.newArrayList();
        String patientOrderId = null;
        if (CollectionUtils.isEmpty(req.getItems())) {
            dealPatientMemberRefundWithoutItemReqs(patientMemberBills, req.getCash(), patientMember);
        } else {
            // patientMemberBills中只保留支付订单，移除掉退款入账的bill
            patientMemberBills.removeIf(patientMemberBill -> Objects.equals(patientMemberBill.getType(), PatientMemberBill.Type.IN));
            billItems = dealPatientMemberRefundWithItemReqs(chainId, req.getClinicId(), patientMemberBills, req.getOperatorId(), req.getItems(), patientMember);
        }

        // 保存数据库
        patientMemberRepository.save(patientMember);

        // 记录流水
        String id = abcIdGenerator.getUUID();

        MemberRefundReq memberRefundReq = new MemberRefundReq();
        memberRefundReq.setPrincipal(oldPrincipal.subtract(patientMember.getPrincipal()));
        memberRefundReq.setPresent(oldPresent.subtract(patientMember.getPresent()));
        memberRefundReq.setLastPresent(patientMember.getPresent());
        memberRefundReq.setLastPrincipal(patientMember.getPrincipal());
        memberRefundReq.setPayMode(PatientMemberBill.PayMode.MEMBER_CARD);
        memberRefundReq.setPaySource(req.getPaySource());
        if (StringUtils.isNotBlank(req.getSellerId())) {
            memberRefundReq.setSellerUserId(req.getSellerId());
            Employee employee = cisScClinicFeignClient.queryEmployeeById(chainId, req.getSellerId());
            if (Objects.nonNull(employee)) {
                memberRefundReq.setSellerUserName(employee.getName());
            }
        }

        PatientMemberBill refundMemberBill = patientMigrationMemberService.saveMemberBill(
                id,
                req.getClinicId(),
                patientMember,
                memberRefundReq,
                req.getBusinessId(),
                patientOrderId,
                patient,
                req.getOperatorId(),
                PatientMemberBill.Action.REFUND,
                req.getChargeSheetId(),
                req.getBusinessId(),
                req.getBusinessType(),
                req.getSourceType(),
                PatientMemberBill.Status.SUCCESS,
                CollectionUtils.isEmpty(req.getItems()) ? null : patientMemberBills.get(0).getChargeSheetId());
        // 如果billItems不为空，设置billId
        if (!CollectionUtils.isEmpty(billItems)) {
            billItems.forEach(billItem -> billItem.setBillId(refundMemberBill.getId()));

            patientMemberBillItemRepository.saveAll(billItems);
        }

        // 余额退款消息
        patientMigrationMemberService.sendMemberBalanceMessage(patientMember, refundMemberBill, patient, memberRefundReq);
        //拼接返回参数
        PatientMemberCardRefundRsp rsp = new PatientMemberCardRefundRsp();
        BeanUtils.copyProperties(refundMemberBill, rsp);
        rsp.setBusinessId(req.getBusinessId());
        rsp.setBusinessType(req.getBusinessType());
        rsp.setTransactionId(refundMemberBill.getId());
        return rsp;
    }

    private List<PatientMemberBillItem> dealPatientMemberRefundWithItemReqs(String chainId,
                                                                            String clinicId,
                                                                            List<PatientMemberBill> associateChargeSheetPayBills,
                                                                            String operatorId,
                                                                            List<PatientMemberCardRefundItemReq> items,
                                                                            PatientMember patientMember) {
        // 1、关联的支付订单bills
        if (CollectionUtils.isEmpty(associateChargeSheetPayBills)) {
            throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_PAY_BILL_NOT_EXIST);
        }
        List<String> payBillIds = Lists.newArrayList();
        List<String> payChargeSheetIds = Lists.newArrayList();
        associateChargeSheetPayBills.forEach(bill -> {
            payBillIds.add(bill.getId());
            payChargeSheetIds.add(bill.getChargeSheetId());
        });
        if (payChargeSheetIds.size() > 1){
            throw new CrmCustomException(CrmServiceError.WHEN_REFUND_PAY_TRANSACTION_NOT_SAME_CHARGE_SHEET);
        }
        String associateChargeSheetId = payChargeSheetIds.get(0);

        // 2、所有的已退费bills
        List<PatientMemberBill> existRefundBills = patientMemberBillRepository.findAllByAssociateChargeSheetIdAndType(associateChargeSheetId, PatientMemberBill.Type.IN);
        List<String> existRefundBillIds = existRefundBills.stream().map(PatientMemberBill::getId).collect(Collectors.toList());

        // 3、查询所有流水明细
        List<String> allBillIds = Lists.newArrayList(payBillIds);
        allBillIds.addAll(existRefundBillIds);
        List<PatientMemberBillItem> allBillItems = patientMemberBillItemRepository.findByChainIdAndBillIdIn(chainId, allBillIds);
        Set<Long> allParentBillItemIds = allBillItems.stream().map(PatientMemberBillItem::getParentItemId).collect(Collectors.toSet());

        // 4、获取实际需要参与退费分摊的itemReqs
        Map<String, PatientMemberCardRefundItemReq> actualRefundBusinessItemIdItemReqMap = items.stream()
                .flatMap(refundItemReq -> refundItemReq.genActualParticipateFlatPriceItems().stream())
                .collect(Collectors.toMap(PatientMemberCardRefundItemReq::getBusinessItemId, Function.identity()));

        // 5、计算associateBusinessItemId剩余可退金额
        Map<String, Pair<BigDecimal, BigDecimal>> payBusinessItemIdEnableRefundPrincipalPresentPairMap = allBillItems
                .stream()
                .filter(billItem -> !allParentBillItemIds.contains(billItem.getId()))
                .collect(Collectors.toMap(
                                billItem -> payBillIds.contains(billItem.getBillId()) ? billItem.getBusinessItemId() : billItem.getAssociateBusinessItemId(),
                                billItem -> Pair.of(
                                        payBillIds.contains(billItem.getBillId()) ? MathUtils.wrapBigDecimalOrZero(billItem.getPrincipal()) : MathUtils.wrapBigDecimalOrZero(billItem.getPrincipal()).negate(),
                                        payBillIds.contains(billItem.getBillId()) ? MathUtils.wrapBigDecimalOrZero(billItem.getPresent()) : MathUtils.wrapBigDecimalOrZero(billItem.getPresent()).negate()
                                ),
                                (a, b) -> Pair.of(a.getLeft().add(b.getLeft()), a.getRight().add(b.getRight()))
                        )
                );

        // 6、计算本次退款中本金、赠金金额
        Map<String, MemberCardAmountFlatHelper.FlatCell> refundBusinessItemIdFlatCellMap = actualRefundBusinessItemIdItemReqMap
                .entrySet()
                .stream()
                .map(entry -> {
                    PatientMemberCardRefundItemReq refundItemReq = entry.getValue();
                    Pair<BigDecimal, BigDecimal> enableRefundPrincipalPresentPair = payBusinessItemIdEnableRefundPrincipalPresentPairMap.get(refundItemReq.getAssociateBusinessItemId());
                    if (Objects.isNull(enableRefundPrincipalPresentPair)) {
                        log.error(AbcLogMarker.MARKER_MESSAGE_INDEX, "未找到支付流水明细，refundItemReq:{}", JsonUtils.dump(refundItemReq));
                        throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_PAY_BILL_NOT_EXIST);
                    }
                    if (MathUtils.wrapBigDecimalCompare(enableRefundPrincipalPresentPair.getLeft().add(enableRefundPrincipalPresentPair.getRight()), refundItemReq.getAmount()) < 0) {
                        log.error(AbcLogMarker.MARKER_MESSAGE_INDEX, "退款金额大于可退金额. refundItemReq:{}, enableRefundPrincipal:{}, enableRefundPresent:{}",
                                JsonUtils.dump(refundItemReq), enableRefundPrincipalPresentPair.getLeft(), enableRefundPrincipalPresentPair.getRight());
                        throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_BILL_INSUFFICIENT_PAYMENT);
                    }
                    MemberCardAmountFlatHelper.FlatCell flatCell = new MemberCardAmountFlatHelper.FlatCell()
                            .setId(entry.getKey())
                            .setTotalPrice(MathUtils.wrapBigDecimalOrZero(refundItemReq.getAmount()))
                            .setMaxFlatPrice(MathUtils.wrapBigDecimalOrZero(refundItemReq.getAmount()));
                    new MemberCardAmountFlatHelper(enableRefundPrincipalPresentPair.getRight(), enableRefundPrincipalPresentPair.getLeft()).flat(Lists.newArrayList(flatCell));
                    return flatCell;
                })
                .collect(Collectors.toMap(MemberCardAmountFlatHelper.FlatCell::getId, Function.identity()));

        // 7、将itemReq转成billItem
        List<PatientMemberBillItem> refundBillItems = items.stream()
                .flatMap(itemReq -> genBillItemsFromRefundItemReq(chainId, clinicId, null, itemReq, refundBusinessItemIdFlatCellMap, operatorId).stream())
                .collect(Collectors.toList());

        // 8、计算总的需退金额
        Set<Long> refundParentItemIds = refundBillItems.stream().map(PatientMemberBillItem::getParentItemId).filter(Objects::nonNull).collect(Collectors.toSet());
        BigDecimal principalNeedRefund = BigDecimal.ZERO;
        BigDecimal presentNeedRefund = BigDecimal.ZERO;
        for (PatientMemberBillItem billItem : refundBillItems) {
            if (refundParentItemIds.contains(billItem.getId())) {
                continue;
            }
            principalNeedRefund = MathUtils.wrapBigDecimalAdd(principalNeedRefund, billItem.getPrincipal());
            presentNeedRefund = MathUtils.wrapBigDecimalAdd(presentNeedRefund, billItem.getPresent());
        }
        patientMember.setPrincipal(patientMember.getPrincipal().add(principalNeedRefund));
        patientMember.setPresent(patientMember.getPresent().add(presentNeedRefund));
        patientMember.setBalance(patientMember.getPresent().add(patientMember.getPrincipal()));

        return refundBillItems;
    }

    private List<PatientMemberBillItem> genBillItemsFromRefundItemReq(String chainId,
                                                                      String clinicId,
                                                                      Long parentItemId,
                                                                      PatientMemberCardRefundItemReq itemReq,
                                                                      Map<String, MemberCardAmountFlatHelper.FlatCell> businessItemIdFlatCellMap,
                                                                      String operatorId) {
        List<PatientMemberBillItem> billItems = Lists.newArrayList();

        MemberCardAmountFlatHelper.FlatCell flatCell = businessItemIdFlatCellMap.get(itemReq.getBusinessItemId());
        PatientMemberBillItem billItem = new PatientMemberBillItem()
                .setId(abcIdGenerator.getUIDLong())
                .setChainId(chainId)
                .setClinicId(clinicId)
                .setBusinessItemId(itemReq.getBusinessItemId())
                .setParentItemId(parentItemId)
                .setAssociateBusinessItemId(itemReq.getAssociateBusinessItemId())
                .setPrincipal(Objects.nonNull(flatCell) ? flatCell.getFlatPrincipalAmount() : BigDecimal.ZERO)
                .setPresent(Objects.nonNull(flatCell) ? flatCell.getFlatPresentAmount() : BigDecimal.ZERO)
                .setCreatedBy(operatorId)
                .setCreated(Instant.now());
        billItems.add(billItem);
        // 如果没有children
        if (CollectionUtils.isEmpty(itemReq.getChildren())) {
            return billItems;
        }
        itemReq.getChildren()
                .stream()
                .flatMap(childItemReq -> genBillItemsFromRefundItemReq(chainId, clinicId, billItem.getId(), childItemReq, businessItemIdFlatCellMap, operatorId).stream())
                .forEach(billItems::add);

        // 将billItem对应的直属子项金额汇总出来
        BigDecimal parentPrincipal = BigDecimal.ZERO;
        BigDecimal parentPresent = BigDecimal.ZERO;
        for (PatientMemberBillItem item : billItems) {
            if (!Objects.equals(item.getParentItemId(), billItem.getId())) {
                continue;
            }
            parentPrincipal = MathUtils.wrapBigDecimalAdd(parentPrincipal, item.getPrincipal());
            parentPresent = MathUtils.wrapBigDecimalAdd(parentPresent, item.getPresent());
        }
        billItem.setPrincipal(parentPrincipal).setPresent(parentPresent);

        return billItems;
    }

    private void dealPatientMemberRefundWithoutItemReqs(List<PatientMemberBill> transactionMemberBills,
                                                        BigDecimal reqCash,
                                                        PatientMember patientMember) {

        if (CollectionUtils.isEmpty(transactionMemberBills)) {
            throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_BILL_NOT_EXIST);
        }

        // 获取所有当前收费单对应的所有流水记录，计算出当前支付金额
        BigDecimal present = BigDecimal.ZERO;
        BigDecimal principal = BigDecimal.ZERO;
        BigDecimal refundPresent = BigDecimal.ZERO;
        BigDecimal refundPrincipal = BigDecimal.ZERO;
        for (PatientMemberBill patientMemberBill : transactionMemberBills) {
            // 收费out为可退金额
            if (patientMemberBill.getType() == PatientMemberBill.Type.OUT) {
                present = present.add(patientMemberBill.getPresent());
                principal = principal.add(patientMemberBill.getPrincipal());
            }
            // 退费in为已退金额
            if (patientMemberBill.getType() == PatientMemberBill.Type.IN) {
                refundPresent = refundPresent.add(patientMemberBill.getPresent());
                refundPrincipal = refundPrincipal.add(patientMemberBill.getPrincipal());
            }
        }
        present = present.subtract(refundPresent);
        principal = principal.subtract(refundPrincipal);

        if (reqCash.compareTo(present.add(principal)) > 0) {
            throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_BILL_INSUFFICIENT_PAYMENT);
        }
        // 退费(优先退赠金)
        if (reqCash.compareTo(present) < 1) {
            // 根据会员流水记录支出查询赠金足够本次退款
            patientMember.setPresent(patientMember.getPresent().add(reqCash));
        } else {
            // 根据会员流水记录支出查询赠金不足，使用赠金+本金退款。（优先将用户的支出赠金扣完，再使用支出的本金）
            patientMember.setPresent(patientMember.getPresent().add(present));
            patientMember.setPrincipal(patientMember.getPrincipal().add(reqCash.subtract(present)));
        }

        patientMember.setBalance(patientMember.getPresent().add(patientMember.getPrincipal()));
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public PatientMemberBillDetailVO getMemberBillsById(String id, String chainId) {
        //获取会员流水
        PatientMemberBill patientMemberBill = patientMemberBillRepository.findByIdAndChainId(id, chainId);
        if (patientMemberBill == null) {
            throw new NotFoundException();
        }

        //获取会员信息
        if (StringUtils.isEmpty(patientMemberBill.getPatientId())) {
            log.warn("患者 id 为空. {}", patientMemberBill);
            throw new NotFoundException();
        }

        Patient patient = patientRepository.findPatientByIdAndChainIdAndStatus(patientMemberBill.getPatientId(), chainId, 1);
        if (patient == null || patient.getIsMember() != 1) {
            log.warn("患者 信息 不存在. {}", patientMemberBill);
            throw new NotFoundException();
        }

        PatientMember patientMember = patientMemberRepository.findPatientMemberByPatientIdAndChainIdAndStatus(patientMemberBill.getPatientId(), chainId, 1);
        if (patientMember == null) {
            log.warn("会员 不存在. {}", patientMemberBill);
            throw new NotFoundException();
        }

        PatientMemberType patientMemberType = patientMemberTypeService.getPatientMemberTypeById(patientMember.getMemberTypeId(), chainId);
        if (patientMemberType == null) {
            log.warn("会员卡信息 不存在. {}", patientMemberBill);
            throw new NotFoundException();
        }


        PatientBasicInfoView patientBasicInfoView = new PatientBasicInfoView();
        BeanUtils.copyProperties(patient, patientBasicInfoView);

        PatientMemberTypeVO memberTypeVO = new PatientMemberTypeVO();
        memberTypeVO.setId(patientMemberType.getId());
        memberTypeVO.setName(patientMemberType.getName());

        PatientMemberInfoVO memberInfoVO = new PatientMemberInfoVO();
        memberInfoVO.setPresent(patientMember.getPresent());
        memberInfoVO.setPrincipal(patientMember.getPrincipal());
        memberInfoVO.setPatient(patientBasicInfoView);
        memberInfoVO.setMemberType(memberTypeVO);

        PatientMemberBillDetailVO billDetailVO = new PatientMemberBillDetailVO();
        BeanUtils.copyProperties(patientMemberBill, billDetailVO);
        billDetailVO.setMember(memberInfoVO);

        if (StringUtils.isNotEmpty(patientMemberBill.getCreatedBy()) && !SysCommon.UNKNOWN_ID.equals(patientMemberBill.getCreatedBy())) {
            Employee employee = cisScClinicFeignClient.queryEmployeeById(patientMemberBill.getChainId(), patientMemberBill.getCreatedBy());
            if (employee != null) {
                PatientMemberBillDetailVO.Creator creator = new PatientMemberBillDetailVO.Creator();
                creator.setId(patientMemberBill.getCreatedBy());
                creator.setName(employee.getName());
            }
        }
        return billDetailVO;
    }

    private void autoUpdateMember(PatientBasicInfo patientBasicInfo, String clinicId, String chainId, String employeeId, Integer hisType, PatientContext context) {
        try {
            if (StringUtils.isEmpty(patientBasicInfo.getMobile())
                    || !CommonUtil.isValidPhoneNumber(patientBasicInfo.getMobile(), patientBasicInfo.getCountryCode())
                    || patientBasicInfo.getMemberFlag() == 1) {
                return;
            }

            String memberTypeId = patientBasicInfo.getMemberTypeId();
            if (TextUtils.isEmpty(memberTypeId)) {
                CrmPatientSetting crmPatientSetting = propertyService.getPropertyValueByKey(PropertyKey.CRM_PATIENT, chainId, CrmPatientSetting.class);
                if (!Objects.isNull(crmPatientSetting) && crmPatientSetting.getEnableAutoUpgradeMember() == 1) {
                    memberTypeId = crmPatientSetting.getAutoUpgradeMemberType();
                }
            }

            PatientMemberType patientMemberType = null;
            if (!StringUtils.isEmpty(memberTypeId)) {
                patientMemberType = patientMemberTypeService.getPatientMemberTypeById(memberTypeId, chainId);
                if (patientMemberType == null) {
                    log.warn("member type {} no exist.", memberTypeId);
                    throw new NotFoundException("会员类型 " + patientMemberType + " 不存在");
                }
            } else {
                // 药店默认就是“普通会员”
                if (hisType == Organ.HisType.CIS_HIS_TYPE_PHARMACY) {
                    patientMemberType = getPharmacyDefaultMemberType(chainId);
                    if (patientMemberType != null) {
                        memberTypeId = patientMemberType.getId();
                    }
                }
            }

            if (StringUtils.isEmpty(memberTypeId)) {
                return;
            }

            PatientMember patientMember = new PatientMember();
            patientMember.setPatientId(patientBasicInfo.getId());
            patientMember.setMemberTypeId(memberTypeId);
            patientMember.setRemark("");
            patientMember.setChainId(chainId);
            patientMember.setBalance(BigDecimal.ZERO);
            patientMember.setPrincipal(BigDecimal.ZERO);
            patientMember.setPresent(BigDecimal.ZERO);
            patientMember.setCreatedClinicId(clinicId);
            patientMember.setStatus(1);
            Date now = new Date();
            patientMember.setCreated(now);
            patientMember.setLastModified(now);
            patientMember.setCreatedBy(employeeId);
            patientMember.setLastModifiedBy(employeeId);
            int rows = patientMemberMapper.insertPatientMember(patientMember);
            if (rows == 1 && context != null) {// rows == 1才是新增、rows == 2 说明走到了 DUPLICATE KEY UPDATE 逻辑
                context.setPatientMemberAction(PatientContext.PatientMemberAction.ADD).setMemberTypeId(memberTypeId);
            }
            patientBasicInfo.setMemberInfo(patientMember);
            patientBasicInfo.setMemberFlag(1);

            patientMemberMapper.updatePatientMemberFlag(patientMember.getPatientId(), (byte) 1, employeeId);
        } catch (Exception e) {
            log.warn("auto update member failed.", e);
            if (!TextUtils.isEmpty(patientBasicInfo.getMemberTypeId())) {
                // 非自动升级，失败返回错误
                throw e;
            }
        }
    }

    private PatientMemberType getPharmacyDefaultMemberType(String chainId) {
        PatientMemberType patientMemberType = this.patientMemberTypeRepository.findByChainIdAndNameAndInnerFlagAndStatus(
                chainId, PatientMemberTypeService.PHARMACY_DEFAULT_MEMBER_TYPE_NAME, 1, 1);
        return patientMemberType;
    }

    public PatientMember autoUpgradePatientMember(Patient patient,
                                                  String clinicId,
                                                  String chainId,
                                                  String employeeId,
                                                  String memberTypeId,
                                                  PatientContext context) {
        try {
            if (StringUtils.isEmpty(patient.getMobile()) || !CommonUtil.isValidPhoneNumber(patient.getMobile(), patient.getCountryCode()) || patient.getIsMember() == 1) {
                return null;
            }

            String autoUpgradeMemberTypeId = memberTypeId;
            if (TextUtils.isEmpty(memberTypeId)) {
                CrmPatientSetting crmPatientSetting = propertyService.getPropertyValueByKey(PropertyKey.CRM_PATIENT, chainId, CrmPatientSetting.class);
                if (Objects.isNull(crmPatientSetting) || crmPatientSetting.getEnableAutoUpgradeMember() == 0 || StringUtils.isBlank(crmPatientSetting.getAutoUpgradeMemberType())) {
                    return null;
                }
                autoUpgradeMemberTypeId = crmPatientSetting.getAutoUpgradeMemberType();
            }

            PatientMemberType patientMemberType = patientMemberTypeService.getPatientMemberTypeById(autoUpgradeMemberTypeId, chainId);
            if (patientMemberType == null) {
                log.warn("member type {} no exist.", memberTypeId);
                throw new NotFoundException("会员类型 " + patientMemberType + " 不存在");
            }

            PatientMember patientMember = new PatientMember();
            patientMember.setPatientId(patient.getId());
            patientMember.setMemberTypeId(autoUpgradeMemberTypeId);
            patientMember.setRemark("");
            patientMember.setChainId(chainId);
            patientMember.setBalance(BigDecimal.ZERO);
            patientMember.setPrincipal(BigDecimal.ZERO);
            patientMember.setPresent(BigDecimal.ZERO);
            patientMember.setCreatedClinicId(clinicId);
            patientMember.setStatus(1);
            Date now = new Date();
            patientMember.setCreated(now);
            patientMember.setLastModified(now);
            patientMember.setCreatedBy(employeeId);
            patientMember.setLastModifiedBy(employeeId);

            patientMemberRepository.save(patientMember);
            if (context != null) {
                context.setPatientMemberAction(PatientContext.PatientMemberAction.ADD).setMemberTypeId(patientMember.getMemberTypeId());
            }

            return patientMember;
        } catch (Exception e) {
            log.warn("auto update member failed.", e);
            if (!TextUtils.isEmpty(memberTypeId)) {
                // 非自动升级，失败返回错误
                throw e;
            }
        }
        return null;
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<PatientMemberByMobileLast4VO> queryPatientMemberByMobileLast4(String key, String chainId, String clinicId, String employeeId) {

        AbcListPage<PatientMemberByMobileLast4VO> res = new AbcListPage<>();
        if (StringUtils.isBlank(key) && !StringUtils.isNumeric(key.trim())) {
            res.setRows(new ArrayList<>());
            return res;
        }

        List<PatientEsDto> patientEsDataList = searchService.searchPatient(chainId, clinicId, employeeId, SearchService.CRM_PATIENT_MEMBER, key, 0, 30);

        List<String> patientIds = patientEsDataList.stream().map(PatientEsDto::getId).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(patientIds)) {
            res.setRows(new ArrayList<>());
            return res;
        }

        Map<String, PatientEsDto> patientEsDtoMap = ListUtils.toMap(patientEsDataList, PatientEsDto::getId);

        List<PatientMember> patientMembers = getPatientMembersByPatientIds(patientIds, chainId);
        patientMembers.sort((o1, o2) -> {
            int res1 = StringUtils.isNotEmpty(o1.getCreatedClinicId()) && o1.getCreatedClinicId().equals(clinicId) ? 1 : 0;
            int res2 = StringUtils.isNotEmpty(o2.getCreatedClinicId()) && o2.getCreatedClinicId().equals(clinicId) ? 1 : 0;
            return res1 >= res2 ? 1 : 0;
        });

        List<String> memberTypeIds = patientMembers.stream().map(PatientMember::getMemberTypeId).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());
        List<PatientMemberType> memberTypes = patientMemberTypeService.getPatientMemberTypesByIds(memberTypeIds, chainId);
        Map<String, PatientMemberType> memberTypeMap = ListUtils.toMap(memberTypes, PatientMemberType::getId);

        res.setRows(patientMembers.stream()
                .filter(patientMember -> StringUtils.isNotEmpty(patientMember.getPatientId())
                        && memberTypeMap.containsKey(patientMember.getMemberTypeId()))
                .map(patientMember -> {
                    PatientEsDto patientEsDto = patientEsDtoMap.get(patientMember.getPatientId());
                    PatientMemberByMobileLast4VO memberInfo = new PatientMemberByMobileLast4VO();
                    BeanUtils.copyProperties(patientEsDto, memberInfo);
                    memberInfo.setMemberTypeId(patientMember.getMemberTypeId());
                    memberInfo.setMemberTypeName(memberTypeMap.get(memberInfo.getMemberTypeId()).getName());
                    memberInfo.setMobile(AESUtils.decryptFromBase64String(patientEsDto.getMobile(), dbSecret));
                    memberInfo.setIdCard(AESUtils.decryptFromBase64String(patientEsDto.getIdCard(), dbSecret));
                    return memberInfo;
                }).collect(Collectors.toList()));
        res.setTotal(res.getRows().size());
        return res;
    }

    @Transactional
    public PatientMemberRechargeOrderRsp createPatientMemberOrder(String patientId,
                                                                  String chainId,
                                                                  String clinicId,
                                                                  String employeeId,
                                                                  PatientMemberOrderReq req) {
        PatientMemberRechargeOrderReq memberRechargeOrderReq = new PatientMemberRechargeOrderReq();
        memberRechargeOrderReq.setOrderSource(PatientMemberOrder.OrderSource.PC);
        memberRechargeOrderReq.setChainId(chainId);
        memberRechargeOrderReq.setClinicId(clinicId);
        memberRechargeOrderReq.setPatientId(patientId);
        memberRechargeOrderReq.setOperatorId(employeeId);
        memberRechargeOrderReq.setPrincipal(req.getPrincipal() == null ? BigDecimal.ZERO : req.getPrincipal());
        memberRechargeOrderReq.setPresent(req.getPresent() == null ? BigDecimal.ZERO : req.getPresent());

        memberRechargeOrderReq.setSellerUserId(req.getSellerUserId());
        memberRechargeOrderReq.setChargeComment(req.getChargeComment());
        return createPatientMemberRechargeOrder(memberRechargeOrderReq);
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public PatientMemberOrderStatusRsp getPatientMemberOrderStatus(String chainId, String clinicId, String patientMemberOrderId) {

        if (StringUtils.isBlank(patientMemberOrderId)) {
            throw new NotFoundException();
        }

        PatientMemberOrder patientMemberOrder = patientMemberOrderRepository.findById(patientMemberOrderId).orElse(null);
        if (patientMemberOrder == null) {
            throw new NotFoundException();
        }

        PatientMemberOrderStatusRsp rsp = new PatientMemberOrderStatusRsp();
        rsp.setStatus(PatientMemberOrderStatusRsp.convertToFrontStatus(patientMemberOrder.getStatus()));
        rsp.setPatientMemberBillId(patientMemberOrder.getMemberBillId());
        return rsp;
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public AbcListPage<PatientMemberInfoVO> getPatientMemberInfosByPatientIds(List<String> patientIds, String chainId) {

        if (CollectionUtils.isEmpty(patientIds) || StringUtils.isEmpty(chainId)) {
            return new AbcListPage<>();
        }

        List<Patient> patients = patientEntityService.getPatientsByIds(patientIds, chainId);
        List<String> filterPatientIds = patients.stream().filter(patient -> patient.getIsMember() == 1).map(Patient::getId).collect(Collectors.toList());

        Map<String, Patient> patientMap = ListUtils.toMap(patients, Patient::getId);

        List<PatientMember> patientMembers = getPatientMembersByPatientIds(filterPatientIds, chainId);

        List<String> memberTypeIds = patientMembers.stream().map(PatientMember::getMemberTypeId).filter(StringUtils::isNotEmpty).distinct().collect(Collectors.toList());

        List<PatientMemberType> patientMemberTypes = patientMemberTypeService.getPatientMemberTypesByIds(memberTypeIds, chainId);
        Map<String, PatientMemberType> patientMemberTypeMap = ListUtils.toMap(patientMemberTypes, PatientMemberType::getId);


        AbcListPage<PatientMemberInfoVO> rsp = new AbcListPage<>();
        rsp.setRows(patientMembers.stream()
                .filter(patientMember -> patientMember != null && StringUtils.isNotEmpty(patientMember.getMemberTypeId()))
                .map(patientMember -> convertToPatientMemberInfoVO(patientMember, patientMap.get(patientMember.getPatientId()), patientMemberTypeMap.get(patientMember.getMemberTypeId())))
                .filter(Objects::nonNull)
                .collect(Collectors.toList()));

        return rsp;
    }

    private PatientMemberInfoVO convertToPatientMemberInfoVO(PatientMember patientMember, Patient patient, PatientMemberType patientMemberType) {
        if (patient == null || patientMemberType == null) {
            return null;
        }

        PatientBasicInfoView patientBasicInfoView = new PatientBasicInfoView();
        BeanUtils.copyProperties(patient, patientBasicInfoView);

        PatientMemberTypeVO memberTypeVO = new PatientMemberTypeVO();
        memberTypeVO.setId(patientMemberType.getId());
        memberTypeVO.setName(patientMemberType.getName());

        PatientMemberInfoVO memberInfoVO = new PatientMemberInfoVO();
        memberInfoVO.setPresent(patientMember.getPresent());
        memberInfoVO.setPrincipal(patientMember.getPrincipal());
        memberInfoVO.setPatient(patientBasicInfoView);
        memberInfoVO.setMemberType(memberTypeVO);
        return memberInfoVO;
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public PatientRelateMemberRsp getPatientRelateMemberInfo(String chainId, String patientId) {
        PatientRelateMemberRsp rsp = new PatientRelateMemberRsp();
        List<PatientMemberInfo> relatePatientMembers = new ArrayList<>(2);
        PatientMemberInfo patientMemberInfo = getPatientMemberInfoBasic(chainId, patientId);
        if (patientMemberInfo != null) {
            relatePatientMembers.add(patientMemberInfo);
        }
        PatientFamily patientFamily = patientFamilyService.getPatientFamilyByPatientIdAndChainId(patientId, chainId);
        if (patientFamily != null && StringUtils.isNotBlank(patientFamily.getParentId())) {
            PatientMemberInfo parentPatientMemberInfo = getPatientMemberInfoBasic(chainId, patientFamily.getParentId());
            if (parentPatientMemberInfo != null) {
                relatePatientMembers.add(parentPatientMemberInfo);
            }
        }

        List<String> patientIds = relatePatientMembers.stream().map(PatientMemberInfo::getPatientId).collect(Collectors.toList());
        List<PatientPoints> patientPointsList = patientPointsService.getPatientPointsByPatientIds(patientIds, chainId);
        Map<String, PatientPoints> patientPointsMap = ListUtils.toMap(patientPointsList, PatientPoints::getId);
        relatePatientMembers.forEach(patientMember -> {
            PatientPoints patientPoints = patientPointsMap.get(patientMember.getPatientId());
            if (patientPoints != null) {
                patientMember.setPoints(patientPoints.getPoints());
                patientMember.setPointsTotal(patientPoints.getPointsTotal());
            }
        });

        rsp.setRelatePatientMembers(relatePatientMembers);
        return rsp;
    }

    /**
     * 获取患者可用的会员信息集合，考虑家庭成员权益共享
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public List<PatientMemberInfo> getPatientRelateAvailableMembers(String chainId, String patientId) {
        List<PatientMemberInfo> relatePatientMembers = new ArrayList<>(2);
        // 1、先查询本人的会员信息
        PatientMemberInfo patientMemberInfo = getPatientMemberInfoBasic(chainId, patientId);
        if (patientMemberInfo != null) {
            relatePatientMembers.add(patientMemberInfo);
        }
        // 2、查询property是否支持家庭成员共享
        CrmPatientFamily crmPatientFamilyShareRights = propertyService.getPropertyValueByKey(PropertyKey.CRM_PATIENT_FAMILY, chainId, CrmPatientFamily.class);
        if (Optional.ofNullable(crmPatientFamilyShareRights)
                .map(CrmPatientFamily::getSharedRights)
                .map(CrmPatientFamily.SharedRights::getMemberBalanceAndDiscount)
                .orElse(0) == 1){
            // 支持家庭成员共享，查询家长的会员信息
            PatientFamily patientFamily = patientFamilyService.getPatientFamilyByPatientIdAndChainId(patientId, chainId);
            if (patientFamily != null && StringUtils.isNotBlank(patientFamily.getParentId())) {
                PatientMemberInfo parentPatientMemberInfo = getPatientMemberInfoBasic(chainId, patientFamily.getParentId());
                if (parentPatientMemberInfo != null) {
                    relatePatientMembers.add(parentPatientMemberInfo);
                }
            }
        }

        List<String> patientIds = relatePatientMembers.stream().map(PatientMemberInfo::getPatientId).collect(Collectors.toList());
        List<PatientPoints> patientPointsList = patientPointsService.getPatientPointsByPatientIds(patientIds, chainId);
        Map<String, PatientPoints> patientPointsMap = ListUtils.toMap(patientPointsList, PatientPoints::getId);
        relatePatientMembers.forEach(patientMember -> {
            PatientPoints patientPoints = patientPointsMap.get(patientMember.getPatientId());
            if (patientPoints != null) {
                patientMember.setPoints(patientPoints.getPoints());
                patientMember.setPointsTotal(patientPoints.getPointsTotal());
            }
        });

        return relatePatientMembers;
    }

    @Transactional
    public void updateMemberTypeForPatient(String chainId,
                                           String clinicId,
                                           String patientId,
                                           UpdatePatientBasicInfoReq req,
                                           String employeeId,
                                           PatientContext context) {
        PatientBasicInfo patientBasicInfo = new PatientBasicInfo();
        patientBasicInfo.setChainId(chainId);
        patientBasicInfo.setClinicId(clinicId);
        patientBasicInfo.setId(patientId);
        patientBasicInfo.setLastModifiedBy(employeeId);

        //2. 更新会员信息 如果会员没有，createPatientMember
        PatientMemberInfo memberInfoFromDB = patientMemberMapper.selectAllPatientMemberInfo(chainId, patientId);
        if (memberInfoFromDB == null) {
            createPatientMember(patientBasicInfo, employeeId, false, context);
        } else if (memberInfoFromDB.getMemberTypeInfo() != null && !TextUtils.equals(memberInfoFromDB.getMemberTypeInfo().getMemberTypeId(), req.getMemberTypeId())) {
            PatientMember patientMember = new PatientMember();
            patientMember.setChainId(chainId);
            patientMember.setPatientId(patientId);
            patientMember.setMemberTypeId(req.getMemberTypeId());
            patientMember.setLastModified(new Date());
            patientMember.setLastModifiedBy(employeeId);
            patientMember.setStatus(1);
            patientMemberMapper.updatePatientMemberInfo(patientMember);
            if (context != null) {
                context.setPatientMemberAction(memberInfoFromDB.getStatus() == 99 ? PatientContext.PatientMemberAction.ADD : PatientContext.PatientMemberAction.UPDATE);
                context.setMemberTypeId(patientMember.getMemberTypeId());
                if (context.getPatientMemberAction() == PatientContext.PatientMemberAction.UPDATE) {
                    context.setBeforePatientMember(PatientMemberConvertor.INST.toPatientMember(memberInfoFromDB));
                }
            }
        }
    }

    public void openApiUpdatePatientMember(String patientId, OpenApiUpdatePatientMemberReq req, PatientContext patientContext) {
        PatientBasicInfo patientBasicInfo = new PatientBasicInfo();
        patientBasicInfo.setId(patientId);
        patientBasicInfo.setChainId(req.getChainId());
        patientBasicInfo.setClinicId(req.getClinicId()); // 开卡门店ID
        patientBasicInfo.setSourceId(req.getSourceId());
        patientBasicInfo.setSourceFrom(req.getSourceFromId());
        patientBasicInfo.setLastModifiedBy(SysCommon.UNKNOWN_ID);
        PatientMember patientMember = new PatientMember();
        patientMember.setRemark(req.getRemark());
        patientMember.setMemberTypeId(req.getMemberTypeId());
        patientBasicInfo.setMemberInfo(patientMember);

        PatientInfo patientInfo = updatePatientMember(patientBasicInfo, SysCommon.UNKNOWN_ID, true, patientContext);
    }

    /**
     * 开放平台会员卡余额支付
     */
    @Transactional(rollbackFor = Exception.class)
    public PatientMemberCardPayRsp openApiPatientMemberPay(OpenApiPatientMemberPayReq req) {
        PatientMemberCardPayReq patientMemberCardPayReq = new PatientMemberCardPayReq();
        patientMemberCardPayReq.setMemberId(req.getMemberId());
        patientMemberCardPayReq.setCash(req.getCash());
        patientMemberCardPayReq.setBusinessType(PatientMemberBill.BusinessType.CHARGE);
        patientMemberCardPayReq.setBusinessId(req.getBusinessId());
        patientMemberCardPayReq.setPassword(req.getPassword());
        patientMemberCardPayReq.setTransactionPatientId(req.getTransactionPatientId());
        patientMemberCardPayReq.setClinicId(req.getClinicId());
        patientMemberCardPayReq.setChainId(req.getChainId());
        patientMemberCardPayReq.setOperatorId(req.getOperatorId());
        patientMemberCardPayReq.setPaySource(PatientMemberBill.PaySource.OPEN_API);
        return rpcPatientMemberPay(patientMemberCardPayReq.getMemberId(), patientMemberCardPayReq);
    }

    /**
     * 开放平台会员卡余额退费
     */
    @Transactional(rollbackFor = Exception.class)
    public PatientMemberCardRefundRsp openApiPatientMemberRefund(OpenApiPatientMemberRefundReq req) {
        PatientMemberCardRefundReq patientMemberCardRefundReq = new PatientMemberCardRefundReq();
        patientMemberCardRefundReq.setMemberId(req.getMemberId());
        patientMemberCardRefundReq.setCash(req.getCash());
        patientMemberCardRefundReq.setTransactionIds(req.getTransactionIds());
        patientMemberCardRefundReq.setBusinessType(PatientMemberBill.BusinessType.CHARGE);
        patientMemberCardRefundReq.setBusinessId(req.getBusinessId());
        patientMemberCardRefundReq.setChainId(req.getChainId());
        patientMemberCardRefundReq.setClinicId(req.getClinicId());
        patientMemberCardRefundReq.setOperatorId(req.getOperatorId());
        patientMemberCardRefundReq.setPaySource(PatientMemberBill.PaySource.OPEN_API);
        return rpcPatientMemberRefund(patientMemberCardRefundReq);
    }

    public RechargeRefundAvailableListView listRefundableRecharge(String chainId, String clinicId, String patientId, Integer payMode, BigDecimal refundPrincipal, BigDecimal refundPresent) {
        PatientBasicInfo patientBasicInfo = patientMemberMapper.selectPatientBasicInfoById(chainId, patientId);
        if (patientBasicInfo == null) {
            throw new CrmCustomException(CrmServiceError.PATIENT_NOT_FOUND);
        }

        PatientMember patientMember = patientBasicInfo.getMemberInfo();
        if (patientMember == null) {
            throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_NOT_FOUND);
        }

        refundPrincipal = MathUtils.wrapBigDecimalOrZero(refundPrincipal);
        refundPresent = MathUtils.wrapBigDecimalOrZero(refundPresent);

        // 校验
        if (refundPrincipal.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ParamNotValidException("退本金不能小于0");
        }
        if (refundPresent.compareTo(BigDecimal.ZERO) < 0) {
            throw new ParamNotValidException("退赠金不能小于0");
        }

        RechargeRefundAvailableListView rsp = new RechargeRefundAvailableListView();
        rsp.setMaxAvailableRefundPresentsAmount(patientMember.getPresent());

        // 找出近半年的充值流水
        LocalDateTime localDateTime = LocalDateTime.now().minusMonths(6);
        Date beginDate = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
        List<PatientMemberBill> patientMemberBills = patientMemberMapper.selectRefundableRechargeBills(chainId, patientId, payMode, beginDate);
        if (CollectionUtils.isEmpty(patientMemberBills)) {
            return rsp;
        }

        List<String> chargeSheetIds = patientMemberBills.stream().map(PatientMemberBill::getChargeSheetId).collect(Collectors.toList());
        List<ChargeRechargeRefundAvailableItem> availableItems = chargeService.queryRechargeRefundableList(chainId, clinicId, CreateChargeSheetForThirdPartyReq.Type.MEMBER_CARD_RECHARGE,
                chargeSheetIds, payMode, refundPrincipal);
        rsp.setAvailableRefundItems(availableItems);

        return rsp;
    }

    @Transactional(rollbackFor = Exception.class)
    public BasicCommonRsp rechargeRefundCallback(RechargeRefundCallbackReq req) {
        return  patientMigrationMemberService.rechargeRefundCallback(req);
    }
}