package cn.abcyun.cis.crm.service;

import cn.abcyun.bis.rpc.sdk.cis.message.crm.PatientMemberTypeMessage;
import cn.abcyun.bis.rpc.sdk.cis.model.mall.promotion.MallMemberDiscountPromotionDetail;
import cn.abcyun.bis.rpc.sdk.cis.model.mall.promotion.MallPromotionGoodsView;
import cn.abcyun.bis.rpc.sdk.cis.model.mall.promotion.SaveMallMemberDiscountPromotionReq;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.PromotionDiscountGoodsView;
import cn.abcyun.bis.rpc.sdk.cis.model.promotion.PromotionMemberDiscountDetail;
import cn.abcyun.bis.rpc.sdk.property.base.PropertyKey;
import cn.abcyun.bis.rpc.sdk.property.base.UpdatePropertyItemReq;
import cn.abcyun.bis.rpc.sdk.property.model.CrmPatientSetting;
import cn.abcyun.bis.rpc.sdk.property.service.PropertyService;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.model.Organ;
import cn.abcyun.cis.commons.rpc.crm.MemberTypeIdAndNameInfo;
import cn.abcyun.cis.commons.rpc.crm.PatientMemberTypesRsp;
import cn.abcyun.cis.commons.util.FillUtils;
import cn.abcyun.cis.commons.util.ListUtils;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import cn.abcyun.cis.crm.api.*;
import cn.abcyun.cis.crm.common.CrmServiceError;
import cn.abcyun.cis.crm.common.JsonUtils;
import cn.abcyun.cis.crm.common.SysCommon;
import cn.abcyun.cis.crm.dao.PatientMemberTypeMapper;
import cn.abcyun.cis.crm.exception.CrmCustomException;
import cn.abcyun.cis.crm.model.PatientMemberType;
import cn.abcyun.cis.crm.mq.RocketMqProducer;
import cn.abcyun.cis.crm.repository.PatientMemberRepository;
import cn.abcyun.cis.crm.repository.PatientMemberTypeRepository;
import cn.abcyun.cis.crm.rpc.client.service.CisMallPromotionService;
import cn.abcyun.cis.idgenerator.AbcIdGenerator;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class PatientMemberTypeService {

    public static final String PHARMACY_DEFAULT_MEMBER_TYPE_NAME = "普通会员";

    private final PatientMemberTypeRepository patientMemberTypeRepository;
    private final PromotionService promotionService;
    private final AbcIdGenerator abcIdGenerator;
    private final PatientMemberRepository patientMemberRepository;
    private final PropertyService propertyService;
    private final PatientMemberTypeMapper patientMemberTypeMapper;
    private final ObjectProvider<PatientMemberTypeService> patientMemberTypeServiceObjectProvider;
    private final RocketMqProducer rocketMqProducer;
    private final CisMallPromotionService cisMallPromotionService;

    public PatientMemberTypeService(PatientMemberTypeRepository patientMemberTypeRepository,
                                    PromotionService promotionService,
                                    AbcIdGenerator abcIdGenerator,
                                    PatientMemberRepository patientMemberRepository,
                                    PropertyService propertyService,
                                    PatientMemberTypeMapper patientMemberTypeMapper,
                                    ObjectProvider<PatientMemberTypeService> patientMemberTypeServiceObjectProvider,
                                    RocketMqProducer rocketMqProducer,
                                    CisMallPromotionService cisMallPromotionService) {
        this.patientMemberTypeRepository = patientMemberTypeRepository;
        this.promotionService = promotionService;
        this.abcIdGenerator = abcIdGenerator;
        this.patientMemberRepository = patientMemberRepository;
        this.propertyService = propertyService;
        this.patientMemberTypeMapper = patientMemberTypeMapper;
        this.patientMemberTypeServiceObjectProvider = patientMemberTypeServiceObjectProvider;
        this.rocketMqProducer = rocketMqProducer;
        this.cisMallPromotionService = cisMallPromotionService;
    }

    public PatientMemberType getPatientMemberTypeByPointsTotal(String chainId, int pointsTotalBefore, int pointsTotalAfter) {
        Map<String, PatientMemberType> patientMemberTypeMap = patientMemberTypeServiceObjectProvider.getObject().getPatientMemberTypeMap(chainId);
        if (MapUtils.isEmpty(patientMemberTypeMap)) {
            return null;
        }

        return patientMemberTypeMap.values()
                .stream().filter(patientMemberType -> patientMemberType.getPointsAutoUpdate() != null
                        && patientMemberType.getPointsAutoUpdate() > pointsTotalBefore
                        && patientMemberType.getPointsAutoUpdate() <= pointsTotalAfter)
                .max(Comparator.comparing(PatientMemberType::getPointsAutoUpdate))
                .orElse(null);
    }

    public PatientMemberType getPatientMemberTypeById(String id, String chainId) {
        if (StringUtils.isEmpty(id)) {
            return null;
        }

        Map<String, PatientMemberType> memberTypeIdToMemberType = patientMemberTypeServiceObjectProvider.getObject().getPatientMemberTypeMap(chainId);
        return memberTypeIdToMemberType.get(id);
    }

    public List<PatientMemberType> getPatientMemberTypesByIds(List<String> ids, String chainId) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }

        Map<String, PatientMemberType> memberTypeIdToMemberType = patientMemberTypeServiceObjectProvider.getObject().getPatientMemberTypeMap(chainId);
        return ids.stream().map(memberTypeIdToMemberType::get).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Cacheable(value = SysCommon.MEMBER_TYPE_REDIS_KEY, key = "#chainId")
    public Map<String, PatientMemberType> getPatientMemberTypeMap(String chainId) {
        List<PatientMemberType> patientMemberTypes = patientMemberTypeRepository.findAllByChainIdAndStatusOrderByCreatedDesc(chainId, 1);
        if (CollectionUtils.isEmpty(patientMemberTypes)) {
            return new HashMap<>();
        }
        Map<String, PatientMemberType> memberTypeIdToMemberType = new LinkedHashMap<>(patientMemberTypes.size());
        for (PatientMemberType patientMemberType : patientMemberTypes) {
            memberTypeIdToMemberType.put(patientMemberType.getId(), patientMemberType);
        }
        return memberTypeIdToMemberType;
    }

    public List<PatientMemberType> getChainPatientMemberTypes(String chainId) {
        return new ArrayList<>(patientMemberTypeServiceObjectProvider.getObject().getPatientMemberTypeMap(chainId).values());
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public PatientMemberTypesRsp listMemberTypeNameByTypeIds(cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientMemberTypesReq memberTypesReq) {
        PatientMemberTypesRsp memberTypesRsp = new PatientMemberTypesRsp();

        // 1、如果为空参数，直接返回空结果
        if (memberTypesReq == null || (StringUtils.isBlank(memberTypesReq.getChainId())
                && org.springframework.util.CollectionUtils.isEmpty(memberTypesReq.getTypeIds()))) {
            memberTypesRsp.setMemberTypeInfos(Lists.newArrayList());
            return memberTypesRsp;
        }

        // 2、调用查询方法
        List<String> typeIds = memberTypesReq.getTypeIds();
        List<PatientMemberType> memberTypes;
        if (StringUtils.isNotBlank(memberTypesReq.getChainId())) {
            memberTypes = getChainPatientMemberTypes(memberTypesReq.getChainId());
            if (!CollectionUtils.isEmpty(typeIds)) {
                memberTypes = memberTypes.stream().filter(memberType -> typeIds.contains(memberType.getId())).collect(Collectors.toList());
            }
        } else {
            memberTypes = patientMemberTypeRepository.findByIdInAndStatus(typeIds, 1);
        }

        if (CollectionUtils.isEmpty(memberTypes)) {
            memberTypesRsp.setMemberTypeInfos(Lists.newArrayList());
            return memberTypesRsp;
        }

        // 3、将结果转换为rpc结果
        List<MemberTypeIdAndNameInfo> rpcMemberTypes = memberTypes.stream()
                .map(memberType -> {
                    MemberTypeIdAndNameInfo rpcMemberType = new MemberTypeIdAndNameInfo();
                    rpcMemberType.setMemberTypeId(memberType.getId());
                    rpcMemberType.setMemberTypeName(memberType.getName());
                    return rpcMemberType;
                })
                .collect(Collectors.toList());
        memberTypesRsp.setMemberTypeInfos(rpcMemberTypes);
        return memberTypesRsp;
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public PatientMemberTypeListVO getPatientMemberTypeList(String chainId, int limit, int offset, int withDetail) {

        int page = offset / limit;
        PageRequest pageRequest = PageRequest.of(page, limit);
        Page<PatientMemberType> memberTypePage = patientMemberTypeRepository.findByChainIdAndStatusOrderByCreatedDesc(chainId, 1, pageRequest);

        //获取disCountList
        List<String> memberTypeIds = memberTypePage.getContent().stream().map(PatientMemberType::getId).collect(Collectors.toList());
        List<PromotionMemberDiscountDetail> discountDetails = promotionService.listMemberDiscountByIds(memberTypeIds, chainId);
        Map<String, PromotionMemberDiscountDetail> discountMap = ListUtils.toMap(discountDetails, PromotionMemberDiscountDetail::getMemberTypeId);

        // 查询微商城折扣权益
        Map<String, MallMemberDiscountPromotionDetail> memberTypeIdMallPromotionMap = ListUtils.toMap(cisMallPromotionService.listPromotionsByMemberTypes(chainId, memberTypeIds), MallMemberDiscountPromotionDetail::getMemberTypeId);

        //获取会员人数
        List<PatientMemberTypeAbstractVO> memberCountList = new ArrayList<>();
        if (withDetail == 1) {
            if (CollectionUtils.isNotEmpty(memberTypeIds)) {
                memberCountList = patientMemberTypeMapper.selectPatientMemberTypeAbstracts(memberTypeIds, chainId);
            }
        }
        Map<String, Integer> memberCountMap = memberCountList.stream().collect(Collectors.toMap(PatientMemberTypeAbstractVO::getId, PatientMemberTypeAbstractVO::getMemberCount, (a, b) -> a));

        PatientMemberTypeListVO memberTypeListVO = new PatientMemberTypeListVO();
        List<PatientMemberTypeAbstractVO> memberTypeVOList = memberTypePage.getContent()
                .stream().map(patientMemberType -> {
                    PatientMemberTypeAbstractVO memberTypeAbstractVO = new PatientMemberTypeAbstractVO();
                    BeanUtils.copyProperties(patientMemberType, memberTypeAbstractVO);
                    PromotionMemberDiscountDetail discountDetail = discountMap.get(patientMemberType.getId());
                    memberTypeAbstractVO.setDiscountBenefits(discountDetail == null ? "" : discountDetail.getDiscountBenefits());
                    memberTypeAbstractVO.setMallDiscountBenefits(
                            Optional.ofNullable(memberTypeIdMallPromotionMap.get(patientMemberType.getId())).map(MallMemberDiscountPromotionDetail::getDiscountBenefitsDisplay).orElse("")
                    );
                    memberTypeAbstractVO.setMemberCount(memberCountMap.getOrDefault(patientMemberType.getId(), 0));
                    return memberTypeAbstractVO;
                })
                .collect(Collectors.toList());

        memberTypeListVO.setRows(memberTypeVOList);
        memberTypeListVO.setLimit(limit);
        memberTypeListVO.setOffset(offset);
        memberTypeListVO.setTotal((int) memberTypePage.getTotalElements());
        return memberTypeListVO;
    }

    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public PatientMemberTypeVO getPatientMemberTypeVO(String chainId, String memberTypeId) {
        PatientMemberType patientMemberType = patientMemberTypeServiceObjectProvider.getObject().getPatientMemberTypeMap(chainId).get(memberTypeId);
        if (patientMemberType == null) {
            log.warn("member type {} no exist.", memberTypeId);
            throw new NotFoundException();
        }

        PromotionMemberDiscountDetail discountDetail = promotionService.getPromotionMemberDiscount(memberTypeId, chainId);

        // 获取微商城折扣权益
        MallMemberDiscountPromotionDetail mallPromotionDiscountDetail = cisMallPromotionService.getPromotionMemberDiscount(memberTypeId, chainId);
        return convertToMemberTypeVO(
                patientMemberType,
                discountDetail != null ? discountDetail.getGoodsList() : null,
                mallPromotionDiscountDetail != null ? mallPromotionDiscountDetail.getGoodsList() : null);
    }

    private PatientMemberTypeVO convertToMemberTypeVO(PatientMemberType patientMemberType, List<PromotionDiscountGoodsView> discountList, List<MallPromotionGoodsView> mallDiscountList) {
        PatientMemberTypeVO memberTypeVO = new PatientMemberTypeVO();
        BeanUtils.copyProperties(patientMemberType, memberTypeVO);
        memberTypeVO.setDiscountList(discountList);
        memberTypeVO.setMallDiscountList(mallDiscountList);
        return memberTypeVO;
    }

    @Transactional
    @CacheEvict(value = SysCommon.MEMBER_TYPE_REDIS_KEY, key = "#chainId")
    public PatientMemberTypeVO updatePatientMemberType(String chainId, String clinicId, String memberTypeId, UpdatePatientMemberTypeReq req, String employeeId) {
        checkMemberTypeReq(chainId, req, memberTypeId);
        PatientMemberType patientMemberType = patientMemberTypeRepository.findByIdAndChainIdAndStatus(memberTypeId, chainId, 1);
        if (patientMemberType == null) {
            log.warn("member type {} no exist.", memberTypeId);
            throw new NotFoundException();
        }

        if (!StringUtils.equals(req.getName(), patientMemberType.getName()) && Objects.equals(1, patientMemberType.getInnerFlag())) {
            throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_TYPE_INNER_NAME_CAN_NOT_UPDATE);
        }
        PatientMemberType oldPatientMemberType = JsonUtils.readValue(JsonUtils.dump(patientMemberType), PatientMemberType.class);

        //更新会员折扣信息
        List<PromotionDiscountGoodsView> discountListVO;
        if (Objects.equals(patientMemberType.getInnerFlag(), 1) && CollectionUtils.isEmpty(req.getDiscountList())) {
            // 如果是内置会员类型，且没有配置折扣商品，则删除营销，主要是为了不改动 promotion 的逻辑
            // 保证营销一定有折扣商品
            promotionService.deletePromotionMemberDiscount(chainId, employeeId, memberTypeId);
            discountListVO = new ArrayList<>();
        } else {
            discountListVO = promotionService.updatePromotionMemberDiscount(chainId, clinicId, memberTypeId, req, employeeId);
        }

        // 修改微商城会员折扣信息
        SaveMallMemberDiscountPromotionReq saveMallPromotionMemberDiscountReq = new SaveMallMemberDiscountPromotionReq();
        saveMallPromotionMemberDiscountReq.setChainId(chainId);
        saveMallPromotionMemberDiscountReq.setName(req.getName());
        saveMallPromotionMemberDiscountReq.setOperatorId(employeeId);
        saveMallPromotionMemberDiscountReq.setGoodsList(req.getMallDiscountList());
        MallMemberDiscountPromotionDetail mallPromotionDiscountDetail = cisMallPromotionService.updatePromotionMemberDiscount(memberTypeId, saveMallPromotionMemberDiscountReq);

        if (!StringUtils.equals(req.getName(), patientMemberType.getName())
                || !StringUtils.equals(req.getBenefits(), patientMemberType.getBenefits())
                || !Objects.equals(patientMemberType.getPointsAutoUpdate(), req.getPointsAutoUpdate())) {
            patientMemberType.setBenefits(req.getBenefits());
            // 内置会员不支持编辑名称和升级条件
            if (!Objects.equals(1, patientMemberType.getInnerFlag())) {
                patientMemberType.setName(req.getName());
                patientMemberType.setPointsAutoUpdate(req.getPointsAutoUpdate());
            }
            FillUtils.fillLastModifiedBy(patientMemberType, employeeId);
            patientMemberTypeRepository.save(patientMemberType);
            rocketMqProducer.sendPatientMemberTypeMessage(PatientMemberTypeMessage.Type.UPDATE, chainId, oldPatientMemberType, patientMemberType, employeeId, Instant.now());
        }

        return convertToMemberTypeVO(patientMemberType, discountListVO, Optional.ofNullable(mallPromotionDiscountDetail).map(MallMemberDiscountPromotionDetail::getGoodsList).orElse(Lists.newArrayList()));
    }

    @Transactional
    @CacheEvict(value = SysCommon.MEMBER_TYPE_REDIS_KEY, key = "#chainId")
    public PatientMemberTypeVO createPatientMemberType(String chainId, String clinicId, String employeeId, CreatePatientMemberTypeReq req) {
        checkMemberTypeReq(chainId, req, "");
        PatientMemberType patientMemberType = new PatientMemberType();
        patientMemberType.setId(abcIdGenerator.getUUID());
        patientMemberType.setName(req.getName());
        patientMemberType.setChainId(chainId);
        patientMemberType.setBenefits(req.getBenefits());
        patientMemberType.setPointsAutoUpdate(req.getPointsAutoUpdate());
        patientMemberType.setStatus(1);
        FillUtils.fillCreatedBy(patientMemberType, employeeId);
        patientMemberTypeRepository.save(patientMemberType);
        rocketMqProducer.sendPatientMemberTypeMessage(PatientMemberTypeMessage.Type.ADD, chainId, null, patientMemberType, employeeId, patientMemberType.getCreated());
        List<PromotionDiscountGoodsView> discountListVO = promotionService.createPromotionMemberDiscount(chainId, clinicId, patientMemberType.getId(), req, employeeId);

        // 修改微商城会员折扣信息
        SaveMallMemberDiscountPromotionReq saveMallPromotionMemberDiscountReq = new SaveMallMemberDiscountPromotionReq();
        saveMallPromotionMemberDiscountReq.setChainId(chainId);
        saveMallPromotionMemberDiscountReq.setName(req.getName());
        saveMallPromotionMemberDiscountReq.setOperatorId(employeeId);
        saveMallPromotionMemberDiscountReq.setGoodsList(req.getMallDiscountList());
        MallMemberDiscountPromotionDetail mallPromotionDiscountDetail = cisMallPromotionService.updatePromotionMemberDiscount(patientMemberType.getId(), saveMallPromotionMemberDiscountReq);

        return convertToMemberTypeVO(patientMemberType, discountListVO, Optional.ofNullable(mallPromotionDiscountDetail).map(MallMemberDiscountPromotionDetail::getGoodsList).orElse(Lists.newArrayList()));
    }

    private void checkMemberTypeReq(String chainId, UpdatePatientMemberTypeReq req, String memberTypeId) {
        boolean isExistSameName = patientMemberTypeRepository.existsPatientMemberTypeByChainIdAndNameAndStatusAndIdNot(chainId, req.getName(), 1, memberTypeId);
        if (isExistSameName) {
            throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_TYPE_NAME_EXIST);
        }

        if (req.getPointsAutoUpdate() != null) {
            boolean isExistSamePointsUpdate = patientMemberTypeRepository.existsPatientMemberTypeByChainIdAndPointsAutoUpdateAndStatusAndIdNot(chainId, req.getPointsAutoUpdate(), 1, memberTypeId);
            if (isExistSamePointsUpdate) {
                throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_TYPE_SAME_POINTS_UPDATE);
            }
        }
    }

    @Transactional
    @CacheEvict(value = SysCommon.MEMBER_TYPE_REDIS_KEY, key = "#chainId")
    public PatientMemberTypeVO deletePatientMemberType(String chainId, String employeeId, String memberTypeId) {
        PatientMemberType patientMemberType = patientMemberTypeRepository.findByIdAndChainIdAndStatus(memberTypeId, chainId, 1);
        if (patientMemberType == null) {
            log.warn("member type {} no exist.", memberTypeId);
            throw new NotFoundException();
        }

        if (Objects.equals(1, patientMemberType.getInnerFlag())) {
            throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_TYPE_INNER_DEL_NOT);
        }

        boolean isExistMemberTypeHasUser = patientMemberRepository.existsPatientMemberByChainIdAndMemberTypeIdAndStatus(chainId, memberTypeId, 1);
        if (isExistMemberTypeHasUser) {
            throw new CrmCustomException(CrmServiceError.PATIENT_MEMBER_TYPE_DEL_NOT);
        }

        CrmPatientSetting crmPatientSetting = propertyService.getPropertyValueByKey(PropertyKey.CRM_PATIENT, chainId, CrmPatientSetting.class);
        if (crmPatientSetting != null && crmPatientSetting.getEnableAutoUpgradeMember() == 1
                && StringUtils.equals(crmPatientSetting.getAutoUpgradeMemberType(), memberTypeId)) {
            log.warn("清空自动升级会员类型设置 {}", crmPatientSetting);
            crmPatientSetting.setAutoUpgradeMemberType(null);
            UpdatePropertyItemReq<CrmPatientSetting> req = new UpdatePropertyItemReq<>();
            req.setPropertyKey(PropertyKey.CRM_PATIENT);
            req.setScopeId(chainId);
            req.setOperatorId(employeeId);
            req.setValue(crmPatientSetting);
            propertyService.updatePropertyValueByKey(req);
        }

        patientMemberType.setStatus(99);
        FillUtils.fillLastModifiedBy(patientMemberType, employeeId);
        patientMemberTypeRepository.save(patientMemberType);
        promotionService.deletePromotionMemberDiscount(chainId, employeeId, memberTypeId);
        // 删除微商城会员折扣
        cisMallPromotionService.deletePromotionMemberDiscount(memberTypeId, chainId, employeeId);

        rocketMqProducer.sendPatientMemberTypeMessage(PatientMemberTypeMessage.Type.DELETE, chainId, patientMemberType, null, employeeId, Instant.now());
        return convertToMemberTypeVO(patientMemberType, null, null);
    }

    /**
     * 药店添加内置的“普通会员”
     */
    @CacheEvict(value = SysCommon.MEMBER_TYPE_REDIS_KEY, key = "#organ.parentId")
    public void insertDefaultMemberTypeForPharmacy(Organ organ) {
        log.info("insertDefaultMemberType for: {}", cn.abcyun.cis.commons.util.JsonUtils.dump(organ));
        if (organ == null) {
            return;
        }

        String chainId = organ.getParentId();
        PatientMemberType existPatientMemberType = this.patientMemberTypeRepository.findByChainIdAndNameAndInnerFlagAndStatus(chainId, PHARMACY_DEFAULT_MEMBER_TYPE_NAME, 1, 1);
        if (existPatientMemberType != null) {
            return;
        }

        PatientMemberType patientMemberType = new PatientMemberType();
        patientMemberType.setId(abcIdGenerator.getUUID());
        patientMemberType.setName(PHARMACY_DEFAULT_MEMBER_TYPE_NAME);
        patientMemberType.setChainId(chainId);
        patientMemberType.setStatus(1);
        patientMemberType.setInnerFlag(1);
        String operator = "sys";
        FillUtils.fillCreatedBy(patientMemberType, operator);
        patientMemberTypeRepository.save(patientMemberType);
        rocketMqProducer.sendPatientMemberTypeMessage(PatientMemberTypeMessage.Type.ADD, chainId, null, patientMemberType, operator, patientMemberType.getCreated());
    }
}
