package cn.abcyun.cis.crm.service;

import cn.abcyun.bis.rpc.sdk.cis.message.crm.PatientConsultantChangeMessage;
import cn.abcyun.bis.rpc.sdk.cis.message.examination.ExaminationSheetMessage;
import cn.abcyun.bis.rpc.sdk.cis.message.medicalplan.MedicalPlanMessage;
import cn.abcyun.bis.rpc.sdk.cis.model.examination.ExaminationBusinessType;
import cn.abcyun.bis.rpc.sdk.cis.model.examination.ExaminationStatusV2;
import cn.abcyun.bis.rpc.sdk.cis.model.goods.EmployeeView;
import cn.abcyun.bis.rpc.sdk.cis.model.medicalplan.MedicalPlan;
import cn.abcyun.bis.rpc.sdk.cis.model.patientorder.HisPatientOrder;
import cn.abcyun.bis.rpc.sdk.cis.model.patientorder.PatientOrderHospitalOperationAction;
import cn.abcyun.bis.rpc.sdk.his.message.patientorder.HisPatientOrderMessage;
import cn.abcyun.bis.rpc.sdk.pe.message.order.PeSheetMessage;
import cn.abcyun.bis.rpc.sdk.pe.model.order.PeSheet;
import cn.abcyun.cis.commons.util.FillUtils;
import cn.abcyun.cis.core.db.UseReadOnlyDB;
import cn.abcyun.cis.crm.api.UpdatePatientBasicInfoReq;
import cn.abcyun.cis.crm.common.JsonUtils;
import cn.abcyun.cis.crm.common.SysCommon;
import cn.abcyun.cis.crm.convertor.PatientClinicConvertor;
import cn.abcyun.cis.crm.dao.PatientClinicMapper;
import cn.abcyun.cis.crm.entity.patient.clinic.PatientClinicReq;
import cn.abcyun.cis.crm.exception.CrmCustomException;
import cn.abcyun.cis.crm.model.Patient;
import cn.abcyun.cis.crm.model.PatientClinic;
import cn.abcyun.cis.crm.repository.PatientClinicRepository;
import cn.abcyun.cis.crm.rpc.client.service.CisScClinicFeignClient;
import cn.abcyun.cis.crm.rpc.entity.PatientClinicImportReq;
import cn.abcyun.cis.crm.rpc.entity.PatientClinicImportRsp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2022/3/18 10:23 上午
 */
@Service
@Slf4j
public class PatientClinicService {
    private final PatientClinicRepository patientClinicRepository;

    private final PatientService patientService;

    private final CisScClinicFeignClient scClinicFeignClient;

    private final PatientClinicMapper patientClinicMapper;


    public PatientClinicService(PatientClinicRepository patientClinicRepository,
                                PatientService patientService,
                                CisScClinicFeignClient scClinicFeignClient,
                                PatientClinicMapper patientClinicMapper) {
        this.patientClinicRepository = patientClinicRepository;
        this.patientService = patientService;
        this.scClinicFeignClient = scClinicFeignClient;
        this.patientClinicMapper = patientClinicMapper;
    }

    @Transactional
    public PatientClinic getPatientClinic(String patientId, String clinicId) {
        if (StringUtils.isEmpty(patientId) || StringUtils.isEmpty(clinicId)) {
            return null;
        }
        return patientClinicRepository.findFirstByPatientIdAndClinicId(patientId, clinicId);
    }

    @Transactional
    public void save(PatientClinic patientClinic) {
        if (patientClinic == null) {
            return;
        }
        patientClinicRepository.save(patientClinic);
    }

    public List<PatientClinic> getPatientClinics(String chainId, List<String> patientIds) {
        if (CollectionUtils.isEmpty(patientIds)) {
            return new ArrayList<>();
        }
        return patientClinicRepository.findAllByPatientIdInAndChainId(patientIds, chainId);
    }

    @Transactional(rollbackFor = Exception.class)
    public PatientClinic createPatientClinicByCreatePatientReq(String patientId,
                                                               String chainId,
                                                               String clinicId,
                                                               String operatorId,
                                                               CreatePatientReq req) {
        if (StringUtils.isEmpty(patientId) || StringUtils.isEmpty(clinicId)) {
            return null;
        }

        PatientClinicReq patientClinicReq = new PatientClinicReq();
        patientClinicReq.setConsultantId(req.getConsultantId());
        patientClinicReq.setDutyTherapistId(req.getDutyTherapistId());
        patientClinicReq.setPrimaryTherapistId(req.getPrimaryTherapistId());
        return createOrUpdatePatientClinic(chainId, clinicId, patientId, patientClinicReq, false, operatorId);
    }

    @Transactional(rollbackFor = Exception.class)
    public PatientClinic createOrUpdatePatientClinic(String chainId, String clinicId, String patientId,
                                                     PatientClinicReq patientClinicReq,
                                                     boolean forceUpdateLastDate,
                                                     String operatorId) {
        if (StringUtils.isBlank(chainId) || StringUtils.isBlank(clinicId) || StringUtils.isBlank(patientId)
                || Objects.equals(patientId, SysCommon.UNKNOWN_ID)) {
            return null;
        }


        PatientClinic existPatientClinic = getPatientClinic(patientId, clinicId);


        PatientClinic patientClinic = new PatientClinic();
        operatorId = StringUtils.isNotBlank(operatorId) ? operatorId : SysCommon.UNKNOWN_ID;
        patientClinic.setPatientId(patientId);
        patientClinic.setClinicId(clinicId);
        patientClinic.setChainId(chainId);
        if (patientClinicReq != null) {
            // 只有之后的时间才更新
            if (patientClinicReq.getLastOutpatientDate() != null && (forceUpdateLastDate || existPatientClinic == null
                    || existPatientClinic.getLastOutpatientDate() == null
                    || existPatientClinic.getLastOutpatientDate().isBefore(patientClinicReq.getLastOutpatientDate()))) {
                patientClinic.setLastOutpatientDate(patientClinicReq.getLastOutpatientDate());
            }
            if (patientClinicReq.getConsultantId() != null) {
                patientClinic.setConsultantId(patientClinicReq.getConsultantId());
            }
            if (patientClinicReq.getDutyTherapistId() != null) {
                patientClinic.setDutyTherapistId(patientClinicReq.getDutyTherapistId());
            }

            if (patientClinicReq.getPrimaryTherapistId() != null) {
                patientClinic.setPrimaryTherapistId(patientClinicReq.getPrimaryTherapistId());
            }
            if (patientClinicReq.getArrearsFlag() != null) {
                patientClinic.setArrearsFlag(patientClinicReq.getArrearsFlag());
            }
            // 只有之后的时间才更新最近一次收费时间
            if (patientClinicReq.getLastChargeDate() != null && (forceUpdateLastDate || existPatientClinic == null
                    || existPatientClinic.getLastChargeDate() == null
                    || existPatientClinic.getLastChargeDate().isBefore(patientClinicReq.getLastChargeDate()))) {
                patientClinic.setLastChargeDate(patientClinicReq.getLastChargeDate());
            }
            if (existPatientClinic == null || existPatientClinic.getFirstChargeDate() == null) {
                patientClinic.setFirstChargeDate(patientClinicReq.getFirstChargeDate());
            }
            if (existPatientClinic == null || StringUtils.isBlank(existPatientClinic.getFirstOutpatientDoctorId())) {
                patientClinic.setFirstOutpatientDoctorId(patientClinicReq.getLastOutpatientDoctorId());
            }
            if (existPatientClinic == null || existPatientClinic.getFirstOutpatientDate() == null) {
                patientClinic.setFirstOutpatientDate(patientClinicReq.getLastOutpatientDate());
            }
            if (existPatientClinic == null || existPatientClinic.getFirstExaminationDate() == null) {
                patientClinic.setFirstExaminationDate(patientClinicReq.getFirstExaminationDate());
            }
            if (existPatientClinic == null || existPatientClinic.getFirstHisChargeSettleDate() == null) {
                patientClinic.setFirstHisChargeSettleDate(patientClinicReq.getFirstHisChargeSettleDate());
            }
        }
        FillUtils.fillCreatedBy(patientClinic, operatorId);
        patientClinic.setLastActiveDate(Instant.now());
        patientClinicMapper.insertOrUpdate(patientClinic);

        if (existPatientClinic != null) {
            PatientClinic returnPatientClinic = JsonUtils.readValue(JsonUtils.dump(existPatientClinic), PatientClinic.class);
            PatientClinicConvertor.INST.copyIfNonNull(patientClinic, returnPatientClinic);
            return returnPatientClinic;
        }
        return patientClinic;
    }

    @Transactional
    public List<PatientClinic> getPatientClinicList(List<String> patientIds, String clinicId) {
        if (CollectionUtils.isEmpty(patientIds) || StringUtils.isEmpty(clinicId)) {
            return new ArrayList<>();
        }

        return patientClinicRepository.findAllByPatientIdInAndClinicId(patientIds, clinicId);
    }

    /**
     * 获取患者欠费标识
     * 连锁：只有有一个门店欠费则返回欠费标识
     * 子店：只看当前门店有没有欠费
     */
    public int getPatientArrearsFlag(String patientId, String chainId, String clinicId, PatientClinic patientClinic) {
        if (Objects.equals(chainId, clinicId)) {
            return getPatientArrearsFlagForChain(patientId, chainId);
        }
        if (patientClinic != null && Objects.equals(patientClinic.getClinicId(), clinicId)) {
            return patientClinic.getArrearsFlag() != null ? patientClinic.getArrearsFlag() : 0;
        }
        return getPatientArrearsFlagForClinic(patientId, clinicId);
    }

    private int getPatientArrearsFlagForClinic(String patientId, String clinicId) {
        return getPatientArrearsFlagMapForClinic(Collections.singletonList(patientId), clinicId).getOrDefault(patientId, 0);
    }

    private Map<String, Integer> getPatientArrearsFlagMapForClinic(List<String> patientIds, String clinicId) {
        List<PatientClinic> patientClinics = patientClinicRepository.findAllByPatientIdInAndClinicId(patientIds, clinicId);
        if (CollectionUtils.isEmpty(patientClinics)) {
            return new HashMap<>();
        }
        return patientClinics.stream().collect(Collectors.toMap(PatientClinic::getPatientId, PatientClinic::getArrearsFlag, (a, b) -> Objects.equals(a, 1) ? a : b));
    }

    private int getPatientArrearsFlagForChain(String patientId, String chainId) {
        return getPatientArrearsFlagMapForChain(Collections.singletonList(patientId), chainId).getOrDefault(patientId, 0);
    }

    private Map<String, Integer> getPatientArrearsFlagMapForChain(List<String> patientIds, String chainId) {
        if (CollectionUtils.isEmpty(patientIds)) {
            return new HashMap<>();
        }

        List<PatientClinic> patientClinics = patientClinicRepository.findAllByPatientIdInAndChainId(patientIds, chainId);
        if (CollectionUtils.isEmpty(patientClinics)) {
            return new HashMap<>();
        }
        return patientClinics.stream().collect(Collectors.toMap(PatientClinic::getPatientId, PatientClinic::getArrearsFlag, (a, b) -> Objects.equals(a, 1) ? a : b));
    }

    /**
     * 查询患者欠费标识
     */
    @Transactional(readOnly = true)
    @UseReadOnlyDB
    public Map<String, Integer> getPatientArrearsFlagMap(List<String> patientIds, String chainId, String clinicId) {
        if (CollectionUtils.isEmpty(patientIds)) {
            return new HashMap<>();
        }

        if (Objects.equals(chainId, clinicId)) {
            return getPatientArrearsFlagMapForChain(patientIds, chainId);
        } else {
            return getPatientArrearsFlagMapForClinic(patientIds, clinicId);
        }
    }

    /**
     * 更新患者门店信息
     */
    @Transactional(rollbackFor = Exception.class)
    public PatientClinic updatePatientClinicByUpdatePatientBasicInfoReq(UpdatePatientBasicInfoReq req,
                                                                        String patientId,
                                                                        String chainId,
                                                                        String clinicId,
                                                                        String operatorId) {
        if (req == null || (req.getConsultantId() == null && req.getDutyTherapistId() == null && req.getPrimaryTherapistId() == null)) {
            return null;
        }

        PatientClinicReq patientClinicReq = new PatientClinicReq();
        patientClinicReq.setConsultantId(req.getConsultantId());
        patientClinicReq.setDutyTherapistId(req.getDutyTherapistId());
        patientClinicReq.setPrimaryTherapistId(req.getPrimaryTherapistId());
        return createOrUpdatePatientClinic(chainId, clinicId, patientId, patientClinicReq, false, operatorId);
    }

    /**
     * 处理医疗方案消息
     */
    @Transactional(rollbackFor = Exception.class)
    public void dealWithMedicalPlanMessage(MedicalPlanMessage message) {
        if (message == null || message.getMedicalPlan() == null) {
            return;
        }

        MedicalPlan medicalPlan = message.getMedicalPlan();
        updatePatientConsultant(medicalPlan.getPatientId(), medicalPlan.getConsultantId(), medicalPlan.getChainId(), medicalPlan.getClinicId(), message.getOperatorId());
    }

    /**
     * 修改患者咨询师
     */
    private void updatePatientConsultant(String patientId, String consultantId, String chainId, String clinicId, String operatorId) {
        if (StringUtils.isAnyBlank(patientId, chainId, clinicId, operatorId) || consultantId == null) {
            return;
        }

        PatientClinicReq patientClinicReq = new PatientClinicReq();
        patientClinicReq.setConsultantId(patientClinicReq.getConsultantId());
        createOrUpdatePatientClinic(chainId, clinicId, patientId, patientClinicReq, false, operatorId);
    }

    /**
     * 处理患者咨询师变更消息
     */
    @Transactional(rollbackFor = Exception.class)
    public void dealWithPatientConsultantChangeMessage(PatientConsultantChangeMessage message) {
        if (message == null) {
            return;
        }

        PatientClinicReq patientClinicReq = new PatientClinicReq();
        patientClinicReq.setConsultantId(message.getConsultantId());
        createOrUpdatePatientClinic(message.getChainId(), message.getClinicId(), message.getPatientId(), patientClinicReq, false, message.getOperatorId());
    }


    @Transactional(rollbackFor = Exception.class)
    public PatientClinicImportRsp importClinicPatient(PatientClinicImportReq req) throws CrmCustomException {

        String chainId = req.getChainId();
        String clinicId = req.getClinicId();
        String employeeId = SysCommon.UNKNOWN_ID;

        PatientClinicImportRsp rsp = new PatientClinicImportRsp();

        Patient patient = patientService.getPatientBySn(chainId, req.getSn());

        if (Objects.isNull(patient)) {
            rsp.setCode(404);
            rsp.setMessage("找不到对应档案号的人员信息:" + req.getSn());
            return rsp;
        }

        EmployeeView dutyTherapist = null;
        EmployeeView primaryTherapist = null;

        if (StringUtils.isNotEmpty(req.getDutyTherapistPhone())) {
            dutyTherapist = scClinicFeignClient.getChainEmployeeByMobile(chainId, req.getDutyTherapistPhone());
        }

        if (StringUtils.isNotEmpty(req.getPrimaryTherapistPhone())) {
            primaryTherapist = scClinicFeignClient.getChainEmployeeByMobile(chainId, req.getPrimaryTherapistPhone());
        }


        if (Objects.isNull(dutyTherapist) && Objects.isNull(primaryTherapist)) {
            rsp.setCode(404);
            rsp.setMessage("找不到对应电话号码员工信息");
            return rsp;
        }


        PatientClinicReq patientClinicReq = new PatientClinicReq();
        patientClinicReq.setPrimaryTherapistId(Optional.ofNullable(primaryTherapist).map(EmployeeView::getId).orElse(null));
        patientClinicReq.setDutyTherapistId(Optional.ofNullable(dutyTherapist).map(EmployeeView::getId).orElse(null));
        createOrUpdatePatientClinic(chainId, clinicId, patient.getId(), patientClinicReq, false, employeeId);

        rsp.setCode(200);
        return rsp;
    }

    @Transactional(rollbackFor = Exception.class)
    public void dealWithHisPatientOrderMessage(HisPatientOrderMessage message) {
        if (message == null || message.getPatientOrder() == null) {
            return;
        }

        log.info("Received HisPatientOrderMessage: patientId={}, clinicId={}, action={}, status={}",
                message.getPatientOrder().getPatientId(), message.getClinicId(),
                message.getAction(), message.getPatientOrder().getStatus());

        // 判断是否是住院结算完成
        boolean isDischargeSettled = isHospitalDischargeSettled(message);

        if (isDischargeSettled) {
            // 更新首次住院结算时间
            updateFirstHisChargeSettleDate(message.getChainId(), message.getClinicId(), message.getPatientOrder().getPatientId(), Instant.now(), message.getOperatorId());
        }
    }

    /**
     * 判断是否是住院结算完成
     *
     * @param message 住院单消息
     * @return 是否是住院结算完成
     */
    private boolean isHospitalDischargeSettled(HisPatientOrderMessage message) {
        // 判断 action 是否是出院结算
        boolean isDischargeSettleAction = message.getAction() == PatientOrderHospitalOperationAction.DISCHARGE_SETTLE;

        // 判断 status 是否是已出院已结算
        boolean isDischargeStatus = message.getPatientOrder() != null &&
                message.getPatientOrder().getStatus() == HisPatientOrder.Status.DISCHARGE;

        return isDischargeSettleAction || isDischargeStatus;
    }

    /**
     * 更新首次住院结算时间
     */
    private void updateFirstHisChargeSettleDate(String chainId, String clinicId, String patientId, Instant firstHisChargeSettleDate, String operatorId) {

        if (StringUtils.isAnyBlank(patientId, clinicId, chainId)) {
            log.warn("updateFirstHisChargeSettleDate failed, patientId or clinicId or chainId is blank");
            return;
        }

        log.info("Updating firstHisChargeSettleDate for patient: patientId={}, clinicId={}", patientId, clinicId);

        PatientClinicReq patientClinicReq = new PatientClinicReq();
        // TODO 按道理说应该要外部传入，但是现在没有这个字段，所以只能先用当前时间
        patientClinicReq.setFirstHisChargeSettleDate(Instant.now());
        createOrUpdatePatientClinic(chainId, clinicId, patientId, patientClinicReq, false, operatorId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void dealWithPeSheetMessage(PeSheetMessage message) {
        if (message == null || message.getPeSheet() == null) {
            return;
        }

        log.info("Received PeSheetMessage: patientId={}, clinicId={}, action={}, status={}",
                message.getPeSheet().getPatientId(), message.getClinicId(),
                message.getAction(), message.getPeSheet().getStatus());

        if (message.getPeSheet().getStatus() >= PeSheet.Status.WAIT_ALL_EXAM_REPORT && message.getPeSheet().getSheetSubmitted() != null) {
            // 更新首次检查时间
            updateFirstExaminationDate(message.getChainId(), message.getClinicId(), message.getPeSheet().getPatientId(), message.getPeSheet().getSheetSubmitted(), message.getOperatorId());
        }
    }

    private void updateFirstExaminationDate(String chainId, String clinicId, String patientId, Instant firstExaminationDate, String operatorId) {
        if (StringUtils.isAnyBlank(patientId, clinicId, chainId)) {
            log.warn("updateFirstExaminationDate failed, patientId or clinicId or chainId is blank");
            return;
        }

        log.info("Updating firstExaminationDate for patient: patientId={}, clinicId={}", patientId, clinicId);

        PatientClinicReq patientClinicReq = new PatientClinicReq();
        patientClinicReq.setFirstExaminationDate(firstExaminationDate);
        createOrUpdatePatientClinic(chainId, clinicId, patientId, patientClinicReq, false, operatorId);
    }

    @Transactional(rollbackFor = Exception.class)
    public void dealWithExaminationSheetMessage(ExaminationSheetMessage message) {
        if (message == null || message.getExaminationSheetView() == null) {
            return;
        }

        log.info("Received ExaminationSheetMessage: patientId={}, clinicId={}, action={}, status={}",
                message.getExaminationSheetView().getPatientId(), message.getClinicId(), message.getType(), message.getExaminationSheetView().getStatusV2());

        if (message.getExaminationSheetView().getStatusV2() == ExaminationStatusV2.EXAM_DONE
                && message.getExaminationSheetView().getBusinessType() != ExaminationBusinessType.PHYSICAL_EXAMINATION
                && message.getExaminationSheetView().getReportTime() != null) {
            // 更新首次检查时间（虽然可能会撤销然后再次审核，但是不管，只取第一次）
            updateFirstExaminationDate(message.getChainId(), message.getClinicId(), message.getExaminationSheetView().getPatientId(), message.getExaminationSheetView().getReportTime(), message.getOperatorId());
        }
    }
}
