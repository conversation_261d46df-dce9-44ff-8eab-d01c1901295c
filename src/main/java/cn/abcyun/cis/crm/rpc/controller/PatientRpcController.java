package cn.abcyun.cis.crm.rpc.controller;

import cn.abcyun.bis.rpc.sdk.cis.model.charge.RechargeRefundCallbackReq;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.ChainOrgan;
import cn.abcyun.bis.rpc.sdk.cis.model.clinic.Organ;
import cn.abcyun.bis.rpc.sdk.cis.model.patient.GetPatientBasicInfosByWxOpenIdsReq;
import cn.abcyun.bis.rpc.sdk.common.model.BasicCommonRsp;
import cn.abcyun.cis.commons.CisServiceResponse;
import cn.abcyun.cis.commons.CisServiceResponseBody;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.jwt.CisJWTUtils;
import cn.abcyun.cis.commons.model.CisPatientInfo;
import cn.abcyun.cis.commons.rpc.crm.*;
import cn.abcyun.cis.commons.rpc.wechat.WeChatPayConfigRsp;
import cn.abcyun.cis.core.aop.LogReqAndRsp;
import cn.abcyun.cis.crm.api.*;
import cn.abcyun.cis.crm.api.patient.member.PatientRelateMemberRsp;
import cn.abcyun.cis.crm.common.CommonUtil;
import cn.abcyun.cis.crm.common.SysCommon;
import cn.abcyun.cis.crm.convertor.PatientConvertor;
import cn.abcyun.cis.crm.entity.PatientBasicInfo;
import cn.abcyun.cis.crm.entity.PatientInfo;
import cn.abcyun.cis.crm.entity.PatientMemberInfo;
import cn.abcyun.cis.crm.exception.CrmCustomException;
import cn.abcyun.cis.crm.facade.PatientFacade;
import cn.abcyun.cis.crm.model.*;
import cn.abcyun.cis.crm.redis.PatientCacheManager;
import cn.abcyun.cis.crm.rpc.client.WeChatPayClient;
import cn.abcyun.cis.crm.rpc.entity.*;
import cn.abcyun.cis.crm.schedule.task.PatientRevisitRecordAutoExecuteTask;
import cn.abcyun.cis.crm.service.*;
import cn.abcyun.common.model.AbcListPage;
import cn.abcyun.common.model.AbcServiceResponse;
import cn.abcyun.common.model.AbcServiceResponseBody;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.Date;
import java.util.List;

@RestController
@RequestMapping("/rpc/crm/patients")
@Slf4j
@Api(value = "PatientRpcController", description = "患者rpc接口", produces = "application/json")
public class PatientRpcController {

    @Autowired
    private PatientService patientService;

    @Autowired
    private PatientMemberService patientMemberService;

    @Autowired
    private WeChatPayClient weChatPayClient;

    @Autowired
    private PatientSnGenerateService patientSnGenerateService;

    @Autowired
    private PatientFamilyService patientFamilyService;

    @Autowired
    private PatientFamilyDoctorService patientFamilyDoctorService;

    @Autowired
    private PatientTraceService patientTraceService;

    @Autowired
    private PatientRemindStatisticsService patientRemindStatisticsService;

    @Autowired
    private PatientRevisitService patientRevisitService;

    @Autowired
    private PatientCacheManager patientCacheManager;

    @Autowired
    private PatientOrganService patientOrganService;

    @Autowired
    private McPatientService mcPatientService;

    @Autowired
    private ClinicService clinicService;

    @Autowired
    private PatientClinicService patientClinicService;
    @Autowired
    private PatientFacade patientFacade;
    @Autowired
    private PatientMemberTypeService patientMemberTypeService;

    @Autowired
    private PatientRevisitRecordAutoExecuteTask patientRevisitRecordAutoExecuteTask;

    @GetMapping(value = "/basic/{patientId}")
    public AbcServiceResponse<InnerPatientInfoView> getPatientBasicInfo(@RequestParam(value = "hisType", required = false) Integer hisType,
                                                                        @RequestParam(value = "chainId", required = false) String chainId,
                                                                        @RequestParam(value = "needMember", required = false, defaultValue = "false") boolean needMember,
                                                                        @RequestParam(value = "wx", required = false, defaultValue = "false") boolean needWxInfo,
                                                                        @RequestParam(value = "chronicArchives", required = false, defaultValue = "false") boolean needChronicArchives,
                                                                        @RequestParam(value = "childCare", required = false, defaultValue = "false") boolean needChildCareInfo,
                                                                        @RequestParam(value = "needPointsInfo", required = false, defaultValue = "false") boolean needPointsInfo,
                                                                        @RequestParam(value = "needPatientTag", required = false, defaultValue = "false") boolean needPatientTag,
                                                                        @PathVariable("patientId") String patientId) throws CrmCustomException {
        InnerPatientInfoView patientInfoView = patientService.rpcGetPatientInfo(hisType, chainId, patientId, needChildCareInfo, needMember, needChronicArchives, needPointsInfo, needWxInfo, needPatientTag);
        return new AbcServiceResponse<>(patientInfoView);
    }

    @GetMapping(value = "/{patientId}")
    public AbcServiceResponse<PatientInfoDetailVO> getPatientInfoDetail(@PathVariable("patientId") String patientId,
                                                                        @RequestParam(value = "chainId") String chainId,
                                                                        @RequestParam(value = "clinicId", required = false) String clinicId,
                                                                        @RequestParam(value = "needWxInfo", required = false, defaultValue = "false") boolean needWxInfo,
                                                                        @RequestParam(value = "needStatInfo", required = false, defaultValue = "false") boolean needStatInfo,
                                                                        @RequestParam(value = "hisType", required = false) Integer hisType) throws CrmCustomException {

        PatientInfoDetailVO patientInfoVO = patientService.getPatientInfoVO(patientId, chainId, clinicId, needWxInfo, needStatInfo, hisType);
        return new AbcServiceResponse<>(patientInfoVO);
    }

    @PostMapping(value = "/basic")
    public AbcServiceResponse<PatientInfoListRsp> getPatientBasicInfos(@RequestBody cn.abcyun.bis.rpc.sdk.cis.model.patient.GetPatientBasicInfosReq req) {
        List<PatientInfo> patientInfoList = patientService.getPatientBasicInfoList(req.getChainId(), req.getClinicId(), req.getPatientIds(), req.isNeedWxInfo(),
                req.isNeedPointsInfo(), req.isNeedTag(), req.isNeedPublicHealthInfo(), req.isNeedPatientClinicInfo());
        PatientInfoListRsp rsp = new PatientInfoListRsp();
        rsp.setPatientInfos(patientInfoList);
        return new AbcServiceResponse<>(rsp);
    }

    @PutMapping(value = "/{patientId}/pastHistory")
    @LogReqAndRsp
    public AbcServiceResponse<HttpStatus> updatePatientPastHistory(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                       @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                       @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) Integer clinicType,
                                                       @RequestHeader(value = CisJWTUtils.CIS_HEADER_EMPLOYEE_ID) String employeeId,
                                                       @PathVariable("patientId") String patientId, @RequestBody @Valid updatePatientPastHistoryReq req) {
        patientFacade.updatePatientPastHistory(patientId, req.getPastHistory(), chainId, clinicId, employeeId);
        return new AbcServiceResponse<>(HttpStatus.OK);
    }

    @PostMapping(value = "")
    @LogReqAndRsp(longTimeLog = true)
    public AbcServiceResponse<PatientInfo> createOrUpdate(@RequestBody @Valid UpdatePatientInfoReq updatePatientInfoReq) throws CrmCustomException {
        Organ organ = clinicService.getOrgan(updatePatientInfoReq.getChainId());
        Integer hisType = organ.getHisType();
        PatientBasicInfo patientBasicInfo = updatePatientInfoReq.getPatient();
        String chainIdReq = updatePatientInfoReq.getChainId();
        String clinicIdReq = updatePatientInfoReq.getClinicId();
        String operatorIdReq = updatePatientInfoReq.getOperatorId();
        CommonUtil.checkBirthdayAndAge(patientBasicInfo.getBirthday(), patientBasicInfo.getAge(), hisType != Organ.HisType.CIS_HIS_TYPE_PHARMACY);
        PatientInfo patientInfo = patientFacade.createOrUpdatePatient(patientBasicInfo, chainIdReq, clinicIdReq, operatorIdReq, updatePatientInfoReq.getOperatorType(), hisType);
        return new AbcServiceResponse<>(patientInfo);
    }

    @GetMapping("/{patientId}/member")
    public CisServiceResponse<PatientMemberInfo> getPatientMember(@RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_ID) String clinicId,
                                                                  @RequestHeader(value = CisJWTUtils.CIS_HEADER_CHAIN_ID, required = false) String chainId,
                                                                  @RequestHeader(value = CisJWTUtils.CIS_HEADER_CLINIC_TYPE) Integer clinicType,
                                                                  @PathVariable @NotEmpty String patientId) {

        PatientMemberInfo patientMemberInfo = patientMemberService.getPatientMemberInfoById(chainId, patientId);
        if (patientMemberInfo == null) {
            throw new NotFoundException();
        }
        return new CisServiceResponse<>(patientMemberInfo);
    }

    /**
     * 查询患者关联的会员信息
     */
    @GetMapping("/{patientId}/relate-member")
    public AbcServiceResponse<PatientRelateMemberRsp> getPatientRelateMember(@PathVariable String patientId, @RequestParam String chainId) {
        PatientRelateMemberRsp rsp = patientMemberService.getPatientRelateMemberInfo(chainId, patientId);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 根据患者id查询患者会员信息
     *
     * @param patientId        患者id
     * @param needDiscountInfo 是否需要折扣信息 null/1:需要;0:不需要
     * @return 患者会员信息
     */
    @GetMapping("/{patientId}/member/info")
    public AbcServiceResponse<PatientMemberInfo> getPatientMemberByPatientId(@PathVariable String patientId,
                                                                             @RequestParam String chainId,
                                                                             @RequestParam(required = false) Integer needDiscountInfo) {
        PatientMemberInfo patientMemberInfo = patientMemberService.getPatientMemberInfoById(chainId, patientId, needDiscountInfo);
        return new AbcServiceResponse<>(patientMemberInfo);
    }

    @GetMapping("/member/recharge/status")
    public AbcServiceResponse<PatientMemberReChargeStatusRsp> getMemberRechargeSwitchStatus(@RequestParam(required = false) String clinicId, @RequestParam String chainId) {
        if (StringUtils.isEmpty(clinicId)) {
            clinicId = chainId;
        }
        PatientMemberReChargeStatusRsp memberChargeStatusRsp = new PatientMemberReChargeStatusRsp();
        memberChargeStatusRsp.setChainId(chainId);
        memberChargeStatusRsp.setClinicId(clinicId);
        memberChargeStatusRsp.setStatus(PatientMemberReChargeStatusRsp.MemberRechargeStatus.UNAVAILABLE);

        CisServiceResponseBody<WeChatPayConfigRsp> weChatPayConfigRspBody = weChatPayClient.getWeChatPayConfig(chainId, clinicId);
        if (weChatPayConfigRspBody != null) {
            WeChatPayConfigRsp weChatPayConfigRsp = weChatPayConfigRspBody.getData();
            if (weChatPayConfigRsp != null
                    && weChatPayConfigRsp.getWeChatPaySwitch().equals(WeChatPayConfigRsp.WeChatPaySwitch.AVAILABLE)
                    && weChatPayConfigRsp.getJsapiPayStatus().equals(WeChatPayConfigRsp.JsapiPayStatus.AVAILABLE)) {
                memberChargeStatusRsp.setStatus(PatientMemberReChargeStatusRsp.MemberRechargeStatus.AVAILABLE);
            }
        }
        return new AbcServiceResponse<>(memberChargeStatusRsp);
    }

    /**
     * 会员卡微信充值生成订单
     *
     * @return
     * @throws NotFoundException
     */
    @PostMapping(value = "/member/balance/order")
    @LogReqAndRsp
    public AbcServiceResponse<PatientMemberRechargeOrderRsp> createPatientMemberRechargeOrder(@RequestBody @Valid PatientMemberRechargeOrderReq req) {

        if (StringUtils.isEmpty(req.getClinicId())) {
            req.setClinicId(req.getChainId());
        }

        //patient member charge
        req.setOrderSource(PatientMemberOrder.OrderSource.WE_CLINIC);
        PatientMemberRechargeOrderRsp memberRechargeOrderRsp = patientMemberService.createPatientMemberRechargeOrder(req);
        return new AbcServiceResponse<>(memberRechargeOrderRsp);
    }

    /**
     * 会员充值异步回调
     */
    @PutMapping(value = "/member/balance/order/{chargeSheetId}")
    @LogReqAndRsp
    public AbcServiceResponse<PatientMemberRechargeRsp> updatePatientMemberRechargeOrderByChargeSheetId(@PathVariable String chargeSheetId,
                                                                                                        @RequestBody PatientMemberRechargeReq memberRechargeReq) {
        PatientMemberRechargeRsp memberRechargeRsp = patientMemberService.updatePatientMemberRechargeOrderByChargeSheetId(chargeSheetId, memberRechargeReq);
        return new AbcServiceResponse<>(memberRechargeRsp);
    }

    @PostMapping(value = "/import")
    @LogReqAndRsp
    public AbcServiceResponse<PatientImportRsp> importPatient(@RequestBody PatientImportReq patientImportReq) throws CrmCustomException {
        PatientImportRsp patientImportRsp = patientFacade.importPatient(patientImportReq);
        return new AbcServiceResponse<>(patientImportRsp);
    }

    @PostMapping(value = "/import-clinic-info")
    @LogReqAndRsp
    public AbcServiceResponse<PatientClinicImportRsp> importPatient(@RequestBody @Valid PatientClinicImportReq req) throws CrmCustomException {
        PatientClinicImportRsp rsp = patientClinicService.importClinicPatient(req);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 根据会员类型id集合查询会员类型信息集合
     *
     * @param memberTypesReq 会员类型集合请求参数
     * @return 会员类型信息集合响应
     */
    @PostMapping(value = "/member/typeNames")
    public CisServiceResponse<PatientMemberTypesRsp> listMemberTypeNameByTypeIds(@RequestBody cn.abcyun.bis.rpc.sdk.cis.model.patient.PatientMemberTypesReq memberTypesReq) {
        PatientMemberTypesRsp memberTypesRsp = patientMemberTypeService.listMemberTypeNameByTypeIds(memberTypesReq);
        return new CisServiceResponse<>(memberTypesRsp);
    }

    /**
     * 会员卡消费
     */
    @PostMapping(value = "/member/pay")
    @LogReqAndRsp
    public AbcServiceResponse<PatientMemberCardPayRsp> rpcPatientMemberPay(@RequestBody @Valid PatientMemberCardPayReq req) throws CrmCustomException {
        req.setPaySource(PatientMemberBill.PaySource.PC);
        PatientMemberCardPayRsp rsp = patientMemberService.rpcPatientMemberPay(req.getMemberId(), req);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 会员卡退款
     */
    @PostMapping(value = "/member/refund")
    @LogReqAndRsp
    public AbcServiceResponse<PatientMemberCardRefundRsp> rpcPatientMemberRefund(@RequestBody @Valid PatientMemberCardRefundReq req) {
        return new AbcServiceResponse<>(patientMemberService.rpcPatientMemberRefund(req));
    }

    @PostMapping("/{patientId}/member/password")
    public AbcServiceResponseBody<GeneralRsp> updatePatientMemberPassword(@PathVariable String patientId,
                                                                          @RequestBody @Valid UpdatePatientMemberPasswordReq updatePatientMemberPasswordReq) throws CrmCustomException {
        GeneralRsp generalRsp = patientMemberService.updatePatientMemberInfo(updatePatientMemberPasswordReq, null, updatePatientMemberPasswordReq.getChainId(), patientId);
        return new AbcServiceResponseBody<>(generalRsp);
    }

    @PostMapping("/{patientId}/member/password/verification")
    public AbcServiceResponseBody<GeneralRsp> verifyPatientMemberPassword(@PathVariable String patientId,
                                                                          @RequestBody UpdatePatientMemberPasswordReq memberPasswordVerifyReq) throws CrmCustomException {

        //修改基本信息 or 修改会员信息 or 添加会员信息
        GeneralRsp generalRsp = patientMemberService.verifyPatientMemberPassword(memberPasswordVerifyReq.getPassword(), memberPasswordVerifyReq.getChainId(), patientId);
        return new AbcServiceResponseBody<>(generalRsp);
    }

    @GetMapping(value = "/basic/query")
    public AbcServiceResponse<CisPatientInfo> queryPatientByParams(@RequestParam(value = "chainId") String chainId,
                                                                   @RequestParam(value = "wx", required = false, defaultValue = "false") boolean needWxInfo,
                                                                   @RequestParam(value = "mobile") String mobile) throws CrmCustomException {

        PatientInfo patientInfo = patientService.queryPatientByParams(chainId, mobile, needWxInfo);
        if (patientInfo != null) {
            CisPatientInfo rsp = new CisPatientInfo();
            BeanUtils.copyProperties(patientInfo, rsp);
            return new AbcServiceResponse<>(rsp);
        }
        return null;
    }

    @GetMapping(value = "/batch/basic/query")
    public CisServiceResponse<PatientInfoListRsp> queryMultiPatientsByParams(@RequestParam(value = "chainId") String chainId,
                                                                             @RequestParam(value = "mobile", required = false) String mobile,
                                                                             @RequestParam(value = "name", required = false) String name) throws CrmCustomException {

        List<PatientInfo> patientInfos = patientService.queryMultiPatientsByParams(chainId, mobile, name);
        PatientInfoListRsp rsp = new PatientInfoListRsp();
        rsp.setPatientInfos(patientInfos);
        return new CisServiceResponse<>(rsp);
    }

    /**
     * 搜索患者信息
     */
    @GetMapping(value = "/batch/basic/search")
    public AbcServiceResponse<PatientBasicSearchList> searchPatientBasicInfo(@RequestParam(value = "chainId") String chainId,
                                                                             @RequestParam(value = "keyword", required = false) String keyword,
                                                                             @RequestParam(value = "offset", defaultValue = "0") Integer offset,
                                                                             @RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        PatientBasicSearchList patientBasicSearchList = patientService.searchPatientBasicInfo(chainId, keyword, offset, limit);
        return new AbcServiceResponse<>(patientBasicSearchList);
    }

    @PutMapping(value = "/{patientId}/child-care/archives")
    public CisServiceResponse<PatientInfo> updatePatientChildCareArchives(@PathVariable(value = "patientId") String patientId,
                                                                          @RequestParam(value = "chainId") String chainId) throws CrmCustomException {

        PatientInfo rsp = patientService.updatePatientChildCareArchives(patientId, chainId);
        return new CisServiceResponse<>(rsp);
    }

    @PostMapping(value = "/shebao/info")
    @LogReqAndRsp
    public CisServiceResponse<PatientInfo> insertOrUpdatePatientShebaoInfo(@RequestBody UpdatePatientShebaoCardInfoReq updatePatientShebaoCardInfoReq) throws CrmCustomException, NotFoundException, ParamRequiredException {

        PatientInfo patientInfo = patientFacade.insertOrUpdatePatientShebaoInfo(updatePatientShebaoCardInfoReq);
        return new CisServiceResponse<>(patientInfo);
    }

    @PutMapping("/{patientId}/child-care/info")
    @LogReqAndRsp
    public CisServiceResponse<PatientChildCareInfoRsp> updatePatientChildCareInfo(@PathVariable String patientId,
                                                                                  @RequestBody UpdatePatientChildCareInfoReq req) throws CrmCustomException, NotFoundException {
        PatientChildCareInfoRsp rsp = patientService.updatePatientChildCareInfo(req, patientId, req.getChainId(), req.getOperatorId());
        return new CisServiceResponse<>(rsp);
    }

    @PostMapping(value = "/basic/by-wx-open-id")
    public CisServiceResponse<PatientInfoListRsp> getPatientBasicInfosByWxOpenIds(@RequestBody GetPatientBasicInfosByWxOpenIdsReq req) {

        List<PatientInfo> patientInfos = patientService.getPatientBasicInfosByWxOpenIds(req.getChainId(), req.getWxOpenIds());
        PatientInfoListRsp patientInfoListRsp = new PatientInfoListRsp();
        patientInfoListRsp.setPatientInfos(patientInfos);
        return new CisServiceResponse<>(patientInfoListRsp);
    }

    /**
     * 代码重复-废弃
     */
    @PostMapping(value = "/basic/by-patient-id")
    @Deprecated
    public AbcServiceResponse<PatientInfoListRsp> getPatientBasicInfosByPatientIds(@RequestBody GetPatientBasicInfosByPatientIdsReq req) {

        List<PatientInfo> patientInfos = patientService.getPatientBasicInfosByPatientIds(req.getChainId(), req.getPatientIds());
        PatientInfoListRsp patientInfoListRsp = new PatientInfoListRsp();
        patientInfoListRsp.setPatientInfos(patientInfos);
        return new AbcServiceResponse<>(patientInfoListRsp);
    }

    @PostMapping(value = "/sn/generator/task")
    public AbcServiceResponse<GeneralRsp> generateSnByChainId(@RequestBody GenerateSnByChainIdReq req) {
        long now = System.currentTimeMillis();
        if (req.isSync()) {
            patientSnGenerateService.flushPatientSnByChainId(req.getChainId(), req.getSnNum());

        } else {
            patientSnGenerateService.flushPatientSnByChainIdAsync(req.getChainId(), req.getSnNum());
        }
        GeneralRsp rsp = new GeneralRsp();
        rsp.setCode(200);
        rsp.setMessage("create sn generator task success! cost time " + (System.currentTimeMillis() - now) / 1000 + "s");
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 申领电子健康卡
     *
     * @param req 申领电子健康卡body
     * @return rsp
     */
    @PostMapping(value = "/health-card")
    @ApiOperation(value = "申领电子健康卡", produces = "application/json")
    @LogReqAndRsp
    public AbcServiceResponse<CreatePatientHealthCardRsp> createPatientHealthCard(@RequestBody @Valid CreatePatientHealthCardReq req) {
        CreatePatientHealthCardRsp rsp = patientFacade.createPatientHealthCard(req);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 更新电子健康卡资料
     */
    @PutMapping(value = "/{patientId}/health-card/{healthCardId}")
    @ApiOperation(value = "更新电子健康卡", produces = "application/json")
    @LogReqAndRsp
    public AbcServiceResponse<CreatePatientHealthCardRsp> updatePatientHealthCard(@PathVariable String patientId,
                                                                                  @PathVariable String healthCardId,
                                                                                  @RequestBody @Valid UpdatePatientHealthCardReq req) {
        CreatePatientHealthCardRsp rsp = patientService.updatePatientHealthCard(patientId, healthCardId, req);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 获取电子健康卡信息
     */
    @GetMapping(value = "/{patientId}/health-card/{healthCardId}")
    @ApiOperation(value = "获取电子健康卡", produces = "application/json")
    public AbcServiceResponse<PatientHealthCardVO> getPatientHealthCard(@PathVariable String patientId,
                                                                        @PathVariable String healthCardId,
                                                                        @RequestParam String chainId) {
        PatientHealthCardVO rsp = patientService.getPatientHealthCard(patientId, healthCardId, chainId);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 解除健康卡绑定
     */
    @PutMapping(value = "/{patientId}/health-card/{healthCardId}/unbind")
    @ApiOperation(value = "解除健康卡绑定", produces = "application/json")
    @LogReqAndRsp
    public AbcServiceResponse<GeneralRsp> unbindPatientHealthCard(@PathVariable String patientId,
                                                                  @PathVariable String healthCardId,
                                                                  @RequestParam String chainId,
                                                                  @RequestParam(required = false) String operatorId) {
        GeneralRsp rsp = patientService.unbindPatientHealthCard(patientId, healthCardId, chainId, operatorId);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 微诊所查询就诊人列表信息
     */
    @GetMapping(value = "/we-clinic/list")
    @ApiOperation(value = "微诊所查询就诊人列表信息", produces = "application/json")
    public AbcServiceResponse<PatientWeClinicListVO> getPatientWeClinicList(@RequestParam String wxOpenId, @RequestParam String chainId) {
        PatientWeClinicListVO rsp = patientService.getPatientWeClinicList(wxOpenId, chainId);
        return new AbcServiceResponse<>(rsp);
    }


    /**
     * 查询家庭成员集合列表-微诊所
     */
    @GetMapping(value = "/family/{chainId}/{patientId}")
    @ApiOperation(value = "查询家庭成员集合列表", produces = "application/json", response = PatientFamilyListView.class)
    @LogReqAndRsp
    public AbcServiceResponse<PatientFamilyListView> getPatientFamilyList(@PathVariable("patientId") String patientId, @PathVariable("chainId") String chainId) {
        PatientFamilyListView patientFamilyListView = patientFamilyService.getPatientFamilyList(patientId, chainId, true);
        return new AbcServiceResponse<>(patientFamilyListView);
    }

    /**
     * 微信端创建患者,如果根据手机和姓名匹配存在该患者则直接返回该患者信息
     */
    @PostMapping(value = "/create-or-find")
    @LogReqAndRsp
    @ApiOperation(value = "微信端创建患者", produces = "application/json", response = PatientBasicInfoForWeClinic.class)
    public AbcServiceResponse<PatientBasicInfoForWeClinic> createOrFindPatient(@RequestBody @Valid CreatePatientByWeClinicReq createPatientByWeClinicReq) {
        Organ organ = clinicService.getOrgan(createPatientByWeClinicReq.getChainId());
        return new AbcServiceResponse<>(patientFacade.createOrFindPatient(createPatientByWeClinicReq, organ.getHisType()));
    }

    /**
     * 微信端修改患者
     */
    @PutMapping(value = "/basic/{patientId}")
    @LogReqAndRsp
    @ApiOperation(value = "微信端修改患者", produces = "application/json", response = PatientBasicInfoForWeClinic.class)
    public AbcServiceResponse<PatientBasicInfoForWeClinic> updatePatientInfoByWx(@PathVariable(value = "patientId") String patientId,
                                                                                 @RequestBody @Valid CreatePatientByWeClinicReq createPatientByWeClinicReq) {
        return new AbcServiceResponse<>(patientFacade.updatePatientInfoByWx(createPatientByWeClinicReq, patientId));
    }

    /**
     * 添加家庭成员关联接口
     */
    @PostMapping(value = "/family/relevance")
    @LogReqAndRsp
    @ApiOperation(value = "添加家庭成员关联接口", produces = "application/json", response = PatientFamily.class)
    public AbcServiceResponse<PatientFamilyView> createPatientAndPatientFamily(@RequestBody @Valid PatientFamilyRelevanceReq patientFamilyRelevanceReq) {
        return new AbcServiceResponse<>(patientFacade.createPatientAndPatientFamily(patientFamilyRelevanceReq));
    }

    /**
     * 删除家庭成员关联接口
     */
    @DeleteMapping(value = "/family/relevance")
    @LogReqAndRsp
    @ApiOperation(value = "删除家庭成员关联接口", produces = "application/json")
    public AbcServiceResponse<PatientFamilyVo> deletePatientFamily(@RequestBody @Valid PatientFamilyDeleteReq patientFamilyDeleteReq) {
        return new AbcServiceResponse<>(patientFacade.deletePatientFamily(patientFamilyDeleteReq.getPatientId(), patientFamilyDeleteReq.getChainId(), null, patientFamilyDeleteReq.getParentId(), SysCommon.UNKNOWN_ID, patientFamilyDeleteReq.getOperatorType()));
    }

    /**
     * 修改家庭成员接口
     */
    @PutMapping(value = "/family/relevance")
    @LogReqAndRsp
    @ApiOperation(value = "修改家庭成员接口", produces = "application/json")
    public AbcServiceResponse<PatientFamilyView> updatePatientFamily(@RequestBody @Valid PatientFamilyRelevanceReq patientFamilyRelevanceReq) {
        return new AbcServiceResponse<>(patientFacade.updatePatientAndPatientFamily(patientFamilyRelevanceReq));
    }

    @GetMapping(value = "/members/{patientId}/bills")
    @LogReqAndRsp
    @ApiOperation(value = "查询会员流水记录", produces = "application/json")
    public AbcServiceResponse<AbcListPage<PatientMemberBillView>> getMemberBillsPage(@PathVariable(value = "patientId") String patientId,
                                                                                     @RequestParam(value = "limit", defaultValue = "20", required = false) Integer limit,
                                                                                     @RequestParam(value = "offset", defaultValue = "0", required = false) Integer offset,
                                                                                     @RequestParam(value = "chainId") String chainId) {

        return new AbcServiceResponse<>(patientService.getMemberBillsPage(patientId, limit, offset, chainId, chainId));
    }


    @GetMapping(value = "/memberShareInfo/{sharedPatientId}")
    public AbcServiceResponse<PatientMemberInfo> getPatientMemberSharedByBasicInfo(@RequestParam("chainId") String chainId,
                                                                                   @PathVariable("sharedPatientId") String sharedPatientId) {
        PatientMemberInfo familyMemberInfo = patientMemberService.getFamilyMemberInfo(chainId, sharedPatientId);
        return new AbcServiceResponse<>(familyMemberInfo);
    }

    /**
     * 校验当前患者（或家长）是否绑定微信
     */
    @PostMapping(value = "/family/wx")
    @LogReqAndRsp
    public AbcServiceResponse<PatientFamilyBindWxListRsp> getPatientFamilyBindWxInfo(@RequestBody BatchFamilyPatientReq req) {
        return new AbcServiceResponse<>(patientFamilyService.getPatientFamilyBindWxInfo(req));
    }


    /**
     * 查看签约详情
     */
    @GetMapping(value = "/find/sign-info/{chargeSheetId}")
    @ApiOperation(value = "查看签约详情", produces = "application/json")
    public AbcServiceResponse<PatientFamilyDoctorRsp> findSignInfo(@PathVariable(value = "chargeSheetId") String chargeSheetId) {
        return new AbcServiceResponse<>(patientFamilyDoctorService.findSignInfo(chargeSheetId));
    }

    /**
     * 根据患者id与连锁id查询家庭医生签约信息(查询出list然后进行处理)
     */
    @GetMapping(value = "/find-doctor/recently")
    @ApiOperation(value = "根据id查询家庭医生签约信息", produces = "application/json")
    public AbcServiceResponse<PatientFamilyDoctorRsp> findFamilyDoctorByPatientIdAndChainId(@RequestParam(value = "patientId") String patientId,
                                                                                            @RequestParam(value = "chainId") String chainId) {
        return new AbcServiceResponse<>(patientFamilyDoctorService.findRpcFamilyDoctorByPatientIdAndChainId(patientId, chainId, false));
    }


    /**
     * todo 在二期版本中需要开启门店配置，目前是连锁配置开关
     * 查询开启了家庭医生的门店
     */
    @GetMapping(value = "/find-doctor/open/clinic")
    @ApiOperation(value = "查询当前连锁开通了家庭医生的诊所", produces = "application/json")
    public AbcServiceResponse<ChainOrgan> findOpenFamilyDoctorClinic(@RequestParam(value = "chainId") String chainId) {
        return new AbcServiceResponse<>(patientFamilyDoctorService.findOpenFamilyDoctorClinic(chainId));
    }

    /**
     * 批量添加家庭成员关联接口
     */
    @PostMapping(value = "/family/relevance/insert/{patientId}")
    @LogReqAndRsp
    @ApiOperation(value = "添加家庭成员关联接口", produces = "application/json", response = PatientFamilyListVO.class)
    @Deprecated
    public AbcServiceResponse<PatientFamilyListVO> updatePatientFamilyMembers(@RequestBody @Valid CreatePatientFamilyRpcReq req,
                                                                              @PathVariable("patientId") String patientId) {

        return new AbcServiceResponse<>(patientFamilyService.updatePatientFamilyMembers(req.getPatientIds(), patientId, req.getChainId(), req.getOperatorId()));
    }

    /**
     * 轻量级查询患者信息接口
     */
    @PostMapping(value = {"/mini/basic/list"})
    @ApiOperation(value = "轻量级查询患者信息接口", produces = "application/json", response = ListMiniPatientRsp.class)
    public AbcServiceResponse<ListMiniPatientRsp> listMiniPatientByIds(@RequestBody @Valid ListMiniPatientIdsReq req) {
        return new AbcServiceResponse<>(patientService.findMiniBasicPatient(req));
    }

    /**
     * 根据账单id
     *
     * @param id      账单id
     * @param chainId
     * @return
     */
    @GetMapping(value = "/members/bills/{id}")
    @LogReqAndRsp
    @ApiOperation(value = "查询会员流水记录", produces = "application/json")
    public AbcServiceResponse<PatientMemberBillDetailVO> getMemberBills(@PathVariable(value = "id") String id,
                                                                        @RequestParam(value = "chainId") String chainId) {

        return new AbcServiceResponse<>(patientMemberService.getMemberBillsById(id, chainId));
    }


    /**
     * 根据创建时间获取患者列表
     *
     * @param chainId
     * @param clinicId
     * @param beginDate
     * @param endDate
     * @param offset
     * @param limit
     * @return
     * @throws CrmCustomException
     */
    @GetMapping(value = "/list/by-created-time")
    public AbcServiceResponse<AbcListPage<PatientBasicInfoView>> queryPatientListByCreatedTime(@RequestParam(value = "chainId") String chainId,
                                                                                               @RequestParam(value = "clinicId", required = false) String clinicId,
                                                                                               @RequestParam(value = "beginDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date beginDate,
                                                                                               @RequestParam(value = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
                                                                                               @RequestParam(value = "offset", required = false, defaultValue = "0") Integer offset,
                                                                                               @RequestParam(value = "limit", required = false, defaultValue = "20") Integer limit) throws CrmCustomException {
        limit = limit > 100 ? 100 : limit;
        AbcListPage<PatientBasicInfoView> rsp = patientService.queryPatientListByCreatedTime(chainId, clinicId, beginDate, endDate, offset, limit);
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping(value = "/list/scrm")
    public AbcServiceResponse<AbcListPage<ScrmPatientInfoView>> queryScrmPatientList(@RequestParam(value = "chainId") String chainId,
                                                                                     @RequestParam(value = "clinicId", required = false) String clinicId,
                                                                                     @RequestParam(value = "beginDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date beginDate,
                                                                                     @RequestParam(value = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate,
                                                                                     @RequestParam(value = "offset", required = false, defaultValue = "0") Integer offset,
                                                                                     @RequestParam(value = "limit", required = false, defaultValue = "20") Integer limit,
                                                                                     @RequestParam(value = "orderByLastOutpatientDate", required = false, defaultValue = "0") Integer orderByLastOutpatientDate) throws CrmCustomException {
        limit = limit > 100 ? 100 : limit;
        AbcListPage<ScrmPatientInfoView> rsp = patientService.queryScrmPatientList(chainId, clinicId, beginDate, endDate, offset, limit, orderByLastOutpatientDate);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 批量查询患者会员信息
     */
    @PostMapping(value = "/members/list")
    @ApiOperation(value = "查询会员流水记录", produces = "application/json")
    public AbcServiceResponse<AbcListPage<PatientMemberInfoVO>> getPatientMemberInfosByPatientIds(@RequestBody GetPatientMemberInfosByPatientIdsReq req) {

        return new AbcServiceResponse<>(patientMemberService.getPatientMemberInfosByPatientIds(req.getPatientIds(), req.getChainId()));
    }

    @GetMapping(value = "/block/list")
    @ApiOperation(value = "查询微诊所黑名单成员", produces = "application/json")
    public AbcServiceResponse<AbcListPage<BlockPatientInfoView>> getBlockPatients(@RequestParam(value = "chainId") String chainId,
                                                                                  @RequestParam(value = "offset", required = false, defaultValue = "0") Integer offset,
                                                                                  @RequestParam(value = "limit", required = false, defaultValue = "20") Integer limit,
                                                                                  @RequestParam(value = "keyword", required = false, defaultValue = "") String keyword) {
        limit = limit > 100 ? 100 : limit;
        return new AbcServiceResponse<>(patientService.getBlockPatients(chainId, offset, limit, keyword));
    }


    /**
     * 修改患者黑名单状态
     */
    @PostMapping(value = "/block-flag")
    @LogReqAndRsp
    @ApiOperation(value = "更新会员黑名单标识", produces = "application/json")
    public AbcServiceResponse<UpdatePatientBlockRsp> updatePatientBlockFlags(@RequestBody @Valid UpdatePatientBlockFlagReq req) {
        return new AbcServiceResponse<>(patientService.updatePatientBlockFlags(req));
    }


    /**
     * 获取患者轨迹
     */
    @PostMapping("/traces")
    public AbcServiceResponse<AbcListPage<GetPatientTraceListRsp>> getPatientTraceListByPatientIds(@RequestBody GetPatientTraceListByPatientIdsReq req) {
        return new AbcServiceResponse<>(patientTraceService.getPatientTraceListByPatientIds(req));
    }

    @PostMapping(value = "/mark-patient-scrm-flag")
    @LogReqAndRsp
    @ApiOperation(value = "给患者打scrm系统的标记", produces = "application/json")
    public AbcServiceResponse<PatientScrmFlagRsp> markPatientSCrmFlag(@RequestBody @Valid PatientScrmFlagReq req) {
        patientService.markPatientSCrmFlag(req.getChainId(), req.getPatientIds());
        PatientScrmFlagRsp rsp = new PatientScrmFlagRsp();
        rsp.setCode(0);
        rsp.setMessage("更新成功");
        return new AbcServiceResponse<>(rsp);
    }

    @PostMapping(value = "/scrm-patient-remind-info")
    @LogReqAndRsp
    @ApiOperation(value = "查询患者当天的提醒信息", produces = "application/json")
    public AbcServiceResponse<QueryScrmPatientRemindInfoRsp> queryScrmPatientRemindInfo(@RequestBody @Valid QueryScrmPatientRemindInfoReq req) {

        QueryScrmPatientRemindInfoRsp rsp = patientRemindStatisticsService.queryScrmPatientRemindInfo(req);

        return new AbcServiceResponse<>(rsp);

    }

    /**
     * 获取患者数量
     *
     * @param chainId
     * @param clinicId
     * @param beginDate
     * @param endDate
     * @return
     */
    @GetMapping(value = "/count")
    public AbcServiceResponse<PatientsCountRsp> getPatientsCount(@RequestParam(value = "chainId") String chainId,
                                                                 @RequestParam(value = "clinicId", required = false) String clinicId,
                                                                 @RequestParam(value = "beginDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date beginDate,
                                                                 @RequestParam(value = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {

        PatientsCountRsp rsp = patientService.getPatientsCount(chainId, clinicId, beginDate, endDate);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 获取SCRM患者统计数据
     *
     * @param chainId
     * @param clinicId
     * @param beginDate
     * @param endDate
     * @return
     */
    @GetMapping(value = "/scrm-count")
    public AbcServiceResponse<ScrmPatientsCountRsp> getScrmPatientsCount(@RequestParam(value = "chainId") String chainId,
                                                                         @RequestParam(value = "clinicId", required = false) String clinicId,
                                                                         @RequestParam(value = "beginDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date beginDate,
                                                                         @RequestParam(value = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {

        ScrmPatientsCountRsp rsp = patientService.getScrmPatientsCount(chainId, clinicId, beginDate, endDate);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 获取患者的随访记录
     */
    @GetMapping("/{patientId}/revisit/records")
    public AbcServiceResponse<PatientRevisitRecordListView> getPatientRevisitRecordsByPatientId(@PathVariable(value = "patientId") String patientId,
                                                                                                @RequestParam(value = "chainId", required = false) String chainId,
                                                                                                @RequestParam(value = "clinicId", required = false) String clinicId,
                                                                                                @RequestParam(value = "limit", defaultValue = "10") Integer limit,
                                                                                                @RequestParam(value = "offset", defaultValue = "0") Integer offset,
                                                                                                @RequestParam(value = "beginDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date beginDate,
                                                                                                @RequestParam(value = "endDate", required = false) @DateTimeFormat(pattern = "yyyy-MM-dd") Date endDate) {
        PatientRevisitRecordListView PatientRevisitRecordListView = patientRevisitService.getPatientRecentRevisitRecordList(chainId, clinicId, patientId, limit, offset, beginDate, endDate, null, null, 0);
        return new AbcServiceResponse<>(PatientRevisitRecordListView);
    }

    /**
     * 修改是否启用缓存配置
     */
    @PutMapping("/enable-cache")
    public AbcServiceResponse<String> setEnableCache(@RequestParam(value = "enableCache", required = false, defaultValue = "false") boolean enableCache) {
        patientCacheManager.setEnableCache(enableCache);
        return new AbcServiceResponse<>("success");
    }

    @PostMapping(value = "/get-patient-organ-by-ids")
    public AbcServiceResponse<AbcListPage<PatientOrganVO>> getPatientOrgan(@RequestBody @Valid GetPatientOrganReq req) {
        AbcListPage<PatientOrganVO> rsp = patientOrganService.getPatientOrganByIds(req.getIds());
        return new AbcServiceResponse<>(rsp);
    }

    @GetMapping(value = "/basic/query/by-sn/{patientSn}")
    public AbcServiceResponse<CisPatientInfo> queryPatientBySn(@PathVariable(value = "patientSn") String patientSn,
                                                               @RequestParam(value = "chainId") String chainId) {
        Patient patient = patientService.getPatientBySn(chainId, patientSn);
        return new AbcServiceResponse<>(PatientConvertor.patientToCisPatientInfo(patient));
    }

    /**
     * 获取患者的业务统计次数
     */
    @GetMapping(value = "/{patientId}/business-stat")
    public AbcServiceResponse<PatientBusinessStatRsp> getPatientBusinessStat(@PathVariable(value = "patientId") String patientId,
                                                                             @RequestParam(value = "chainId") String chainId) {
        PatientBusinessStatRsp rsp = patientService.getPatientBusinessStat(patientId, chainId);
        return new AbcServiceResponse<>(rsp);
    }

    /**
     * 生成随访自动执行任务
     */
    @PostMapping(value = "/revisit/auto-execute")
    public AbcServiceResponse<String> autoExecutePatientRevisitRecord() {
        patientRevisitRecordAutoExecuteTask.autoExecutePatientRevisitRecord();
        return new AbcServiceResponse<>("success");
    }

    /**
     * 会员卡充值原路退回回调
     * @param req
     * @return
     */
    @PostMapping({"/member/recharge-refund/callback"})
    public AbcServiceResponse<BasicCommonRsp> rechargeRefundCallback(@RequestBody RechargeRefundCallbackReq req) {
        BasicCommonRsp rsp = patientMemberService.rechargeRefundCallback(req);
        return new AbcServiceResponse<>(rsp);
    }
}
