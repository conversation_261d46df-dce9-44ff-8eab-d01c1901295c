package cn.abcyun.cis.crm.entity.patient.clinic;

import lombok.Data;

import java.time.Instant;

/**
 * 患者门店请求
 *
 * <AUTHOR>
 * @since 2023-12-08 10:01:08
 **/
@Data
public class PatientClinicReq {

    /**
     * 最近一次收费时间
     */
    private Instant lastChargeDate;

    /**
     * 第一次收费时间（忽略挂号费）
     */
    private Instant firstChargeDate;

    /**
     * 上次就诊时间
     */
    private Instant lastOutpatientDate;

    /**
     * 最近就诊医生ID
     */
    private String lastOutpatientDoctorId;

    /**
     * 欠费标识 0:无欠费 1:有欠费
     */
    private Integer arrearsFlag;

    /**
     * 咨询师ID
     */
    private String consultantId;


    /**
     * 责任治疗师
     */
    private String dutyTherapistId;

    /**
     * 首评治疗师
     */
    private String primaryTherapistId;

    /**
     * 首次检查/检验/体验时间
     */
    private Instant firstExaminationDate;

    /**
     * 首次住院结算时间
     */
    private Instant firstHisChargeSettleDate;


}
