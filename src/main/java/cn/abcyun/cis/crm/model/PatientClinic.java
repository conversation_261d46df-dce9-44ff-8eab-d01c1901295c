package cn.abcyun.cis.crm.model;


import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Id;
import javax.persistence.IdClass;
import javax.persistence.Table;
import java.time.Instant;

@Data
@Entity
@IdClass(PatientClinicURK.class)
@Table(name = "v2_patient_clinic")
public class PatientClinic {
    @Id
    private String clinicId;
    @Id
    private String patientId;
    private Instant lastActiveDate;
    private Instant lastOutpatientDate;
    /**
     * 最近一次收费时间
     */
    private Instant lastChargeDate;
    private String chainId;

    /**
     * 欠费标识 0:无欠费 1:有欠费
     */
    private Integer arrearsFlag;

    /**
     * 咨询师ID
     */
    private String consultantId;


    /**
     * 责任治疗师
     */
    private String dutyTherapistId;
    /**
     * 首评治疗师
     */
    private String primaryTherapistId;

    /**
     * 首次就诊医生
     */
    private String firstOutpatientDoctorId;

    /**
     * 首次就诊时间
     */
    private Instant firstOutpatientDate;

    /**
     * 首次收费时间（排除掉挂号费的第一次收费时间）
     * <p>
     * 这个时间定义是由统计那边给出的，也是因为统计那边需要才加上的，目前业务这边没有地方在使用
     */
    private Instant firstChargeDate;

    /**
     * 首次检查/检验/体验时间
     */
    private Instant firstExaminationDate;

    /**
     * 首次住院结算时间
     */
    private Instant firstHisChargeSettleDate;

    private Instant created;
    private String createdBy;
    private Instant lastModified;
    private String lastModifiedBy;
}