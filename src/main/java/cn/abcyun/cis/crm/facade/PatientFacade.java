package cn.abcyun.cis.crm.facade;

import cn.abcyun.bis.rpc.sdk.cis.message.crm.PatientMemberLevelMessage;
import cn.abcyun.bis.rpc.sdk.cis.message.mall.MallAfterSaleStatusChangeMessage;
import cn.abcyun.bis.rpc.sdk.cis.message.mall.MallOrderChangeMessage;
import cn.abcyun.bis.rpc.sdk.cis.message.mall.MallOrderMessageView;
import cn.abcyun.bis.rpc.sdk.cis.model.charge.CreateChargeSheetForFamilyDoctorRsp;
import cn.abcyun.bis.rpc.sdk.cis.model.mall.order.Constants;
import cn.abcyun.bis.rpc.sdk.cis.model.mall.order.MallOrderAfterSaleDetailView;
import cn.abcyun.bis.rpc.sdk.cis.model.patient.OperatorType;
import cn.abcyun.cis.commons.amqp.message.ChargeSheetMessage;
import cn.abcyun.cis.commons.exception.NotFoundException;
import cn.abcyun.cis.commons.exception.ParamRequiredException;
import cn.abcyun.cis.commons.model.ShebaoCardInfo;
import cn.abcyun.cis.commons.rpc.crm.UpdatePatientShebaoCardInfoReq;
import cn.abcyun.cis.crm.api.*;
import cn.abcyun.cis.crm.api.points.PatientPointsExchangeManualReq;
import cn.abcyun.cis.crm.api.points.PatientPointsExchangeManualRsp;
import cn.abcyun.cis.crm.common.RetryOnOptimisticLockingFailure;
import cn.abcyun.cis.crm.common.SysCommon;
import cn.abcyun.cis.crm.convertor.PatientMemberConvertor;
import cn.abcyun.cis.crm.domain.context.PatientContext;
import cn.abcyun.cis.crm.entity.PatientBasicInfo;
import cn.abcyun.cis.crm.entity.PatientInfo;
import cn.abcyun.cis.crm.exception.CrmCustomException;
import cn.abcyun.cis.crm.exception.CrmCustomSnRetryException;
import cn.abcyun.cis.crm.exception.CrmServiceException;
import cn.abcyun.cis.crm.model.PatientDeliveryInfo;
import cn.abcyun.cis.crm.model.PatientMember;
import cn.abcyun.cis.crm.mq.RocketMqProducer;
import cn.abcyun.cis.crm.repository.PatientMemberRepository;
import cn.abcyun.cis.crm.rpc.entity.*;
import cn.abcyun.cis.crm.rpc.entity.open.OpenApiUpdatePatientMemberReq;
import cn.abcyun.cis.crm.service.*;
import cn.abcyun.cis.crm.service.mall.PatientMallPointsAccumulateService;
import cn.abcyun.cis.crm.service.mall.PatientMallPointsRefundAccumulateService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.time.Instant;
import java.util.Objects;

/**
 * 患者门面
 *
 * <AUTHOR>
 * @since 2024/5/16 16:57
 **/
@Slf4j
@Component
public class PatientFacade {

    @Autowired
    private PatientService patientService;

    @Autowired
    private PatientMemberService patientMemberService;

    @Autowired
    private RocketMqProducer rocketMqProducer;

    @Autowired
    private PatientPointsService patientPointsService;

    @Autowired
    private PatientMemberRepository patientMemberRepository;

    @Autowired
    private MessageHandler messageHandler;

    @Autowired
    private PatientTraceService patientTraceService;

    @Autowired
    private PatientLogService patientLogService;

    @Autowired
    private McPatientService mcPatientService;

    @Autowired
    private PatientFamilyDoctorService patientFamilyDoctorService;

    @Autowired
    private PatientFamilyService patientFamilyService;

    @Autowired
    private PatientDeliveryService patientDeliveryService;
    @Autowired
    private PatientMallPointsAccumulateService patientMallPointsAccumulateService;
    @Autowired
    private PatientMallPointsRefundAccumulateService patientMallPointsRefundAccumulateService;

    /**
     * 添加会员
     */
    @Transactional
    @Retryable(value = {CrmCustomSnRetryException.class})
    public PatientInfo addPatientMember(PatientBasicInfo patientBasicInfo,
                                        String chainId,
                                        String clinicId,
                                        String employeeId,
                                        Integer hisType) throws CrmCustomException {
        PatientContext patientContext = PatientContext.newInstant(employeeId).setChainId(chainId).setPatientId(patientBasicInfo.getId()).setClinicId(clinicId);
        PatientInfo patientInfo = patientMemberService.addPatientMember(patientBasicInfo, chainId, clinicId, employeeId, hisType, patientContext);
        sendPatientMemberMessageIfNeeded(patientContext);
        return patientInfo;
    }

    /**
     * 更新会员信息
     */
    @Transactional
    public PatientInfo updatePatientMember(PatientBasicInfo patientBasicInfo, String employeeId) {
        PatientContext patientContext = PatientContext.newInstant(employeeId).setChainId(patientBasicInfo.getChainId()).setClinicId(patientBasicInfo.getClinicId()).setPatientId(patientBasicInfo.getId());
        PatientInfo patientInfo = patientMemberService.updatePatientMember(patientBasicInfo, employeeId, false, patientContext);
        sendPatientMemberMessageIfNeeded(patientContext);
        return patientInfo;
    }

    /**
     * 修改患者信息
     */
    @Transactional
    public PatientInfoView updatePatientInfo(UpdatePatientBasicInfoReq updatePatientBasicInfoReq,
                                             String patientId,
                                             String chainId,
                                             String clinicId,
                                             String employeeId,
                                             Integer hisType) {
        PatientContext patientContext = PatientContext.newInstant(employeeId).setChainId(chainId).setClinicId(clinicId).setPatientId(patientId);
        PatientInfoView patientInfoView = patientService.updatePatientInfo(updatePatientBasicInfoReq, patientId, chainId, clinicId, employeeId, hisType, patientContext);
        sendPatientMemberMessageIfNeeded(patientContext);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(patientContext));
        return patientInfoView;
    }

    @Transactional
    public PatientBasicInfoForWeClinic createOrFindPatient(CreatePatientByWeClinicReq req, Integer hisType) {
        PatientContext context = PatientContext.newInstant(req.getOperatorId(), req.getOperatorType()).setChainId(req.getChainId()).setPatientId(req.getPatientId());
        PatientBasicInfoForWeClinic patientBasicForWeClinic = patientService.createOrFindPatient(req, hisType, context);
        sendPatientMemberMessageIfNeeded(context);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(context));
        return patientBasicForWeClinic;
    }

    @Transactional
    public PatientFamilyView createPatientAndPatientFamily(PatientFamilyRelevanceReq patientFamilyRelevanceReq) {
        PatientContext context = PatientContext.newInstant(patientFamilyRelevanceReq.getOperatorId(), patientFamilyRelevanceReq.getOperatorType())
                .setChainId(patientFamilyRelevanceReq.getChainId())
                .setPatientId(patientFamilyRelevanceReq.getPatientId());
        PatientFamilyView patientFamilyView = patientService.createPatientAndPatientFamily(patientFamilyRelevanceReq, context);
        sendPatientMemberMessageIfNeeded(context);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(context));
        return patientFamilyView;
    }

    @Transactional
    public PatientInfoView createPatientByShebaoInfo(CreatePatientByShebaoReq req, String chainId, String clinicId, String employeeId, Integer hisType) {
        PatientContext context = PatientContext.newInstant(employeeId).setChainId(chainId).setClinicId(clinicId);
        PatientInfoView patientInfoView = patientService.createPatientByShebaoInfo(req, chainId, clinicId, employeeId, hisType, context);
        sendPatientMemberMessageIfNeeded(context);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(context));
        return patientInfoView;
    }

    @Transactional
    public PatientInfoView createPatientExternal(CreatePatientReq req, String chainId, String clinicId, String employeeId, Integer hisType) {
        PatientContext context = PatientContext.newInstant(employeeId).setChainId(chainId).setClinicId(clinicId);
        PatientInfoView patientInfoView = patientService.createPatientExternal(req, chainId, clinicId, employeeId, hisType, context);
        sendPatientMemberMessageIfNeeded(context);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(context));
        return patientInfoView;
    }

    @Transactional
    public PatientInfoView createPatientByCard(CreatePatientByCardReq req, String chainId, String clinicId, String employeeId, Integer hisType) {
        PatientContext context = PatientContext.newInstant(employeeId).setChainId(chainId).setClinicId(clinicId);
        PatientInfoView patientInfoView = patientService.createPatientByCard(req, chainId, clinicId, employeeId, hisType, context);
        sendPatientMemberMessageIfNeeded(context);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(context));
        return patientInfoView;
    }

    @Transactional
    public PatientImportRsp importPatient(PatientImportReq patientImportReq) throws CrmCustomException {
        PatientContext context = PatientContext.newInstant("import").setChainId(patientImportReq.getChainId());
        PatientImportRsp patientImportRsp = patientMemberService.importPatient(patientImportReq, context);
        sendPatientMemberMessageIfNeeded(context);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(context));
        return patientImportRsp;
    }

    @Transactional(rollbackFor = Exception.class)
    public PatientInfo createOrUpdatePatient(PatientBasicInfo patientBasicInfo,
                                             String chainId,
                                             String clinicId,
                                             String employeeId,
                                             Integer operatorType, Integer hisType) throws NotFoundException, CrmCustomException {
        PatientContext context = PatientContext.newInstant(employeeId, operatorType).setChainId(chainId).setPatientId(patientBasicInfo.getId()).setClinicId(clinicId);
        PatientInfo patientInfo = patientMemberService.createOrUpdatePatient(patientBasicInfo, chainId, clinicId, employeeId, hisType, context);
        sendPatientMemberMessageIfNeeded(context);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(context));
        return patientInfo;
    }

    @Transactional
    public PatientInfo insertOrUpdatePatientShebaoInfo(UpdatePatientShebaoCardInfoReq updatePatientShebaoCardInfoReq) throws NotFoundException, ParamRequiredException, CrmCustomException {
        PatientContext context = PatientContext.newInstant(updatePatientShebaoCardInfoReq.getOperatorId())
                .setChainId(updatePatientShebaoCardInfoReq.getChainId())
                .setClinicId(updatePatientShebaoCardInfoReq.getClinicId())
                .setPatientId(updatePatientShebaoCardInfoReq.getPatientId());
        PatientInfo patientInfo = patientService.insertOrUpdatePatientShebaoInfo(updatePatientShebaoCardInfoReq, context);
        sendPatientMemberMessageIfNeeded(context);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(context));
        return patientInfo;
    }

    @Transactional
    public void deletePatientMember(String patientId, String chainId, String clinicId, String employeeId) throws CrmServiceException {
        PatientContext context = PatientContext.newInstant(employeeId).setChainId(chainId).setPatientId(patientId).setClinicId(clinicId);
        patientMemberService.deletePatientMember(patientId, chainId, clinicId, employeeId, context);
        sendPatientMemberMessageIfNeeded(context);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(context));
    }

    @Transactional
    public PatientInfo createOrUpdatePatientInfoByQrCode(String chainId, String clinicId, QRCodeForPatientCrmReq req, String employeeId, Integer hisType) throws CrmCustomException {
        PatientContext context = PatientContext.newInstant(employeeId).setChainId(chainId).setClinicId(clinicId);
        PatientInfo patientInfo = patientService.createOrUpdatePatientInfoByQrCode(chainId, clinicId, req, employeeId, hisType, context);
        sendPatientMemberMessageIfNeeded(context);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(context));
        return patientInfo;
    }

    @RetryOnOptimisticLockingFailure
    @Transactional
    public PatientPointsDeductionRsp deductionRefundPatientPoints(PatientPointsDeductionRefundReq req) {
        PatientContext context = PatientContext.newInstant(req.getOperatorId()).setChainId(req.getChainId()).setClinicId(req.getClinicId()).setPatientId(req.getPatientId());
        PatientPointsDeductionRsp patientPointsDeductionRsp = patientPointsService.deductionRefundPatientPoints(req, context);
        sendPatientMemberMessageIfNeeded(context);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(context));
        return patientPointsDeductionRsp;
    }

    @RetryOnOptimisticLockingFailure
    @Transactional
    public PatientPointsDeductionRsp deductionPatientPoints(PatientPointsDeductionReq req) {
        PatientContext context = PatientContext.newInstant(req.getOperatorId()).setChainId(req.getChainId()).setClinicId(req.getClinicId()).setPatientId(req.getPatientId());
        PatientPointsDeductionRsp patientPointsDeductionRsp = patientPointsService.deductionPatientPoints(req, context);
        sendPatientMemberMessageIfNeeded(context);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(context));
        return patientPointsDeductionRsp;
    }

    @RetryOnOptimisticLockingFailure
    @Transactional
    public PatientPointsExchangeManualRsp exchangePatientPointsManual(String chainId,
                                                                      String clinicId,
                                                                      String employeeId,
                                                                      PatientPointsExchangeManualReq req,
                                                                      int action) {
        PatientContext context = PatientContext.newInstant(employeeId).setChainId(chainId).setClinicId(clinicId).setPatientId(req.getPatientId());
        PatientPointsExchangeManualRsp patientPointsExchangeManualRsp = patientPointsService.exchangePatientPointsManual(chainId, clinicId, employeeId, req, action, context);
        sendPatientMemberMessageIfNeeded(context);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(context));
        return patientPointsExchangeManualRsp;
    }

    public void onChargeSheetMessageReceived(ChargeSheetMessage message) {
        PatientContext context = PatientContext.newInstant(message.getOperatorId());
        if (message.getChargeSheet() != null) {
            ChargeSheetMessage.ChargeSheet chargeSheet = message.getChargeSheet();
            context.setPatientId(chargeSheet.getPatientId()).setChainId(chargeSheet.getChainId()).setClinicId(chargeSheet.getClinicId());
        }
        try {
            messageHandler.handleChargeMessage(message, context);
        } catch (Exception e) {
            log.warn("handle ChargeMsg failed. catch exception: ", e);
        }

        try {
            patientTraceService.dealWithChargeSheetMessage(message);
        } catch (Exception e) {
            log.error("patientTraceService handle ChargeSheetMessage error", e);
        }

        sendPatientMemberMessageIfNeeded(context);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(context));
    }

    private void sendPatientMemberMessageIfNeeded(PatientContext patientContext) {
        if (patientContext == null) {
            return;
        }

        if (patientContext.getPatientMemberAction() == null) {
            return;
        }
        if (!patientContext.isValid()) {
            log.warn("patientContext is invalid");
            return;
        }

        try {
            RocketMqProducer.doAfterTransactionCommit(() -> {
                PatientMemberLevelMessage patientMemberLevelMessage = buildPatientMemberLevelMessage(patientContext);
                if (patientMemberLevelMessage == null) {
                    return;
                }
                rocketMqProducer.sendPatientMemberLevelMessage(patientMemberLevelMessage);
            });
        } catch (Exception e) {
            log.error("sendPatientMemberMessageIfNeeded error", e);
        }
    }

    private PatientMemberLevelMessage buildPatientMemberLevelMessage(PatientContext patientContext) {
        if (patientContext.getPatientMemberAction() == null) {
            return null;
        }

        if (patientContext.getPatientMemberAction() == PatientContext.PatientMemberAction.ADD) {
            // 查询最新的会员信息
            PatientMember patientMember = patientMemberRepository.findPatientMemberByPatientIdAndChainIdAndStatus(patientContext.getPatientId(), patientContext.getChainId(), PatientMember.Status.NORMAL);
            if (patientMember == null) {
                log.warn("patientMember is null");
                return null;
            }
            return doBuildPatientMemberLevelMessage(PatientMemberLevelMessage.Type.ADD, patientContext.getChainId(), patientContext.getPatientId(), null, patientMember, Instant.now(), patientContext.getOperatorId());
        } else if (patientContext.getPatientMemberAction() == PatientContext.PatientMemberAction.UPDATE) {
            PatientMember beforePatientMember = patientContext.getBeforePatientMember();
            if (beforePatientMember == null) {
                log.warn("beforePatientMember is null");
                return null;
            }
            PatientMember nowPatientMember = patientMemberRepository.findPatientMemberByPatientIdAndChainId(patientContext.getPatientId(), patientContext.getChainId());
            if (nowPatientMember == null) {
                log.warn("nowPatientMember is null");
                return null;
            }
            return doBuildPatientMemberLevelMessage(PatientMemberLevelMessage.Type.UPDATE, patientContext.getChainId(), patientContext.getPatientId(), beforePatientMember, nowPatientMember, Instant.now(), patientContext.getOperatorId());
        } else if (patientContext.getPatientMemberAction() == PatientContext.PatientMemberAction.DELETE) {
            PatientMember beforePatientMember = patientContext.getBeforePatientMember();
            if (beforePatientMember == null) {
                log.warn("beforePatientMember is null");
                return null;
            }
            return doBuildPatientMemberLevelMessage(PatientMemberLevelMessage.Type.DELETE, patientContext.getChainId(), patientContext.getPatientId(), beforePatientMember, null, Instant.now(), patientContext.getOperatorId());
        }
        return null;
    }

    private PatientMemberLevelMessage doBuildPatientMemberLevelMessage(int type,
                                                                       String chainId,
                                                                       String patientId,
                                                                       PatientMember before,
                                                                       PatientMember now,
                                                                       Instant operateTime,
                                                                       String operator) {
        if (before == null && now == null) {
            return null;
        }
        PatientMemberLevelMessage patientMemberLevelMessage = new PatientMemberLevelMessage();
        patientMemberLevelMessage.setType(type);
        patientMemberLevelMessage.setChainId(chainId);
        patientMemberLevelMessage.setPatientId(patientId);
        patientMemberLevelMessage.setBefore(PatientMemberConvertor.INST.toRpcPatientMember(before));
        patientMemberLevelMessage.setNow(PatientMemberConvertor.INST.toRpcPatientMember(now));
        patientMemberLevelMessage.setOperateTime(operateTime);
        patientMemberLevelMessage.setOperator(operator);
        return patientMemberLevelMessage;
    }

    /**
     * 修改患者和患者家庭成员信息
     */
    @Transactional(rollbackFor = Exception.class)
    public PatientFamilyView updatePatientAndPatientFamily(PatientFamilyRelevanceReq patientFamilyRelevanceReq) {
        PatientContext patientContext = PatientContext.newInstant(patientFamilyRelevanceReq.getOperatorId(), patientFamilyRelevanceReq.getOperatorType())
                .setPatientId(patientFamilyRelevanceReq.getPatientId())
                .setChainId(patientFamilyRelevanceReq.getChainId());
        PatientFamilyView patientFamilyView = mcPatientService.updatePatientAndPatientFamily(patientFamilyRelevanceReq, patientContext);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(patientContext));
        return patientFamilyView;
    }

    public void updatePatientPastHistory(String patientId, String pastHistory, String chainId, String clinicId, String employeeId) {
        PatientContext patientContext = PatientContext.newInstant(employeeId).setChainId(chainId).setClinicId(clinicId).setPatientId(patientId);
        patientService.updatePatientPastHistory(patientId, pastHistory, chainId, employeeId, patientContext);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(patientContext));
    }

    public PatientInfo bindPatientInfoWithExternalCodeInfo(String chainId, String clinicId, String qrCode, String patientId, String employeeId) {
        PatientContext patientContext = PatientContext.newInstant(employeeId).setChainId(chainId).setClinicId(clinicId).setPatientId(patientId);
        PatientInfo patientInfo = patientService.bindPatientInfoWithExternalCodeInfo(chainId, clinicId, qrCode, patientId, patientContext);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(patientContext));
        return patientInfo;
    }

    @Transactional
    public CreateChargeSheetForFamilyDoctorRsp signatureDoctorFamily(SignatureDoctorFamilyReq req, String randomId) {
        PatientContext patientContext = PatientContext.newInstant(req.getOperator());
        CreateChargeSheetForFamilyDoctorRsp rsp = patientFamilyDoctorService.signatureDoctorFamily(req, randomId, patientContext);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(patientContext));
        return rsp;
    }

    @Transactional
    public CreatePatientHealthCardRsp createPatientHealthCard(CreatePatientHealthCardReq req) {
        PatientContext patientContext = PatientContext.newInstant(req.getOperatorId()).setChainId(req.getChainId()).setPatientId(req.getPatientId());
        CreatePatientHealthCardRsp rsp = patientService.createPatientHealthCard(req, patientContext);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(patientContext));
        return rsp;
    }

    @Transactional
    public PatientBasicInfoForWeClinic updatePatientInfoByWx(CreatePatientByWeClinicReq createPatientByWeClinicReq, String patientId) {
        PatientContext patientContext = PatientContext.newInstant(createPatientByWeClinicReq.getOperatorId(), createPatientByWeClinicReq.getOperatorType()).setChainId(createPatientByWeClinicReq.getChainId()).setPatientId(patientId);
        PatientBasicInfoForWeClinic rsp = patientService.updatePatientInfoByWx(createPatientByWeClinicReq, patientId, patientContext);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(patientContext));
        return rsp;
    }

    public PatientInfoView bindPatientShebaoInfo(ShebaoCardInfo req, String patientId, String chainId, String clinicId, String employeeId) {
        PatientContext patientContext = PatientContext.newInstant(employeeId).setChainId(chainId).setPatientId(patientId).setClinicId(clinicId);
        PatientInfoView rsp = patientService.bindPatientShebaoInfo(req, patientId, chainId, employeeId, patientContext);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(patientContext));
        return rsp;
    }

    @Transactional
    public PatientInfoView bindPatientCardInfo(BindPatientCardInfoReq req, String patientId, String chainId, String clinicId, String employeeId) {
        PatientContext patientContext = PatientContext.newInstant(employeeId).setChainId(chainId).setPatientId(patientId).setClinicId(clinicId);
        PatientInfoView rsp = patientService.bindPatientCardInfo(req, patientId, chainId, clinicId, employeeId, patientContext);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(patientContext));
        return rsp;
    }

    /**
     * 删除家庭成员
     */
    @Transactional(rollbackFor = Exception.class)
    public PatientFamilyVo deletePatientFamily(String patientId, String chainId, String clinicId, String parentId, String operatorId, Integer operatorType) {
        PatientContext patientContext = PatientContext.newInstant(operatorId, operatorType).setChainId(chainId).setPatientId(patientId).setClinicId(clinicId);
        PatientFamilyVo patientFamilyVo = patientFamilyService.deletePatientFamily(patientId, chainId, parentId, operatorId, patientContext);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(patientContext));
        return patientFamilyVo;
    }

    /**
     * 添加患者配送信息
     */
    @Transactional
    public PatientDeliveryInfo addPatientDeliveryInfo(String patientId, PatientDeliveryInfoReq patientDeliveryInfoReq, String employeeId) {
        PatientContext patientContext = PatientContext.newInstant(employeeId, patientDeliveryInfoReq.getOperatorType()).setChainId(patientDeliveryInfoReq.getChainId()).setPatientId(patientId);
        PatientDeliveryInfo patientDeliveryInfo = patientDeliveryService.addPatientDeliveryInfo(patientId, patientDeliveryInfoReq, employeeId, patientContext);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(patientContext));
        return patientDeliveryInfo;
    }

    /**
     * 更新患者配送信息
     */
    @Transactional
    public PatientDeliveryInfo updatePatientDeliveryInfo(String patientId, String deliveryId, PatientDeliveryInfoReq patientDeliveryInfoReq, String operatorId) {
        PatientContext patientContext = PatientContext.newInstant(operatorId, patientDeliveryInfoReq.getOperatorType()).setChainId(patientDeliveryInfoReq.getChainId()).setPatientId(patientId);
        PatientDeliveryInfo patientDeliveryInfo = patientDeliveryService.updatePatientDeliveryInfo(patientId, deliveryId, patientDeliveryInfoReq, patientContext);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(patientContext));
        return patientDeliveryInfo;
    }

    /**
     * 删除患者配送信息
     */
    @Transactional
    public int deletePatientDeliveryInfo(String patientId, String deliveryId, Integer operatorType) {
        PatientContext patientContext = PatientContext.newInstant(SysCommon.UNKNOWN_ID, operatorType).setPatientId(patientId);
        int rsp = patientDeliveryService.deletePatientDeliveryInfo(patientId, deliveryId);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(patientContext));
        return rsp;
    }

    /**
     * 开放平台修改患者会员请求
     */
    @Transactional
    public void openApiUpdatePatientMember(String patientId, OpenApiUpdatePatientMemberReq req) {
        PatientContext patientContext = PatientContext.newInstant(null, OperatorType.OPEN_API)
                .setChainId(req.getChainId()).setClinicId(req.getClinicId()).setPatientId(patientId);
        patientMemberService.openApiUpdatePatientMember(patientId, req, patientContext);
        sendPatientMemberMessageIfNeeded(patientContext);
    }

    /**
     * 处理微商城支付成功后给患者增加积分的功能
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleCisMallOrderChangeMessage(MallOrderChangeMessage message) {
        // 1.获取需要积分的患者信息:PatientPoints如果不存在则新建
        String chainId = message.getChainId();
        String clinicId = message.getOrganId();
        String patientId = message.getPatientId();
        MallOrderMessageView mallOrderView = message.getMallOrderView();
        if (StringUtils.isAnyBlank(chainId, clinicId, patientId) || Objects.isNull(mallOrderView) || CollectionUtils.isEmpty(mallOrderView.getItems())) {
            log.error("receive mall order params is null");
            return;
        }
        // 这一步设置是为了防止外面传了值,里面不赋值的情况
        mallOrderView.setChainId(chainId);
        mallOrderView.setOrganId(clinicId);
        mallOrderView.setPatientId(patientId);
        PatientContext context = PatientContext.newInstant(message.getOperatorId()).setPatientId(patientId).setChainId(chainId).setClinicId(clinicId);
        // 如果是支付
        if (Objects.equals(mallOrderView.getOldStatus(), Constants.MallOrderStatus.WAIT_PAY)
                && !Objects.equals(mallOrderView.getStatus(), Constants.MallOrderStatus.CLOSED)) {
            patientMallPointsAccumulateService.handleCisMallOrderPayMessage(mallOrderView, context);
        }
        this.sendPatientMemberMessageIfNeeded(context);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(context));
    }

    /**
     * 处理微商城售后成功后给患者扣除累计积分的功能
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleMallAfterSaleStatusChangeMessage(MallAfterSaleStatusChangeMessage message) {
        if (Objects.isNull(message) || Objects.isNull(message.getMallOrderAfterSale())) {
            return;
        }
        MallOrderAfterSaleDetailView mallOrderAfterSale = message.getMallOrderAfterSale();

        // 1.获取需要积分的患者信息:PatientPoints如果不存在则新建
        String chainId = mallOrderAfterSale.getChainId();
        String clinicId = mallOrderAfterSale.getOrganId();
        String patientId = mallOrderAfterSale.getPatientId();
        if (StringUtils.isAnyBlank(chainId, clinicId, patientId)) {
            log.error("receive after sale params is null");
            return;
        }
        // 如果是售后完成
        if (!Objects.equals(mallOrderAfterSale.getStatus(), Constants.MallOrderAfterSaleStatus.FINISH)) {
            return;
        }
        PatientContext context = PatientContext.newInstant(message.getOperatorId()).setPatientId(patientId).setChainId(chainId).setClinicId(clinicId);

        patientMallPointsRefundAccumulateService.handleCisMallAfterSaleFinishMessage(mallOrderAfterSale, context);

        this.sendPatientMemberMessageIfNeeded(context);
        RocketMqProducer.doAfterTransactionCommit(() -> patientLogService.sendOperationLogMessageIfNeeded(context));
    }
}