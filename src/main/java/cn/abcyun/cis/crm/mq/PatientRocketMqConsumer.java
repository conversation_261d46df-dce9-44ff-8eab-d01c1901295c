package cn.abcyun.cis.crm.mq;

import cn.abcyun.bis.rpc.sdk.cis.message.charge.ChargePatientOweAmountChangedMessage;
import cn.abcyun.bis.rpc.sdk.cis.message.crm.PatientConsultantChangeMessage;
import cn.abcyun.bis.rpc.sdk.cis.message.examination.ExaminationSheetMessage;
import cn.abcyun.bis.rpc.sdk.cis.message.mall.MallAfterSaleStatusChangeMessage;
import cn.abcyun.bis.rpc.sdk.cis.message.mall.MallOrderChangeMessage;
import cn.abcyun.bis.rpc.sdk.cis.message.medicalplan.MedicalPlanFollowupRecordMessage;
import cn.abcyun.bis.rpc.sdk.cis.message.outpatient.MedicalRecordMessage;
import cn.abcyun.bis.rpc.sdk.his.message.patientorder.HisPatientOrderMessage;
import cn.abcyun.bis.rpc.sdk.pe.message.order.PeSheetMessage;
import cn.abcyun.cis.commons.amqp.message.OrganMessage;
import cn.abcyun.cis.commons.amqp.message.PatientAttachmentMessage;
import cn.abcyun.cis.commons.amqp.message.TreatmentExecuteMessage;
import cn.abcyun.cis.commons.amqp.message.patient.merge.PatientMergeTaskReport;
import cn.abcyun.cis.commons.model.Organ;
import cn.abcyun.cis.commons.util.JsonUtils;
import cn.abcyun.cis.crm.facade.PatientFacade;
import cn.abcyun.cis.crm.model.OutpatientFinishMessage;
import cn.abcyun.cis.crm.mq.entity.FamilyDoctorMessage;
import cn.abcyun.cis.crm.mq.entity.PatientRevisitRecordMessage;
import cn.abcyun.cis.crm.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.util.Objects;

/**
 * 患者消息 rocket mq 消费者
 *
 * <AUTHOR>
 * @date 2023/10/13 14:18
 **/
@Slf4j
@Component
public class PatientRocketMqConsumer {

    @Autowired
    private RocketMqProducer rocketMqProducer;

    @Autowired
    private PatientRevisitService patientRevisitService;

    @Autowired
    private PatientMergeService patientMergeService;

    @Autowired
    private PatientFamilyDoctorService patientFamilyDoctorService;

    @Autowired
    private PatientAttachmentService patientAttachmentService;

    @Autowired
    private PatientService patientService;

    @Autowired
    private PatientRevisitTemplateService patientRevisitTemplateService;

    @Autowired
    private PatientSourceTypeService patientSourceTypeService;

    @Autowired
    private PatientSnGenerateService patientSnGenerateService;

    @Autowired
    private PatientTagService patientTagService;

    @Autowired
    private PatientMemberTypeService patientMemberTypeService;

    @Autowired
    private PatientTraceService patientTraceService;

    @Autowired
    private PatientClinicService patientClinicService;

    @Autowired
    private PatientFacade patientFacade;

    /**
     * 监听患者随访记录执行
     */
    @Service
    @RocketMQMessageListener(topic = "${rocketmq.topic.cis-crm-delay.name}", //topic名
            selectorExpression = "${rocketmq.tag.cis-crm-delay.patient-revisit-record}", //tag名
            consumerGroup = "${rocketmq.group.cis-crm-delay-patient-revisit-record.name}",
            consumeThreadNumber = 3
    )
    public class PatientRevisitRecordMessageListener implements RocketMQListener<PatientRevisitRecordMessage> {
        // 监听到消息就会执行此方法
        @Override
        public void onMessage(PatientRevisitRecordMessage message) {
            log.info("receive PatientRevisitRecordMessage: {}", JsonUtils.dump(message));

            if (message == null) {
                return;
            }

            Instant deliverTime = message.getDeliverTime();
            if (deliverTime.compareTo(Instant.now()) > 0) {
                // 当没有到执行时间，继续丢到消息队列中
                rocketMqProducer.sendPatientRevisitExecuteDelayMessage(message);
                return;
            }

            try {
                patientRevisitService.executePatientRecord(message);
            } catch (Exception e) {
                log.error("handle PatientRevisitRecordMessage error. message: {}", e.getMessage());
            }
        }
    }

    /**
     * 监听就诊完成
     */
    @Service
    @RocketMQMessageListener(topic = "${rocketmq.topic.cis-crm-delay.name}",
            selectorExpression = "${rocketmq.tag.cis-crm-delay.outpatient-finish}",
            consumerGroup = "${rocketmq.group.cis-crm-delay-outpatient-finish.name}",
            consumeThreadNumber = 2
    )
    public class OutpatientFinishMessageListener implements RocketMQListener<OutpatientFinishMessage> {
        // 监听到消息就会执行此方法
        @Override
        public void onMessage(OutpatientFinishMessage message) {
            log.info("receive OutpatientFinishMessage: {}", JsonUtils.dump(message));

            if (message == null) {
                return;
            }

            try {
                patientRevisitService.createPatientRevisitRecordByTaskRule(message);
            } catch (Exception e) {
                log.error("handle OutpatientFinishMessage error. message: {}", e.getMessage());
            }
        }
    }

    /**
     * 新建门店
     */
    @Service
    @RocketMQMessageListener(topic = "${rocketmq.topic.cis-sc-clinic.name}",
            selectorExpression = "${rocketmq.tag.cis-sc-clinic.clinic-create-update}",
            consumerGroup = "${rocketmq.group.cis-sc-clinic-clinic-create-update.name}",
            consumeThreadNumber = 3
    )
    public class OrganMessageListener implements RocketMQListener<OrganMessage> {
        // 监听到消息就会执行此方法
        @Override
        public void onMessage(OrganMessage message) {
            try {
                log.info("receiveOrganMessage:{}, clinicId:{}, message:{}", message.getType(), message.getClinicId(), cn.abcyun.cis.crm.common.JsonUtils.dump(message));
                if (message.getType() == OrganMessage.MSG_TYPE_ORGAN_CREATE) {
                    Organ organ = message.getNewVal();
                    if (organ == null) {
                        return;
                    }
                    if (organ.getNodeType() != 2) {
                        patientSourceTypeService.initOrganDefaultSourceType(organ.getId());
                        patientSnGenerateService.flushPatientSnByChainId(organ.getId(), 1000);
                        patientTagService.initChainPatientTagTypeIfNeeded(organ.getId(), organ.getHisType());
                    }
                    patientRevisitTemplateService.initClinicRevisitTargetTemplate(organ.getParentId(), organ.getId(), organ.getHisType());
                }

                if (message.getType() == OrganMessage.MSG_TYPE_ORGAN_CREATE || message.getType() == OrganMessage.MSG_TYPE_ORGAN_UPDATE) {
                    Organ newOrgan = message.getNewVal();
                    if (newOrgan.getNodeType() != Organ.Type.CHAIN_BRANCH_CLINIC && newOrgan.getHisType() == Organ.HisType.CIS_HIS_TYPE_PHARMACY) {
                        patientMemberTypeService.insertDefaultMemberTypeForPharmacy(newOrgan);
                    }
                }
            } catch (Exception e) {
                log.error("initOrganDefaultSourceType error", e);
            }
        }
    }

    /**
     * 监听患者合并结果
     */
    @Service
    @RocketMQMessageListener(topic = "${rocketmq.topic.cis-crm.name}",
            selectorExpression = "${rocketmq.tag.cis-crm.patient-merge-report}",
            consumerGroup = "${rocketmq.group.cis-crm-patient-merge-report.name}",
            consumeThreadNumber = 2
    )
    public class PatientMergeTaskReportListener implements RocketMQListener<PatientMergeTaskReport> {
        // 监听到消息就会执行此方法
        @Override
        public void onMessage(PatientMergeTaskReport patientMergeTaskReport) {
            log.info("receive PatientMergeTaskReport: {}", JsonUtils.dump(patientMergeTaskReport));

            if (patientMergeTaskReport == null) {
                return;
            }

            try {
                patientMergeService.updatePatientMergeTaskByTaskReport(patientMergeTaskReport);
            } catch (Exception e) {
                log.error("handle PatientMergeTaskReport error. message: {}", e.getMessage());
            }
        }
    }

    /**
     * 监听家庭医生签约超时消息
     */
    @Service
    @RocketMQMessageListener(topic = "${rocketmq.topic.cis-crm-delay.name}",
            selectorExpression = "${rocketmq.tag.cis-crm-delay.family-doctor-sign-timeout}",
            consumerGroup = "${rocketmq.group.cis-crm-delay-family-doctor-sign-timeout.name}",
            consumeThreadNumber = 1
    )
    public class FamilyDoctorMessageListener implements RocketMQListener<FamilyDoctorMessage> {
        // 监听到消息就会执行此方法
        @Override
        public void onMessage(FamilyDoctorMessage familyDoctorMessage) {
            log.info("receive FamilyDoctorMessage: {}", JsonUtils.dump(familyDoctorMessage));

            if (familyDoctorMessage == null) {
                return;
            }

            try {
                patientFamilyDoctorService.overTimeUpdateState(familyDoctorMessage);
            } catch (Exception e) {
                log.error("handle FamilyDoctorMessage error. message: {}", e.getMessage());
            }
        }
    }

    /**
     * 监听患者附件消息
     */
    @Service
    @RocketMQMessageListener(topic = "${rocketmq.topic.cis-crm.name}", //topic名
            selectorExpression = "${rocketmq.tag.cis-crm.patient-attachment}", //tag名
            consumerGroup = "${rocketmq.group.cis-crm-patient-attachment.name}",
            consumeThreadNumber = 3
    )
    public class PatientAttachmentMessageListener implements RocketMQListener<PatientAttachmentMessage> {
        // 监听到消息就会执行此方法
        @Override
        public void onMessage(PatientAttachmentMessage patientAttachmentMessage) {
            log.info("receive PatientAttachmentMessage: {}", JsonUtils.dump(patientAttachmentMessage));

            if (patientAttachmentMessage == null) {
                return;
            }

            try {
                patientAttachmentService.processPatientAttachmentMessage(patientAttachmentMessage);
            } catch (Exception e) {
                log.error("handle PatientAttachmentMessage error. message: {}", e.getMessage());
            }
        }
    }

    /**
     * 监听患者欠费金额变更消息
     */
    @Service
    @RocketMQMessageListener(topic = "${rocketmq.topic.cis-charge.name}", //topic名
            selectorExpression = "${rocketmq.tag.cis-charge.patient-owe-amount-changed}", //tag名
            consumerGroup = "${rocketmq.group.cis-charge-patient-owe-amount-changed.name}",
            consumeThreadNumber = 2
    )
    public class ChargePatientOweAmountChangedMessageListener implements RocketMQListener<ChargePatientOweAmountChangedMessage> {
        @Override
        public void onMessage(ChargePatientOweAmountChangedMessage patientArrearsMessage) {
            log.info("receive patientArrearsMessage: [{}]", patientArrearsMessage);
            try {
                patientService.processPatientArrearsMessage(patientArrearsMessage);
            } catch (Exception e) {
                log.warn("process patientArrearsMessage [{}] failed. catch exception: ", patientArrearsMessage, e);
            }
        }
    }

    /**
     * 监听门诊病历更新消息
     */
    @Service
    @RocketMQMessageListener(topic = "${rocketmq.topic.cis-outpatient.name}", //topic名
            selectorExpression = "${rocketmq.tag.cis-outpatient.medical-record}", //tag名
            consumerGroup = "${rocketmq.group.cis-outpatient-medical-record.name}",
            consumeThreadNumber = 2
    )
    public class MedicalRecordMessageListener implements RocketMQListener<MedicalRecordMessage> {
        @Override
        public void onMessage(MedicalRecordMessage medicalRecordMessage) {
            log.info("receive medicalRecordMessage: [{}]", JsonUtils.dump(medicalRecordMessage));
            try {
                patientService.processMedicalRecordMessage(medicalRecordMessage);
            } catch (Exception e) {
                log.warn("process medicalRecordMessage [{}] failed. catch exception: ", medicalRecordMessage, e);
            }
        }
    }

    /**
     * 监听跟进记录消息
     */
    @Service
    @RocketMQMessageListener(topic = "${rocketmq.topic.cis-medical-plan.name}",
            selectorExpression = "${rocketmq.tag.cis-medical-plan.medical-plan-followup-record}",
            consumerGroup = "${rocketmq.group.cis-medical-plan-followup-record.name}",
            consumeThreadNumber = 1
    )
    public class MedicalPlanFollowupRecordMessageListener implements RocketMQListener<MedicalPlanFollowupRecordMessage> {
        // 监听到消息就会执行此方法
        @Override
        public void onMessage(MedicalPlanFollowupRecordMessage medicalPlanFollowupRecordMessage) {
            log.info("receive MedicalPlanFollowupRecordMessage: {}", cn.abcyun.cis.crm.common.JsonUtils.dump(medicalPlanFollowupRecordMessage));
            try {
                patientTraceService.dealWithMedicalPlanFollowupRecordMessage(medicalPlanFollowupRecordMessage);
            } catch (Exception e) {
                log.error("messageHandleFacade.handleAdviceExecuteMessage error. message: {}", e.getMessage());
            }
        }
    }

    /**
     * 监听检查检验单变更消息
     */
    @Service
    @RocketMQMessageListener(topic = "${rocketmq.topic.cis-examination.name}",
            selectorExpression = "${rocketmq.tag.cis-examination.examination-sheet-change}",
            consumerGroup = "${rocketmq.group.cis-examination-examination-sheet-change.name}",
            consumeThreadNumber = 5
    )
    public class ExaminationSheetMessageListener implements RocketMQListener<ExaminationSheetMessage> {
        // 监听到消息就会执行此方法
        @Override
        public void onMessage(ExaminationSheetMessage message) {
            log.info("receive ExaminationSheetMessage message:{}", cn.abcyun.cis.crm.common.JsonUtils.dump(message));
            if (Objects.isNull(message) || message.getExaminationSheetView() == null) {
                return;
            }
            try {
                patientTraceService.dealWithExaminationSheetMessage(message);
            } catch (Exception e) {
                log.error("dealWithExaminationSheetMessage error. ", e);
            }

            try {
                patientClinicService.dealWithExaminationSheetMessage(message);
            } catch (Exception e) {
                log.error("dealWithExaminationSheetMessage error. ", e);
            }
        }
    }

    /**
     * 监听跟执行消息
     */
    @Service
    @RocketMQMessageListener(topic = "${rocketmq.topic.cis-charge.name}",
            selectorExpression = "${rocketmq.tag.cis-charge.treatment-execute}",
            consumerGroup = "${rocketmq.group.cis-charge-treatment-execute.name}",
            consumeThreadNumber = 1
    )
    public class TreatmentExecuteMessageListener implements RocketMQListener<TreatmentExecuteMessage> {
        // 监听到消息就会执行此方法
        @Override
        public void onMessage(TreatmentExecuteMessage message) {
            log.info("receive TreatmentExecuteMessage message:{}", cn.abcyun.cis.crm.common.JsonUtils.dump(message));
            if (Objects.isNull(message)) {
                return;
            }
            try {
                patientTraceService.dealWithTreatmentExecuteMessage(message);
            } catch (Exception e) {
                log.error("dealWithTreatmentExecuteMessage error. ", e);
            }
        }
    }


    /**
     * 监听患者咨询师变更消息
     */
    @Service
    @RocketMQMessageListener(topic = "${rocketmq.topic.cis-crm.name}",
            selectorExpression = "${rocketmq.tag.cis-crm.patient-consultant-change}",
            consumerGroup = "${rocketmq.group.cis-crm-patient-consultant-change.name}",
            consumeThreadNumber = 1
    )
    public class PatientConsultantChangeMessageListener implements RocketMQListener<PatientConsultantChangeMessage> {
        // 监听到消息就会执行此方法
        @Override
        public void onMessage(PatientConsultantChangeMessage message) {
            log.info("receive PatientConsultantChangeMessage message:{}", cn.abcyun.cis.crm.common.JsonUtils.dump(message));
            if (Objects.isNull(message)) {
                return;
            }
            try {
                patientClinicService.dealWithPatientConsultantChangeMessage(message);
            } catch (Exception e) {
                log.error("dealWithPatientConsultantChangeMessage error. ", e);
            }
        }
    }


    /**
     * 监听住院结算单消息
     */
    @Service
    @RocketMQMessageListener(topic = "${rocketmq.topic.his-patient-order.name}", //topic名
            selectorExpression = "${rocketmq.tag.his-patient-order.status-change}", //tag名
            consumerGroup = "${rocketmq.group.his-patient-order-status-change.name}" //group名
    )
    public class HisPatientOrderMessageListener implements RocketMQListener<HisPatientOrderMessage> {

        @Override
        public void onMessage(HisPatientOrderMessage message) {
            log.info("receive HisPatientOrderMessage:{}", cn.abcyun.cis.commons.util.JsonUtils.dump(message));
            if (message == null) {
                return;
            }

            try {
                patientClinicService.dealWithHisPatientOrderMessage(message);
            } catch (Exception e) {
                log.error("patientClinic handle HisPatientOrderMessage error. ", e);
            }

            try {
                patientTraceService.dealWithHisPatientOrderMessage(message);
            } catch (Exception e) {
                log.error("handle HisPatientOrderMessage error", e);
            }

            //自动随访
            try {
                patientRevisitService.createPatientRevisitRecordByHisPatientOrderMessage(message);
            } catch (Exception e) {
                log.error("handle HisPatientOrderMessage error. message: {}", e.getMessage());
            }
        }
    }


    /**
     * 监听体检单消息
     */
    @Service
    @RocketMQMessageListener(topic = "${rocketmq.topic.pe-order.name}", //topic名
            selectorExpression = "${rocketmq.tag.pe-order.sheet-status-change}", //tag名
            consumerGroup = "${rocketmq.group.pe-sheet-status-change.name}" //group名
    )
    public class PeSheetMessageListener implements RocketMQListener<PeSheetMessage> {

        @Override
        public void onMessage(PeSheetMessage message) {
            log.info("receive PeSheetMessage:{}", cn.abcyun.cis.commons.util.JsonUtils.dump(message));
            if (message == null) {
                return;
            }

            try {
                patientTraceService.dealWithPeSheetMessage(message);
            } catch (Exception e) {
                log.error("handle PeSheetMessage error", e);
            }

            try {
                patientClinicService.dealWithPeSheetMessage(message);
            } catch (Exception e) {
                log.error("handle PeSheetMessage error", e);
            }
        }
    }

    /**
     * 监听微商城缴费单记录信息--增加积分
     */
    @Service
    @RocketMQMessageListener(topic = "${rocketmq.topic.cis-mall-order.name}",
            selectorExpression = "${rocketmq.tag.cis-mall-order.status-change}",
            consumerGroup = "${rocketmq.group.cis-mall-order-status-change.name}",
            consumeThreadNumber = 5
    )
    public class CisMallOrderChangeMessageListener implements RocketMQListener<MallOrderChangeMessage> {
        @Override
        public void onMessage(MallOrderChangeMessage message) {
            log.info("Receive CisMallOrderChange Message:{}", cn.abcyun.cis.crm.common.JsonUtils.dump(message));
            if (Objects.isNull(message)) {
                return;
            }
            try {
                // 处理微商城订单信息
                patientFacade.handleCisMallOrderChangeMessage(message);
            } catch (Exception e) {
                log.error("Deal With CisMallChargeSheetMessage Error. ", e);
            }
        }
    }

    /**
     * 监听微商城售后单信息--扣出消费累计积分
     */
    @Service
    @RocketMQMessageListener(topic = "${rocketmq.topic.cis-mall-order.name}",
            selectorExpression = "${rocketmq.tag.cis-mall-order.after-sale-status-change}",
            consumerGroup = "${rocketmq.group.cis-mall-after-sale-status-change.name}",
            consumeThreadNumber = 5
    )
    public class MallAfterSaleStatusChangeMessageListener implements RocketMQListener<MallAfterSaleStatusChangeMessage> {
        @Override
        public void onMessage(MallAfterSaleStatusChangeMessage message) {
            log.info("Receive MallAfterSaleStatusChangeMessage Message:{}", cn.abcyun.cis.crm.common.JsonUtils.dump(message));
            if (Objects.isNull(message)) {
                return;
            }
            try {
                // 处理微商城售后订单信息
                patientFacade.handleMallAfterSaleStatusChangeMessage(message);
            } catch (Exception e) {
                log.error("Deal With MallAfterSaleStatusChangeMessage Error. ", e);
            }
        }
    }

}
